import { TweenService } from "@rbxts/services";

export function createKneeBendStance(character: Model): void {
    // Create Whitebeard-style knee bend stance for cross punch
    print("🦵 Creating knee bend stance like <PERSON><PERSON><PERSON>");

    const humanoid = character.FindFirstChild("Humanoid") as Humanoid;
    const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
    if (!humanoid || !humanoidRootPart) return;

    // Find leg joints for knee bending
    const leftHip = character.FindFirstChild("Left Hip") as Motor6D;
    const rightHip = character.FindFirstChild("Right Hip") as Motor6D;
    const leftKnee = character.FindFirstChild("Left Knee") as Motor6D;
    const rightKnee = character.FindFirstChild("Right Knee") as Motor6D;

    if (leftHip && rightHip && leftKnee && rightKnee) {
        // Store original positions
        const originalLeftHip = leftHip.C0;
        const originalRightHip = rightHip.C0;

        // Create knee bend animation (slight crouch like <PERSON><PERSON><PERSON>'s stance)
        const kneeBendTween = TweenService.Create(
            leftHip,
            new TweenInfo(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            { C0: originalLeftHip.mul(CFrame.Angles(math.rad(15), 0, 0)) }
        );

        const rightKneeBendTween = TweenService.Create(
            rightHip,
            new TweenInfo(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            { C0: originalRightHip.mul(CFrame.Angles(math.rad(15), 0, 0)) }
        );

        kneeBendTween.Play();
        rightKneeBendTween.Play();

        // Reset after animation
        task.delay(2, () => {
            const resetLeftTween = TweenService.Create(leftHip, new TweenInfo(0.5), { C0: originalLeftHip });
            const resetRightTween = TweenService.Create(rightHip, new TweenInfo(0.5), { C0: originalRightHip });
            resetLeftTween.Play();
            resetRightTween.Play();
        });
    }
}

export function createArmCrossingPose(character: Model): void {
    // Create Whitebeard-style arm crossing pose before punches
    print("💪 Creating arm crossing pose like Whitebeard");

    const leftShoulder = character.FindFirstChild("Left Shoulder") as Motor6D;
    const rightShoulder = character.FindFirstChild("Right Shoulder") as Motor6D;
    const leftElbow = character.FindFirstChild("Left Elbow") as Motor6D;
    const rightElbow = character.FindFirstChild("Right Elbow") as Motor6D;

    if (leftShoulder && rightShoulder && leftElbow && rightElbow) {
        // Store original positions
        const originalLeftShoulder = leftShoulder.C0;
        const originalRightShoulder = rightShoulder.C0;

        // Cross arms in front of body (Whitebeard style)
        const leftArmCrossTween = TweenService.Create(
            leftShoulder,
            new TweenInfo(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            { C0: originalLeftShoulder.mul(CFrame.Angles(math.rad(-45), math.rad(30), math.rad(-20))) }
        );

        const rightArmCrossTween = TweenService.Create(
            rightShoulder,
            new TweenInfo(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            { C0: originalRightShoulder.mul(CFrame.Angles(math.rad(-45), math.rad(-30), math.rad(20))) }
        );

        leftArmCrossTween.Play();
        rightArmCrossTween.Play();

        // Reset arms after punches (2s delay)
        task.delay(2, () => {
            const resetLeftTween = TweenService.Create(leftShoulder, new TweenInfo(0.5), { C0: originalLeftShoulder });
            const resetRightTween = TweenService.Create(rightShoulder, new TweenInfo(0.5), { C0: originalRightShoulder });
            resetLeftTween.Play();
            resetRightTween.Play();
        });
    }
}
