export type WeatherType = 
    | "clear"
    | "rain" 
    | "heavy_rain"
    | "snow"
    | "blizzard"
    | "storm"
    | "thunderstorm"
    | "fog"
    | "sandstorm";

export interface WeatherOptions {
    type: WeatherType;
    intensity?: number; // 0-1, affects rain/snow/fog density
    duration?: number; // seconds, undefined = permanent until changed
    windSpeed?: number; // 0-100, affects particle movement
    temperature?: number; // affects particle behavior
}
