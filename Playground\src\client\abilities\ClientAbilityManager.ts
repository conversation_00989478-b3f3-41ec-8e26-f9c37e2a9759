import { AbilityBase } from "./AbilityBase";
import { RoomAbility } from "./RoomAbility";
import { QuakeAbility } from "../abilities/whitebeard/QuakeAbility";
import { HakiDominanceAbility } from "./HakiDominanceAbility";
import { IceAgeAbility } from "./IceAgeAbility";
import { FireFistAbility } from "./FireFistAbility";
import { ThreeSwordStyleAbility } from "./ThreeSwordStyleAbility";

export class ClientAbilityManager {
    private abilities: Map<string, AbilityBase> = new Map();
    
    constructor() {
        this.initializeAbilities();
    }
    
    private initializeAbilities(): void {
        this.registerAbility(new RoomAbility());
        this.registerAbility(new QuakeAbility());
        this.registerAbility(new HakiDominanceAbility());
        this.registerAbility(new IceAgeAbility());
        this.registerAbility(new FireFistAbility());
        this.registerAbility(new ThreeSwordStyleAbility());
    }
    
    private registerAbility(ability: AbilityBase): void {
        this.abilities.set(ability.getName(), ability);
    }
    
    public activateAbility(name: string): void {
        const ability = this.abilities.get(name);
        if (ability) {
            ability.activate();
        }
    }
}


