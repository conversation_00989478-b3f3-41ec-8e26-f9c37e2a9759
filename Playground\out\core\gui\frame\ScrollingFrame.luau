-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local BORDER_RADIUS = _design.BORDER_RADIUS
local function ScrollingFrame(props)
	local size = props.size or UDim2.new(1, 0, 1, 0)
	local canvasSize = props.canvasSize or UDim2.new(0, 0, 0, 0)
	local _condition = props.scrollBarThickness
	if _condition == nil then
		_condition = 6
	end
	local scrollBarThickness = _condition
	local _condition_1 = props.backgroundColor
	if _condition_1 == nil then
		_condition_1 = COLORS.bg.base
	end
	local backgroundColor = _condition_1
	local _condition_2 = props.backgroundTransparency
	if _condition_2 == nil then
		_condition_2 = 0
	end
	local backgroundTransparency = _condition_2
	local _condition_3 = props.borderColor
	if _condition_3 == nil then
		_condition_3 = COLORS.border.l2
	end
	local borderColor = _condition_3
	local _condition_4 = props.borderThickness
	if _condition_4 == nil then
		_condition_4 = 1
	end
	local borderThickness = _condition_4
	local _condition_5 = props.cornerRadius
	if _condition_5 == nil then
		_condition_5 = BORDER_RADIUS.md
	end
	local cornerRadius = _condition_5
	local scrollingDirection = props.scrollingDirection or Enum.ScrollingDirection.XY
	local elasticBehavior = props.elasticBehavior or Enum.ElasticBehavior.WhenScrollable
	local automaticCanvasSize = props.automaticCanvasSize or Enum.AutomaticSize.Y
	return React.createElement("scrollingframe", {
		Size = size,
		Position = props.position,
		AnchorPoint = props.anchorPoint,
		LayoutOrder = props.layoutOrder,
		CanvasSize = canvasSize,
		ScrollBarThickness = scrollBarThickness,
		BackgroundColor3 = Color3.fromHex(backgroundColor),
		BackgroundTransparency = backgroundTransparency,
		ScrollBarImageColor3 = Color3.fromHex(COLORS.border.l3),
		BorderSizePixel = 0,
		ScrollingDirection = scrollingDirection,
		ElasticBehavior = elasticBehavior,
		AutomaticCanvasSize = automaticCanvasSize,
		ScrollingEnabled = true,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, cornerRadius),
	}), borderThickness > 0 and (React.createElement("uistroke", {
		Color = Color3.fromHex(borderColor),
		Thickness = borderThickness,
	})), props.children)
end
return {
	ScrollingFrame = ScrollingFrame,
}
