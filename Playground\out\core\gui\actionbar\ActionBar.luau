-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local HorizontalFrame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame").HorizontalFrame
local AbilitySlot = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "actionbar", "AbilitySlot").AbilitySlot
local function ActionBar(props)
	local slotSize = 60
	local slotSpacing = 8
	local _exp = props.slots
	-- ▼ ReadonlyArray.map ▼
	local _newValue = table.create(#_exp)
	local _callback = function(slot, index)
		return React.createElement(AbilitySlot, {
			key = `slot-{index}`,
			slotNumber = index + 1,
			abilityData = slot,
			onClick = function()
				return props.onSlotClick(index, slot.id)
			end,
			layoutOrder = index,
			size = UDim2.new(0, slotSize, 0, slotSize),
		})
	end
	for _k, _v in _exp do
		_newValue[_k] = _callback(_v, _k - 1, _exp)
	end
	-- ▲ ReadonlyArray.map ▲
	return React.createElement(HorizontalFrame, {
		fitContent = true,
		backgroundTransparency = 1,
		position = props.position or UDim2.new(0.5, 0, 1, -80),
		anchorPoint = props.anchorPoint or Vector2.new(0.5, 0),
		layoutOrder = props.layoutOrder,
		spacing = slotSpacing,
		padding = 8,
		horizontalAlignment = Enum.HorizontalAlignment.Center,
		verticalAlignment = Enum.VerticalAlignment.Center,
		zIndex = props.zIndex,
	}, _newValue)
end
return {
	ActionBar = ActionBar,
}
