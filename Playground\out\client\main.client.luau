-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local createRoot = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react-roblox").createRoot
local Players = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Players
local BottomLeftGrid = TS.import(script, script.Parent, "gui", "BottomLeftGrid").BottomLeftGrid
local ActionBarDemo = TS.import(script, script.Parent, "gui", "ActionBarDemo").ActionBarDemo
local MovementExample = TS.import(script, script.Parent, "movement", "MovementExample").MovementExample
local initializeDebugSystem = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug").initializeDebugSystem
local version = "v1.3.5"
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
-- Create a ScreenGui with properties to make it visible
local screenGui = Instance.new("ScreenGui", playerGui)
screenGui.ResetOnSpawn = false
screenGui.IgnoreGuiInset = true
screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
local root = createRoot(screenGui)
-- Render the GUI components
root:render(React.createElement(React.Fragment, nil, React.createElement(BottomLeftGrid, {
	onTestClick = function()
		return print("Test button clicked!")
	end,
	onHelloClick = function()
		return print("Hello button clicked!")
	end,
}), React.createElement(ActionBarDemo)))
-- Initialize movement example for testing
MovementExample.new()
-- Initialize debug system for development
initializeDebugSystem()
print(`🔥 Playground client loaded! [{version}]`)
print(`🎙️ Voice system available! Use voiceDemo methods in console`)
