import { Debug<PERSON>enderer } from "./DebugRenderer";
import { DebugConfig } from "./DebugManager";
export declare class AIDebugger {
    private renderer;
    private aiInfoLabel?;
    constructor(renderer: DebugRenderer);
    update(_config: DebugConfig): void;
    private setupGUI;
    private updateAIInfo;
    private debugAIAgent;
    private debugAIPaths;
    private debugAITarget;
    private debugBlackboard;
    private getStateColor;
    private getEntityLookDirection;
    private getPrivateField;
    private callPrivateMethod;
    cleanup(): void;
}
