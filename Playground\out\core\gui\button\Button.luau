-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local TYPOGRAPHY = _design.TYPOGRAPHY
local BORDER_RADIUS = _design.BORDER_RADIUS
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local function Button(props)
	local hovered, setHovered = React.useState(false)
	-- Get the appropriate color based on state
	local bgColorHSL = if props.disabled then COLORS.bg.secondary elseif hovered then COLORS.bg["surface-hover"] else COLORS.bg.surface
	local textColorHSL = if props.disabled then COLORS.text.secondary else COLORS.text.main
	-- Responsive manager for dynamic sizing
	local responsiveManager = ResponsiveManager:getInstance()
	-- Smart sizing: use autoSize if specified or no size provided
	local _condition = props.autoSize
	if _condition == nil then
		_condition = (props.size == nil)
	end
	local useAutoSize = _condition
	-- Calculate responsive dimensions
	local _condition_1 = props.minWidth
	if _condition_1 == nil then
		_condition_1 = SIZES.button.width
	end
	local minWidth = _condition_1
	local buttonHeight = SIZES.button.height
	local _condition_2 = props.padding
	if _condition_2 == nil then
		_condition_2 = SIZES.padding
	end
	local padding = _condition_2
	if props.responsive then
		local deviceType = responsiveManager:getDeviceType()
		local scaleFactor = if deviceType == "mobile" then 0.9 elseif deviceType == "tablet" then 0.95 else 1.0
		minWidth = minWidth * scaleFactor
		buttonHeight = buttonHeight * scaleFactor
		padding = responsiveManager:getResponsiveMargin(padding)
	end
	-- Calculate size based on autoSize setting
	local size = props.size or (if useAutoSize then UDim2.new(0, minWidth, 0, buttonHeight) else UDim2.new(0, minWidth, 0, buttonHeight))
	return React.createElement("textbutton", {
		Text = props.text,
		TextColor3 = Color3.fromHex(textColorHSL),
		BackgroundColor3 = Color3.fromHex(bgColorHSL),
		Size = size,
		LayoutOrder = props.LayoutOrder,
		Font = TYPOGRAPHY.font,
		TextSize = SIZES.fontSize,
		AutoButtonColor = false,
		AutomaticSize = if useAutoSize then Enum.AutomaticSize.X else Enum.AutomaticSize.None,
		Event = {
			Activated = props.onClick,
			MouseEnter = function()
				return not props.disabled and setHovered(true)
			end,
			MouseLeave = function()
				return setHovered(false)
			end,
		},
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.border.l2),
		Thickness = 1,
		Transparency = if props.disabled then 0.5 else 0,
	}), useAutoSize and (React.createElement("uipadding", {
		PaddingLeft = UDim.new(0, padding),
		PaddingRight = UDim.new(0, padding),
	})))
end
return {
	Button = Button,
}
