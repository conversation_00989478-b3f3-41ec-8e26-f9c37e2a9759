import { AbilityBase } from "./AbilityBase";
import { TweenService, RunService, Workspace, Players, Lighting } from "@rbxts/services";

export class HakiDominanceAbility extends AbilityBase {
    private dominanceEffect?: Part;
    private lightningEffects: Part[] = [];
    private dominanceConnection?: RBXScriptConnection;
    private cooldownEndTime = 0;
    private isActive = false;

    constructor() {
        super("HAKI_DOMINANCE", 25);
    }

    public isOnCooldown(): boolean {
        return tick() < this.cooldownEndTime;
    }

    private startCooldown(): void {
        this.cooldownEndTime = tick() + this.getCooldownTime();
    }

    public activate(): void {
        if (this.isOnCooldown() || this.isActive) return;

        const player = Players.LocalPlayer;
        const character = player.Character;
        if (!character) return;

        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (!humanoidRootPart) return;

        print("🔴 Activating <PERSON><PERSON>'s Haki Dominance");
        this.isActive = true;

        // Phase 1: Character Haki preparation animation
        this.createHakiDominanceAnimation(character);

        // Phase 2: Create overwhelming red atmosphere
        this.createRedAtmosphere();

        // Phase 2: Create map-wide red lightning effects
        this.createMapWideLightning();

        // Phase 3: Create camera shake and pressure effect
        this.createPressureEffect();

        // Phase 4: Create character aura
        this.createCharacterAura(character);

        // Phase 5: Create red glass sparkle effects
        this.createRedGlassSparkles();

        // Phase 6: Create floating glass shards
        this.createFloatingGlassShards();

        // Duration: 8 seconds
        task.delay(8, () => {
            this.cleanupEffects();
            this.isActive = false;
        });

        this.startCooldown();
    }

    private createHakiDominanceAnimation(character: Model): void {
        print("🔴 Creating Rayleigh's Haki Dominance animation");

        const humanoid = character.FindFirstChild("Humanoid") as Humanoid;
        if (!humanoid) return;

        // Create proper Motor6D animation like QuakeAbility
        this.createCharacterAnimation(character);
    }

    private createCharacterAnimation(character: Model): void {
        const torso = character.FindFirstChild("Torso") as Part;
        const upperTorso = character.FindFirstChild("UpperTorso") as Part;

        if (torso) {
            // R6 character
            this.animateR6Character(torso);
        } else if (upperTorso) {
            // R15 character
            this.animateR15Character(upperTorso);
        }
    }

    private animateR6Character(torso: Part): void {
        const rightShoulder = torso.FindFirstChild("Right Shoulder") as Motor6D;
        const leftShoulder = torso.FindFirstChild("Left Shoulder") as Motor6D;
        const rightHip = torso.FindFirstChild("Right Hip") as Motor6D;
        const leftHip = torso.FindFirstChild("Left Hip") as Motor6D;

        if (!rightShoulder || !leftShoulder) return;

        // Store original C0 values
        const originalRightC0 = rightShoulder.C0;
        const originalLeftC0 = leftShoulder.C0;
        const originalRightHipC0 = rightHip ? rightHip.C0 : undefined;
        const originalLeftHipC0 = leftHip ? leftHip.C0 : undefined;

        // Phase 1: Concentration stance (0-1s) - Arms at sides, focusing
        print("🔴 Haki concentration stance");

        const rightArmConcentrate = TweenService.Create(rightShoulder, new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
            C0: originalRightC0.mul(CFrame.Angles(math.pi/12, 0, -math.pi/12))
        });
        const leftArmConcentrate = TweenService.Create(leftShoulder, new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
            C0: originalLeftC0.mul(CFrame.Angles(math.pi/12, 0, math.pi/12))
        });

        rightArmConcentrate.Play();
        leftArmConcentrate.Play();

        // Stable, powerful stance
        if (rightHip && leftHip && originalRightHipC0 && originalLeftHipC0) {
            const rightLegStance = TweenService.Create(rightHip, new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                C0: originalRightHipC0.mul(CFrame.Angles(0, 0, math.pi/24))
            });
            const leftLegStance = TweenService.Create(leftHip, new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                C0: originalLeftHipC0.mul(CFrame.Angles(0, 0, -math.pi/24))
            });

            rightLegStance.Play();
            leftLegStance.Play();
        }

        // Phase 2: Haki buildup (1-2s) - Arms slightly raised, power building
        task.delay(1, () => {
            print("🔴 Haki power buildup");

            const rightArmBuildup = TweenService.Create(rightShoulder, new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                C0: originalRightC0.mul(CFrame.Angles(-math.pi/8, 0, -math.pi/6))
            });
            const leftArmBuildup = TweenService.Create(leftShoulder, new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                C0: originalLeftC0.mul(CFrame.Angles(-math.pi/8, 0, math.pi/6))
            });

            rightArmBuildup.Play();
            leftArmBuildup.Play();
        });

        // Phase 3: Haki release (2-3s) - Dramatic power release pose
        task.delay(2, () => {
            print("💥 Haki power release!");

            const rightArmRelease = TweenService.Create(rightShoulder, new TweenInfo(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
                C0: originalRightC0.mul(CFrame.Angles(-math.pi/4, 0, -math.pi/3))
            });
            const leftArmRelease = TweenService.Create(leftShoulder, new TweenInfo(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
                C0: originalLeftC0.mul(CFrame.Angles(-math.pi/4, 0, math.pi/3))
            });

            rightArmRelease.Play();
            leftArmRelease.Play();
        });

        // Phase 4: Dominance pose (3-6s) - Intimidating stance
        task.delay(3, () => {
            print("🔴 Haki dominance pose");

            const rightArmDominance = TweenService.Create(rightShoulder, new TweenInfo(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                C0: originalRightC0.mul(CFrame.Angles(-math.pi/6, 0, -math.pi/4))
            });
            const leftArmDominance = TweenService.Create(leftShoulder, new TweenInfo(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                C0: originalLeftC0.mul(CFrame.Angles(-math.pi/6, 0, math.pi/4))
            });

            rightArmDominance.Play();
            leftArmDominance.Play();

            // Wide, powerful stance
            if (rightHip && leftHip && originalRightHipC0 && originalLeftHipC0) {
                const rightLegDominance = TweenService.Create(rightHip, new TweenInfo(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                    C0: originalRightHipC0.mul(CFrame.Angles(0, 0, math.pi/12))
                });
                const leftLegDominance = TweenService.Create(leftHip, new TweenInfo(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                    C0: originalLeftHipC0.mul(CFrame.Angles(0, 0, -math.pi/12))
                });

                rightLegDominance.Play();
                leftLegDominance.Play();
            }
        });

        // Phase 5: Return to normal (6-8s)
        task.delay(6, () => {
            print("🔴 Returning to normal stance");

            const rightArmReturn = TweenService.Create(rightShoulder, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                C0: originalRightC0
            });
            const leftArmReturn = TweenService.Create(leftShoulder, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                C0: originalLeftC0
            });

            rightArmReturn.Play();
            leftArmReturn.Play();

            // Restore legs
            if (rightHip && leftHip && originalRightHipC0 && originalLeftHipC0) {
                const rightLegReturn = TweenService.Create(rightHip, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                    C0: originalRightHipC0
                });
                const leftLegReturn = TweenService.Create(leftHip, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                    C0: originalLeftHipC0
                });

                rightLegReturn.Play();
                leftLegReturn.Play();
            }
        });
    }

    private animateR15Character(upperTorso: Part): void {
        const rightShoulder = upperTorso.FindFirstChild("RightShoulder") as Motor6D;
        const leftShoulder = upperTorso.FindFirstChild("LeftShoulder") as Motor6D;

        if (!rightShoulder || !leftShoulder) return;

        // Store original C0 values
        const originalRightC0 = rightShoulder.C0;
        const originalLeftC0 = leftShoulder.C0;

        // Similar animation sequence for R15
        print("🔴 R15 Haki Dominance animation");

        // Phase 1: Concentration
        const rightArmConcentrate = TweenService.Create(rightShoulder, new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
            C0: originalRightC0.mul(CFrame.Angles(math.pi/12, 0, -math.pi/12))
        });
        const leftArmConcentrate = TweenService.Create(leftShoulder, new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
            C0: originalLeftC0.mul(CFrame.Angles(math.pi/12, 0, math.pi/12))
        });

        rightArmConcentrate.Play();
        leftArmConcentrate.Play();

        // Continue with similar phases...
        task.delay(1, () => {
            const rightArmBuildup = TweenService.Create(rightShoulder, new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                C0: originalRightC0.mul(CFrame.Angles(-math.pi/8, 0, -math.pi/6))
            });
            const leftArmBuildup = TweenService.Create(leftShoulder, new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                C0: originalLeftC0.mul(CFrame.Angles(-math.pi/8, 0, math.pi/6))
            });

            rightArmBuildup.Play();
            leftArmBuildup.Play();
        });

        // Return to normal (6s delay)
        task.delay(6, () => {
            const rightArmReturn = TweenService.Create(rightShoulder, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                C0: originalRightC0
            });
            const leftArmReturn = TweenService.Create(leftShoulder, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                C0: originalLeftC0
            });

            rightArmReturn.Play();
            leftArmReturn.Play();
        });
    }

    private createRedAtmosphere(): void {
        print("🔴 Creating red atmospheric dominance");

        // Create red fog/atmosphere effect
        const atmosphere = Lighting.FindFirstChild("Atmosphere") as Atmosphere;
        if (atmosphere) {
            const originalColor = atmosphere.Color;
            const originalHaze = atmosphere.Haze;
            const originalGlare = atmosphere.Glare;

            // Tween to red dominance atmosphere
            const atmosphereTween = TweenService.Create(
                atmosphere,
                new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                {
                    Color: Color3.fromRGB(150, 0, 0), // Deep red
                    Haze: 2,
                    Glare: 1
                }
            );
            atmosphereTween.Play();

            // Restore after duration
            task.delay(7, () => {
                const restoreTween = TweenService.Create(
                    atmosphere,
                    new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                    {
                        Color: originalColor,
                        Haze: originalHaze,
                        Glare: originalGlare
                    }
                );
                restoreTween.Play();
            });
        }

        // Create red lighting effect
        const originalAmbient = Lighting.Ambient;
        const originalOutdoorAmbient = Lighting.OutdoorAmbient;

        const lightingTween = TweenService.Create(
            Lighting,
            new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            {
                Ambient: Color3.fromRGB(50, 0, 0), // Red ambient
                OutdoorAmbient: Color3.fromRGB(80, 0, 0) // Red outdoor ambient
            }
        );
        lightingTween.Play();

        // Restore lighting after duration
        task.delay(7, () => {
            const restoreLightingTween = TweenService.Create(
                Lighting,
                new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                {
                    Ambient: originalAmbient,
                    OutdoorAmbient: originalOutdoorAmbient
                }
            );
            restoreLightingTween.Play();
        });
    }

    private createMapWideLightning(): void {
        print("⚡ Creating map-wide red lightning effects");

        // Create multiple lightning strikes across the map
        for (let i = 0; i < 15; i++) {
            task.delay(i * 0.3, () => {
                this.createRedLightningStrike();
            });
        }

        // Create continuous lightning for duration
        this.dominanceConnection = RunService.Heartbeat.Connect(() => {
            if (math.random() < 0.3) { // 30% chance each frame
                this.createRedLightningStrike();
            }
        });

        // Stop continuous lightning after 7 seconds
        task.delay(7, () => {
            if (this.dominanceConnection) {
                this.dominanceConnection.Disconnect();
                this.dominanceConnection = undefined;
            }
        });
    }

    private createRedLightningStrike(): void {
        // Random position across the map
        const randomX = math.random(-200, 200);
        const randomZ = math.random(-200, 200);
        const startY = 100;
        const endY = 5;

        const startPosition = new Vector3(randomX, startY, randomZ);
        const endPosition = new Vector3(randomX + math.random(-10, 10), endY, randomZ + math.random(-10, 10));

        // Create lightning bolt
        const lightning = new Instance("Part");
        lightning.Name = "HakiLightning";
        lightning.Size = new Vector3(0.5, (startY - endY), 0.5);
        lightning.Color = Color3.fromRGB(255, 0, 0); // Bright red
        lightning.Material = Enum.Material.Neon;
        lightning.Transparency = 0.2;
        lightning.CanCollide = false;
        lightning.Anchored = true;
        lightning.Shape = Enum.PartType.Cylinder;

        // Position between start and end
        const midPosition = startPosition.add(endPosition).div(2);
        lightning.Position = midPosition;

        // Rotate to point from start to end
        const direction = endPosition.sub(startPosition).Unit;
        lightning.CFrame = CFrame.lookAt(midPosition, midPosition.add(direction));

        lightning.Parent = Workspace;
        this.lightningEffects.push(lightning);

        // Add glow effect
        const pointLight = new Instance("PointLight");
        pointLight.Color = Color3.fromRGB(255, 0, 0);
        pointLight.Brightness = 3;
        pointLight.Range = 20;
        pointLight.Parent = lightning;

        // Flicker effect
        const flickerTween = TweenService.Create(
            lightning,
            new TweenInfo(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut, -1, true),
            { Transparency: 0.8 }
        );
        flickerTween.Play();

        // Remove after short duration
        task.delay(0.5, () => {
            flickerTween.Cancel();
            const fadeTween = TweenService.Create(
                lightning,
                new TweenInfo(0.3, Enum.EasingStyle.Quad),
                { Transparency: 1 }
            );
            fadeTween.Play();
            fadeTween.Completed.Connect(() => {
                lightning.Destroy();
                const index = this.lightningEffects.indexOf(lightning);
                if (index > -1) {
                    this.lightningEffects.remove(index);
                }
            });
        });
    }

    private createPressureEffect(): void {
        print("💥 Creating Haki pressure effect");

        // Create camera shake
        const camera = Workspace.CurrentCamera;
        if (!camera) return;

        const originalCFrame = camera.CFrame;
        let shakeTime = 0;
        const shakeDuration = 8;

        const shakeConnection = RunService.RenderStepped.Connect((deltaTime) => {
            shakeTime += deltaTime;
            
            if (shakeTime >= shakeDuration) {
                camera.CFrame = originalCFrame;
                shakeConnection.Disconnect();
                return;
            }

            // Intense shaking that gradually reduces
            const intensity = 3 * (1 - shakeTime / shakeDuration);
            const randomOffset = new Vector3(
                (math.random() - 0.5) * intensity,
                (math.random() - 0.5) * intensity,
                (math.random() - 0.5) * intensity
            );

            camera.CFrame = originalCFrame.add(randomOffset);
        });
    }

    private createCharacterAura(character: Model): void {
        print("🔴 Creating character Haki aura");

        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (!humanoidRootPart) return;

        // Create red aura sphere around character
        const aura = new Instance("Part");
        aura.Name = "HakiAura";
        aura.Shape = Enum.PartType.Ball;
        aura.Size = new Vector3(15, 15, 15);
        aura.Color = Color3.fromRGB(255, 0, 0);
        aura.Material = Enum.Material.ForceField;
        aura.Transparency = 0.7;
        aura.CanCollide = false;
        aura.Anchored = true;
        aura.Position = humanoidRootPart.Position;
        aura.Parent = Workspace;

        // Add intense red light
        const auraLight = new Instance("PointLight");
        auraLight.Color = Color3.fromRGB(255, 0, 0);
        auraLight.Brightness = 5;
        auraLight.Range = 30;
        auraLight.Parent = aura;

        // Pulsing effect
        const pulseTween = TweenService.Create(
            aura,
            new TweenInfo(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
            {
                Size: new Vector3(18, 18, 18),
                Transparency: 0.5
            }
        );
        pulseTween.Play();

        // Create orbiting glass sparkles around the character
        this.createOrbitingSparkles(humanoidRootPart);

        // Follow character
        const followConnection = RunService.Heartbeat.Connect(() => {
            if (humanoidRootPart.Parent && aura.Parent) {
                aura.Position = humanoidRootPart.Position;
            }
        });

        // Cleanup after duration
        task.delay(8, () => {
            followConnection.Disconnect();
            pulseTween.Cancel();
            
            const fadeTween = TweenService.Create(
                aura,
                new TweenInfo(1, Enum.EasingStyle.Quad),
                { Transparency: 1 }
            );
            fadeTween.Play();
            fadeTween.Completed.Connect(() => aura.Destroy());
        });
    }

    private cleanupEffects(): void {
        print("🧹 Cleaning up Haki Dominance effects");

        // Clean up any remaining lightning effects
        for (const lightning of this.lightningEffects) {
            if (lightning.Parent) {
                lightning.Destroy();
            }
        }
        this.lightningEffects = [];

        // Disconnect any remaining connections
        if (this.dominanceConnection) {
            this.dominanceConnection.Disconnect();
            this.dominanceConnection = undefined;
        }
    }

    private createRedGlassSparkles(): void {
        print("✨ Creating red glass sparkle effects");

        // Create sparkles around the character and across the map
        for (let i = 0; i < 50; i++) {
            task.delay(i * 0.1, () => {
                this.createSingleGlassSparkle();
            });
        }

        // Create continuous sparkles during the ability duration
        const sparkleConnection = RunService.Heartbeat.Connect(() => {
            if (math.random() < 0.8) { // 80% chance each frame for intense sparkle effect
                this.createSingleGlassSparkle();
            }
        });

        // Stop sparkles after 7 seconds
        task.delay(7, () => {
            sparkleConnection.Disconnect();
        });
    }

    private createSingleGlassSparkle(): void {
        const player = Players.LocalPlayer;
        const character = player.Character;
        if (!character) return;

        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (!humanoidRootPart) return;

        // Random position around the character and across the map
        const isNearCharacter = math.random() < 0.6; // 60% chance to spawn near character
        let sparklePosition: Vector3;

        if (isNearCharacter) {
            // Sparkles around character (within 30 studs)
            const randomOffset = new Vector3(
                math.random(-30, 30),
                math.random(-10, 20),
                math.random(-30, 30)
            );
            sparklePosition = humanoidRootPart.Position.add(randomOffset);
        } else {
            // Sparkles across the map
            sparklePosition = new Vector3(
                math.random(-150, 150),
                math.random(5, 80),
                math.random(-150, 150)
            );
        }

        // Create sharp glass sparkle
        const sparkle = new Instance("Part");
        sparkle.Name = "HakiGlassSparkle";
        sparkle.Shape = Enum.PartType.Block;
        sparkle.Size = new Vector3(0.3, 0.3, 0.3);
        sparkle.Color = Color3.fromRGB(255, 50, 50); // Bright red
        sparkle.Material = Enum.Material.Glass;
        sparkle.Transparency = 0.1;
        sparkle.CanCollide = false;
        sparkle.Anchored = true;
        sparkle.Position = sparklePosition;

        // Random rotation for sharp glass effect
        sparkle.CFrame = sparkle.CFrame.mul(CFrame.Angles(
            math.random() * math.pi * 2,
            math.random() * math.pi * 2,
            math.random() * math.pi * 2
        ));

        sparkle.Parent = Workspace;
        this.lightningEffects.push(sparkle);

        // Add intense red light
        const sparkleLight = new Instance("PointLight");
        sparkleLight.Color = Color3.fromRGB(255, 0, 0);
        sparkleLight.Brightness = 4;
        sparkleLight.Range = 8;
        sparkleLight.Parent = sparkle;

        // Create sparkle animation - rapid spinning and scaling
        const spinTween = TweenService.Create(
            sparkle,
            new TweenInfo(0.5, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1),
            {
                CFrame: sparkle.CFrame.mul(CFrame.Angles(math.pi * 4, math.pi * 4, math.pi * 4))
            }
        );
        spinTween.Play();

        // Pulsing scale effect
        const scaleTween = TweenService.Create(
            sparkle,
            new TweenInfo(0.3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
            {
                Size: new Vector3(0.6, 0.6, 0.6)
            }
        );
        scaleTween.Play();

        // Flickering transparency
        const flickerTween = TweenService.Create(
            sparkle,
            new TweenInfo(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut, -1, true),
            { Transparency: 0.5 }
        );
        flickerTween.Play();

        // Remove after random duration
        const lifetime = math.random(1, 3);
        task.delay(lifetime, () => {
            spinTween.Cancel();
            scaleTween.Cancel();
            flickerTween.Cancel();

            const fadeTween = TweenService.Create(
                sparkle,
                new TweenInfo(0.5, Enum.EasingStyle.Quad),
                {
                    Transparency: 1,
                    Size: new Vector3(0.1, 0.1, 0.1)
                }
            );
            fadeTween.Play();
            fadeTween.Completed.Connect(() => {
                sparkle.Destroy();
                const index = this.lightningEffects.indexOf(sparkle);
                if (index > -1) {
                    this.lightningEffects.remove(index);
                }
            });
        });
    }

    private createFloatingGlassShards(): void {
        print("🔴 Creating floating red glass shards");

        const player = Players.LocalPlayer;
        const character = player.Character;
        if (!character) return;

        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (!humanoidRootPart) return;

        // Create larger floating glass shards around the character
        for (let i = 0; i < 15; i++) {
            task.delay(i * 0.2, () => {
                this.createFloatingGlassShard(humanoidRootPart.Position);
            });
        }
    }

    private createFloatingGlassShard(centerPosition: Vector3): void {
        // Random position in a circle around the character
        const angle = math.random() * math.pi * 2;
        const distance = math.random(10, 25);
        const height = math.random(5, 15);

        const shardPosition = centerPosition.add(new Vector3(
            math.cos(angle) * distance,
            height,
            math.sin(angle) * distance
        ));

        // Create sharp glass shard
        const shard = new Instance("Part");
        shard.Name = "HakiGlassShard";
        shard.Shape = Enum.PartType.Block;
        shard.Size = new Vector3(
            math.random(0.5, 1.5),
            math.random(2, 4),
            math.random(0.3, 0.8)
        );
        shard.Color = Color3.fromRGB(200, 0, 0); // Dark red glass
        shard.Material = Enum.Material.Glass;
        shard.Transparency = 0.2;
        shard.CanCollide = false;
        shard.Anchored = true;
        shard.Position = shardPosition;

        // Random sharp rotation
        shard.CFrame = shard.CFrame.mul(CFrame.Angles(
            math.random() * math.pi,
            math.random() * math.pi * 2,
            math.random() * math.pi
        ));

        shard.Parent = Workspace;
        this.lightningEffects.push(shard);

        // Add red glow
        const shardLight = new Instance("PointLight");
        shardLight.Color = Color3.fromRGB(255, 0, 0);
        shardLight.Brightness = 2;
        shardLight.Range = 12;
        shardLight.Parent = shard;

        // Floating animation - slow rotation and gentle bobbing
        const floatTween = TweenService.Create(
            shard,
            new TweenInfo(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
            {
                Position: shardPosition.add(new Vector3(0, 2, 0))
            }
        );
        floatTween.Play();

        const rotateTween = TweenService.Create(
            shard,
            new TweenInfo(4, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1),
            {
                CFrame: shard.CFrame.mul(CFrame.Angles(0, math.pi * 2, 0))
            }
        );
        rotateTween.Play();

        // Subtle pulsing glow
        const glowTween = TweenService.Create(
            shardLight,
            new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
            { Brightness: 3 }
        );
        glowTween.Play();

        // Remove after ability duration
        task.delay(8, () => {
            floatTween.Cancel();
            rotateTween.Cancel();
            glowTween.Cancel();

            const fadeTween = TweenService.Create(
                shard,
                new TweenInfo(1, Enum.EasingStyle.Quad),
                {
                    Transparency: 1,
                    Size: new Vector3(0.1, 0.1, 0.1)
                }
            );
            fadeTween.Play();
            fadeTween.Completed.Connect(() => {
                shard.Destroy();
                const index = this.lightningEffects.indexOf(shard);
                if (index > -1) {
                    this.lightningEffects.remove(index);
                }
            });
        });
    }

    private createOrbitingSparkles(humanoidRootPart: Part): void {
        print("✨ Creating orbiting glass sparkles around character");

        // Create 8 orbiting sparkles
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * math.pi * 2;
            this.createOrbitingSparkle(humanoidRootPart, angle, i);
        }
    }

    private createOrbitingSparkle(humanoidRootPart: Part, startAngle: number, index: number): void {
        const orbitRadius = 8;
        const orbitHeight = 3;

        // Create orbiting sparkle
        const sparkle = new Instance("Part");
        sparkle.Name = "OrbitingHakiSparkle";
        sparkle.Shape = Enum.PartType.Block;
        sparkle.Size = new Vector3(0.4, 0.4, 0.4);
        sparkle.Color = Color3.fromRGB(255, 100, 100); // Bright red
        sparkle.Material = Enum.Material.Glass;
        sparkle.Transparency = 0.2;
        sparkle.CanCollide = false;
        sparkle.Anchored = true;
        sparkle.Parent = Workspace;
        this.lightningEffects.push(sparkle);

        // Add bright light
        const sparkleLight = new Instance("PointLight");
        sparkleLight.Color = Color3.fromRGB(255, 0, 0);
        sparkleLight.Brightness = 3;
        sparkleLight.Range = 6;
        sparkleLight.Parent = sparkle;

        // Orbiting animation
        let currentAngle = startAngle;
        const orbitSpeed = 2; // Radians per second

        const orbitConnection = RunService.Heartbeat.Connect((deltaTime) => {
            if (!humanoidRootPart.Parent || !sparkle.Parent) {
                orbitConnection.Disconnect();
                return;
            }

            currentAngle += orbitSpeed * deltaTime;

            const orbitPosition = humanoidRootPart.Position.add(new Vector3(
                math.cos(currentAngle) * orbitRadius,
                orbitHeight + math.sin(currentAngle * 3) * 1, // Vertical bobbing
                math.sin(currentAngle) * orbitRadius
            ));

            sparkle.Position = orbitPosition;

            // Rotate the sparkle itself for extra sparkle effect
            sparkle.CFrame = CFrame.lookAt(orbitPosition, humanoidRootPart.Position).mul(
                CFrame.Angles(currentAngle * 2, currentAngle * 3, currentAngle * 1.5)
            );
        });

        // Pulsing scale effect
        const scaleTween = TweenService.Create(
            sparkle,
            new TweenInfo(0.4, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
            {
                Size: new Vector3(0.7, 0.7, 0.7)
            }
        );
        scaleTween.Play();

        // Cleanup after duration
        task.delay(8, () => {
            orbitConnection.Disconnect();
            scaleTween.Cancel();

            const fadeTween = TweenService.Create(
                sparkle,
                new TweenInfo(0.5, Enum.EasingStyle.Quad),
                {
                    Transparency: 1,
                    Size: new Vector3(0.1, 0.1, 0.1)
                }
            );
            fadeTween.Play();
            fadeTween.Completed.Connect(() => {
                sparkle.Destroy();
                const index = this.lightningEffects.indexOf(sparkle);
                if (index > -1) {
                    this.lightningEffects.remove(index);
                }
            });
        });
    }
}
