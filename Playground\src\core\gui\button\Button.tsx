import * as React from "@rbxts/react";
import { COLORS, SIZES, TYPOGRAPHY, BORDER_RADIUS } from "../../design";
import { ResponsiveManager } from "../layout/ResponsiveManager";

interface ButtonProps {
  text: string;
  onClick: () => void;
  disabled?: boolean;
  LayoutOrder?: number;
  size?: UDim2;
  autoSize?: boolean; // When true, button sizes to fit text content
  minWidth?: number; // Minimum width when using autoSize
  padding?: number; // Custom padding override
  responsive?: boolean; // Enable responsive sizing
}

export function Button(props: ButtonProps) {
  const [hovered, setHovered] = React.useState(false);

  // Get the appropriate color based on state
  const bgColorHSL = props.disabled
    ? COLORS.bg.secondary
    : hovered
      ? COLORS.bg["surface-hover"]
      : COLORS.bg.surface;

  const textColorHSL = props.disabled
    ? COLORS.text.secondary
    : COLORS.text.main;

  // Responsive manager for dynamic sizing
  const responsiveManager = ResponsiveManager.getInstance();

  // Smart sizing: use autoSize if specified or no size provided
  const useAutoSize = props.autoSize ?? (props.size === undefined);

  // Calculate responsive dimensions
  let minWidth = props.minWidth ?? SIZES.button.width;
  let buttonHeight = SIZES.button.height;
  let padding = props.padding ?? SIZES.padding;

  if (props.responsive) {
    const deviceType = responsiveManager.getDeviceType();
    const scaleFactor = deviceType === "mobile" ? 0.9 : deviceType === "tablet" ? 0.95 : 1.0;

    minWidth = minWidth * scaleFactor;
    buttonHeight = buttonHeight * scaleFactor;
    padding = responsiveManager.getResponsiveMargin(padding);
  }

  // Calculate size based on autoSize setting
  const size = props.size ?? (useAutoSize
    ? new UDim2(0, minWidth, 0, buttonHeight)
    : new UDim2(0, minWidth, 0, buttonHeight));

  return (
    <textbutton
      Text={props.text}
      TextColor3={Color3.fromHex(textColorHSL)}
      BackgroundColor3={Color3.fromHex(bgColorHSL)}
      Size={size}
      LayoutOrder={props.LayoutOrder}
      Font={TYPOGRAPHY.font}
      TextSize={SIZES.fontSize}
      AutoButtonColor={false}
      AutomaticSize={useAutoSize ? Enum.AutomaticSize.X : Enum.AutomaticSize.None}
      Event={{
        Activated: props.onClick,
        MouseEnter: () => !props.disabled && setHovered(true),
        MouseLeave: () => setHovered(false),
      }}
      BorderSizePixel={0}
    >
      {/* Rounded corners */}
      <uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />

      {/* Border using border.l2 */}
      <uistroke
        Color={Color3.fromHex(COLORS.border.l2)}
        Thickness={1}
        Transparency={props.disabled ? 0.5 : 0}
      />

      {/* Padding for auto-sizing */}
      {useAutoSize && (
        <uipadding
          PaddingLeft={new UDim(0, padding)}
          PaddingRight={new UDim(0, padding)}
        />
      )}
    </textbutton>
  );
}






