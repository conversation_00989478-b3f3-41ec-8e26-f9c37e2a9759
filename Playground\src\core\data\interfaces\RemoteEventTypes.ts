import { BasePlayerData, PlayerSettings } from "./PlayerData";

export interface DataStoreRequest {
	action: DataStoreAction;
	data?: unknown;
}

export type DataStoreAction =
	| "loadPlayerData"
	| "updateGameData"
	| "updateSettings"
	| "getStats";

export interface DataStoreResponse {
	action: DataStoreAction;
	result: RemoteDataStoreResult;
}

export interface RemoteDataStoreResult {
	success: boolean;
	data?: unknown;
	error?: string;
}

// Specific response data types
export interface LoadPlayerDataResult extends RemoteDataStoreResult {
	data?: BasePlayerData;
}

export interface AddCoinsResult extends RemoteDataStoreResult {
	data?: number;
}

export interface AddExperienceResult extends RemoteDataStoreResult {
	data?: {
		level: number;
		experience: number;
		leveledUp: boolean;
	};
}

export interface AddGemsResult extends RemoteDataStoreResult {
	data?: number;
}

export interface AddInventoryItemResult extends RemoteDataStoreResult {
	data?: number;
}

export interface UnlockAchievementResult extends RemoteDataStoreResult {
	data?: boolean;
}

export interface UpdateSettingsResult extends RemoteDataStoreResult {
	data?: PlayerSettings;
}

export interface GetStatsResult extends RemoteDataStoreResult {
	data?: {
		cacheSize: number;
		loadedPlayers: number;
		isPlayerLoaded: boolean;
	};
}

// Request data types
export interface AddCoinsRequestData {
	amount: number;
}

export interface AddExperienceRequestData {
	amount: number;
}

export interface AddGemsRequestData {
	amount: number;
}

export interface AddInventoryItemRequestData {
	itemId: string;
	quantity: number;
}

export interface UnlockAchievementRequestData {
	achievementId: string;
}

export interface UpdateSettingsRequestData {
	musicVolume?: number;
	graphics?: "Low" | "Medium" | "High";
}
