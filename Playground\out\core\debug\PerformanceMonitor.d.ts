import { Debug<PERSON>enderer } from "./DebugRenderer";
export interface PerformanceData {
    fps: number;
    frameTime: number;
    memoryUsage: number;
    networkReceive: number;
    networkSend: number;
    instanceCount: number;
    heartbeatTime: number;
}
export declare class PerformanceMonitor {
    private renderer;
    private performanceLabel?;
    private fpsHistory;
    private frameTimeHistory;
    private lastUpdateTime;
    private updateInterval;
    private maxHistorySize;
    constructor(renderer: DebugRenderer);
    update(deltaTime: number): void;
    private setupGUI;
    private collectPerformanceData;
    private updateHistory;
    private updatePerformanceDisplay;
    private getMemoryUsage;
    private getNetworkStats;
    private getInstanceCount;
    private getHeartbeatTime;
    private calculateAverage;
    private getPerformanceRating;
    getPerformanceSummary(): string;
    isPerformanceGood(): boolean;
    getDetailedReport(): PerformanceData | undefined;
    cleanup(): void;
}
