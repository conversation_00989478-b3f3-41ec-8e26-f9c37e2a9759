import * as React from "@rbxts/react";
import { Players, ReplicatedStorage } from "@rbxts/services";
import { ContainerFrame, VerticalFrame, Button, Label, IconButton, ScrollingFrame, EntityManager, EntityType, DataStoreResponse, BasePlayerData, AIController, LimbAnimator, AnimationBuilder, DataStoreHelper, DebugUtils, ZIndexManager } from "../../core";

interface WorldTestingPanelProps {
  isOpen?: boolean;
  onClose?: () => void;
}

export function WorldTestingPanel(props: WorldTestingPanelProps): React.ReactElement {
  const isPanelOpen = props.isOpen || false;
  const player = Players.LocalPlayer;

  // Use debug priority Z-Index to ensure this appears above debug overlay panels
  const zIndex = ZIndexManager.getDebugZIndex("world-testing-panel");

  // Debug logging to verify z-index values
  React.useEffect(() => {
    print(`🌍 World Testing Panel created with z-index: ${zIndex}`);
  }, [zIndex]);

  // Get player position for testing
  const getPlayerPosition = (): Vector3 => {
    const character = player.Character;
    const humanoidRootPart = character?.FindFirstChild("HumanoidRootPart") as Part;
    return humanoidRootPart?.Position || new Vector3(0, 10, 0);
  };

  // Send world test request to server
  const sendWorldTestRequest = (testType: string) => {
    const position = getPlayerPosition();
    const remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
    const worldTestingEvent = remoteEventsFolder?.FindFirstChild("WORLD_TESTING") as RemoteEvent;

    if (worldTestingEvent) {
      worldTestingEvent.FireServer({
        request: {
          testType: testType,
          position: position,
          playerId: player.UserId
        },
        clientTimestamp: tick(),
        sequenceNumber: 1
      });
      print(`🌍 Sent ${testType} test request to server`);
    } else {
      print("❌ World testing RemoteEvent not found!");
    }
  };

  // Gravity test functions
  const testLowGravity = () => sendWorldTestRequest("low_gravity_world");
  const testHighGravity = () => sendWorldTestRequest("high_gravity_world");
  const testZeroGravity = () => sendWorldTestRequest("zero_gravity_world");
  const testNormalGravity = () => sendWorldTestRequest("normal_gravity_world");

  // Weather test functions
  const testClearWeather = () => sendWorldTestRequest("clear_weather");
  const testRain = () => sendWorldTestRequest("rain_weather");
  const testHeavyRain = () => sendWorldTestRequest("heavy_rain_weather");
  const testSnow = () => sendWorldTestRequest("snow_weather");
  const testBlizzard = () => sendWorldTestRequest("blizzard_weather");
  const testStorm = () => sendWorldTestRequest("storm_weather");
  const testThunderstorm = () => sendWorldTestRequest("thunderstorm_weather");
  const testFog = () => sendWorldTestRequest("fog_weather");
  const testSandstorm = () => sendWorldTestRequest("sandstorm_weather");

  // Day/Night test functions
  const testDawn = () => sendWorldTestRequest("dawn_time");
  const testNoon = () => sendWorldTestRequest("noon_time");
  const testDusk = () => sendWorldTestRequest("dusk_time");
  const testNight = () => sendWorldTestRequest("night_time");

  // Entity Management test functions
  const entityManager = EntityManager.getInstance();

  const spawnNPC = () => {
    const position = getPlayerPosition().add(new Vector3(5, 0, 0));
    const entity = entityManager.spawnEntity({
      type: EntityType.NPC,
      position: position,
      data: { name: "Test NPC", health: 100 }
    });
    print(`🤖 Spawned NPC with ID: ${entity.id} at position: ${position}`);
  };

  const spawnProjectile = () => {
    const position = getPlayerPosition().add(new Vector3(0, 5, 0));
    const entity = entityManager.spawnEntity({
      type: EntityType.Projectile,
      position: position,
      lifetime: 5, // Auto-destroy after 5 seconds
      data: { speed: 50, damage: 25 }
    });
    print(`🚀 Spawned Projectile with ID: ${entity.id} at position: ${position}`);
  };

  const spawnEffect = () => {
    const position = getPlayerPosition().add(new Vector3(-5, 0, 0));
    const entity = entityManager.spawnEntity({
      type: EntityType.Effect,
      position: position,
      lifetime: 3, // Auto-destroy after 3 seconds
      data: { effectType: "explosion", intensity: 0.8 }
    });
    print(`✨ Spawned Effect with ID: ${entity.id} at position: ${position}`);
  };

  const spawnPickup = () => {
    const position = getPlayerPosition().add(new Vector3(0, 0, 5));
    const entity = entityManager.spawnEntity({
      type: EntityType.Pickup,
      position: position,
      data: { itemType: "health_potion", value: 50 }
    });
    print(`💎 Spawned Pickup with ID: ${entity.id} at position: ${position}`);
  };

  const listEntities = () => {
    const activeCount = entityManager.getActiveEntityCount();
    print(`📊 Active entities: ${activeCount}`);

    const npcs = entityManager.getEntitiesByType(EntityType.NPC);
    const projectiles = entityManager.getEntitiesByType(EntityType.Projectile);
    const effects = entityManager.getEntitiesByType(EntityType.Effect);
    const pickups = entityManager.getEntitiesByType(EntityType.Pickup);

    print(`🤖 NPCs: ${npcs.size()}`);
    print(`🚀 Projectiles: ${projectiles.size()}`);
    print(`✨ Effects: ${effects.size()}`);
    print(`💎 Pickups: ${pickups.size()}`);
  };

  const findNearbyEntities = () => {
    const playerPos = getPlayerPosition();
    const nearbyEntities = entityManager.getEntitiesInRadius(playerPos, 20);
    print(`🔍 Found ${nearbyEntities.size()} entities within 20 studs of player`);

    nearbyEntities.forEach((entity) => {
      const distance = entity.position.sub(playerPos).Magnitude;
      print(`  - ${entity.type} (ID: ${entity.id}) at distance: ${math.floor(distance)} studs`);
    });
  };

  const cleanupAllEntities = () => {
    const beforeCount = entityManager.getActiveEntityCount();
    entityManager.cleanup();
    print(`🧹 Cleaned up ${beforeCount} entities`);
  };

  // Data Persistence test functions using RemoteEvents
  const sendDataStoreRequest = (action: string, data?: any) => {
    const remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
    const dataStoreEvent = remoteEventsFolder?.FindFirstChild("DATA_STORE_TESTING") as RemoteEvent;

    if (dataStoreEvent) {
      dataStoreEvent.FireServer({ action, data });
      print(`📡 Sent ${action} request to server`);
    } else {
      print("❌ DataStore RemoteEvent not found!");
    }
  };

  // Set up response handler
  React.useEffect(() => {
    const remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
    const responseEvent = remoteEventsFolder?.FindFirstChild("DATA_STORE_RESPONSE") as RemoteEvent;

    if (responseEvent) {
      const connection = responseEvent.OnClientEvent.Connect((response: unknown) => {
        const typedResponse = response as DataStoreResponse;
        const { action, result } = typedResponse;

        if (result.success) {
          switch (action) {
            case "loadPlayerData":
              if (result.data) {
                const playerData = result.data as BasePlayerData;
                print(`📊 Loaded player data:`);
                print(`  User ID: ${playerData.userId}`);
                print(`  Created: ${playerData.createdAt}`);
                print(`  Last Login: ${playerData.lastLogin}`);
                print(`  Playtime: ${math.floor(playerData.playtime)} seconds`);
                print(`  Total Sessions: ${playerData.metadata.totalSessions}`);
                print(`  Music Volume: ${playerData.settings.musicVolume}`);
                print(`  Game Data:`, playerData.gameData);
              }
              break;
            case "updateGameData":
              if (result.data) {
                const updatedData = result.data as BasePlayerData;
                print(`🎮 Updated game data!`);
                print(`  Game Data:`, updatedData.gameData);
              }
              break;
            case "updateSettings":
              if (result.data) {
                const settings = result.data as { musicVolume: number; graphics: string };
                print(`⚙️ Updated settings:`);
                print(`  Music Volume: ${settings.musicVolume}`);
                print(`  Graphics: ${settings.graphics}`);
              }
              break;
            case "getStats":
              if (result.data) {
                const stats = result.data as { cacheSize: number; loadedPlayers: number; isPlayerLoaded: boolean };
                print(`📈 DataStore Statistics:`);
                print(`  Cache entries: ${stats.cacheSize}`);
                print(`  Loaded players: ${stats.loadedPlayers}`);
                print(`  Player loaded: ${stats.isPlayerLoaded}`);
              }
              break;
          }
        } else {
          print(`❌ ${action} failed: ${result.error}`);
        }
      });

      return () => connection.Disconnect();
    }
  }, []);

  const testLoadPlayerData = () => {
    sendDataStoreRequest("loadPlayerData");
  };

  const testUpdateGameData = () => {
    sendDataStoreRequest("updateGameData", {
      gameDataUpdates: {
        coins: 100,
        level: 5,
        experience: 250,
        lastAction: "test_update",
        timestamp: tick()
      }
    });
  };

  const testUpdateSettings = () => {
    sendDataStoreRequest("updateSettings", {
      musicVolume: 0.6,
      graphics: "High"
    });
  };

  const testDataStoreStats = () => {
    sendDataStoreRequest("getStats");
  };

  const enableStudioDataStore = () => {
    // Access the DataStoreHelper directly to enable Studio testing
    const dataStore = DataStoreHelper.getInstance();
    dataStore.enableStudioTesting();
  };

  const disableAutoSave = () => {
    // Access the DataStoreHelper directly to disable auto-save
    const dataStore = DataStoreHelper.getInstance();
    dataStore.disableAutoSave();
  };

  // AI System test functions
  const aiController = AIController.getInstance();
  const [spawnedAIEntities, setSpawnedAIEntities] = React.useState<string[]>([]);

  const spawnAINPC = () => {
    const position = getPlayerPosition().add(new Vector3(10, 0, 0));
    const entity = entityManager.spawnEntity({
      type: EntityType.NPC,
      position: position,
      data: { name: "AI NPC", aiEnabled: true }
    });

    // Register AI for this entity
    const aiAgent = aiController.registerAI(entity.id, {
      detectionRange: 30,
      followRange: 20,
      moveSpeed: 12,
      patrolRadius: 15
    });

    setSpawnedAIEntities([...spawnedAIEntities, entity.id]);
    print(`🤖 Spawned AI NPC with ID: ${entity.id}`);
    print(`🧠 AI registered with default behaviors`);
  };

  const spawnPatrollingNPC = () => {
    const position = getPlayerPosition().add(new Vector3(-10, 0, 10));
    const entity = entityManager.spawnEntity({
      type: EntityType.NPC,
      position: position,
      data: { name: "Patrol NPC", behavior: "patrol" }
    });

    // Register AI with patrol focus
    aiController.registerAI(entity.id, {
      detectionRange: 15, // Shorter detection range
      followRange: 8,
      moveSpeed: 8,
      patrolRadius: 20
    });

    setSpawnedAIEntities([...spawnedAIEntities, entity.id]);
    print(`🚶 Spawned Patrolling NPC with ID: ${entity.id}`);
  };

  const spawnFleeingNPC = () => {
    const position = getPlayerPosition().add(new Vector3(0, 0, -10));
    const entity = entityManager.spawnEntity({
      type: EntityType.NPC,
      position: position,
      data: { name: "Scared NPC", behavior: "flee" }
    });

    // Register AI that prefers to flee
    const aiAgent = aiController.registerAI(entity.id, {
      detectionRange: 25,
      followRange: 5, // Very short follow range
      moveSpeed: 16,
      patrolRadius: 10
    });

    // Set initial flee state
    aiAgent.setBlackboardValue("shouldFlee", true);

    setSpawnedAIEntities([...spawnedAIEntities, entity.id]);
    print(`😱 Spawned Fleeing NPC with ID: ${entity.id}`);
  };

  const makeNPCInvestigate = () => {
    if (spawnedAIEntities.size() === 0) {
      print("❌ No AI NPCs spawned yet!");
      return;
    }

    const entityId = spawnedAIEntities[0];
    const aiAgent = aiController.getAI(entityId);

    if (aiAgent) {
      const investigatePos = getPlayerPosition().add(new Vector3(5, 0, 5));
      aiAgent.setBlackboardValue("investigatePosition", investigatePos);
      print(`🔍 ${entityId} will investigate position: ${investigatePos}`);
    }
  };

  const showAIStats = () => {
    const aiCount = aiController.getAICount();
    const allAIs = aiController.getAllAIs();

    print(`🧠 AI System Statistics:`);
    print(`  Total AI entities: ${aiCount}`);
    print(`  Spawned entities: ${spawnedAIEntities.size()}`);

    allAIs.forEach((aiAgent, index) => {
      print(`  AI ${index + 1}: State = ${aiAgent.getState()}`);
    });
  };

  const cleanupAllAI = () => {
    spawnedAIEntities.forEach((entityId) => {
      aiController.unregisterAI(entityId);
      entityManager.destroyEntity(entityId);
    });

    setSpawnedAIEntities([]);
    print(`🧹 Cleaned up all AI entities`);
  };

  const demonstrateDebugSystem = () => {
    const playerPos = getPlayerPosition();

    // Draw some debug lines and shapes
    DebugUtils.drawLine(playerPos, playerPos.add(new Vector3(10, 0, 0)), Color3.fromRGB(255, 0, 0), 5);
    DebugUtils.drawLine(playerPos, playerPos.add(new Vector3(0, 10, 0)), Color3.fromRGB(0, 255, 0), 5);
    DebugUtils.drawLine(playerPos, playerPos.add(new Vector3(0, 0, 10)), Color3.fromRGB(0, 0, 255), 5);

    // Draw some spheres
    DebugUtils.drawSphere(playerPos.add(new Vector3(5, 5, 5)), 2, Color3.fromRGB(255, 255, 0), 5);
    DebugUtils.drawSphere(playerPos.add(new Vector3(-5, 5, -5)), 1.5, Color3.fromRGB(255, 0, 255), 5);

    // Draw some text
    DebugUtils.drawText(playerPos.add(new Vector3(0, 8, 0)), "Debug System Demo!", Color3.fromRGB(255, 255, 255), 5);
    DebugUtils.drawText(playerPos.add(new Vector3(5, 3, 0)), "Red = X Axis", Color3.fromRGB(255, 0, 0), 5);
    DebugUtils.drawText(playerPos.add(new Vector3(0, 13, 0)), "Green = Y Axis", Color3.fromRGB(0, 255, 0), 5);
    DebugUtils.drawText(playerPos.add(new Vector3(0, 3, 5)), "Blue = Z Axis", Color3.fromRGB(0, 0, 255), 5);

    // Log debug messages
    DebugUtils.log("Demo", "Debug system demonstration started", { position: playerPos });
    DebugUtils.log("Demo", "Drawing coordinate axes and markers");

    print(`🔍 Debug demonstration active! Press F3 to toggle debug overlay`);
    print(`🎨 Visual elements will appear for 5 seconds`);
  };

  // Existing Animation System test functions
  const [limbAnimator, setLimbAnimator] = React.useState<LimbAnimator | undefined>();

  const initializeAnimationSystem = () => {
    const player = Players.LocalPlayer;
    const character = player.Character;

    if (!character) {
      print("❌ No character found! Spawn first.");
      return;
    }

    try {
      const limbAnim = LimbAnimator.forCharacter(character);
      setLimbAnimator(limbAnim);

      print("🎬 Existing animation system initialized!");
      print("🦴 Using LimbAnimator with moveLimb, moveBodyPart, and lunge methods");
    } catch (error) {
      print(`❌ Failed to initialize animation system: ${error}`);
    }
  };

  const testLimbAnimation = () => {
    if (!limbAnimator) {
      print("❌ Animation system not initialized!");
      return;
    }

    print("👋 Testing limb animation...");

    // Move right arm up (wave gesture)
    limbAnimator.moveLimb("RightShoulder",
      new CFrame().mul(CFrame.fromEulerAnglesXYZ(0, 0, math.rad(-90))),
      0.5
    );

    print("👋 Wave animation started!");
  };

  const testPresetAnimations = () => {
    if (!limbAnimator) {
      print("❌ Animation system not initialized!");
      return;
    }

    print("🦾 Testing preset animations...");

    // Test lunge animation (existing method)
    limbAnimator.lunge(new Vector3(1, 0, 0), 5, 0.5);
    print("🏃 Lunge animation started!");

    // Test body part movement
    limbAnimator.moveBodyPart("Head",
      new CFrame().mul(CFrame.fromEulerAnglesXYZ(0, math.rad(30), 0)),
      0.5
    );
    print("🗣️ Head turn started!");
  };

  const showAnimationStats = () => {
    if (!limbAnimator) {
      print("❌ Animation system not initialized!");
      return;
    }

    print("🎬 Animation System Statistics:");
    print("  LimbAnimator: Available for moveLimb, moveBodyPart, and lunge");
    print("  AnimationBuilder: Available for complex animation sequences");
  };

  const cleanupAnimationSystem = () => {
    if (limbAnimator) {
      setLimbAnimator(undefined);
    }

    print("🧹 Animation system cleaned up");
  };

  return (
    <>
      {/* World Testing Panel using Core Components */}
      {isPanelOpen && (
        <ContainerFrame
          size={new UDim2(0, 300, 1, 0)}
          position={new UDim2(1, -300, 0, 0)}
          backgroundTransparency={0}
          borderThickness={0}
          zIndex={zIndex}
        >
          {/* Header with Title and Close Button */}
          <ContainerFrame
            size={new UDim2(1, 0, 0, 60)}
            position={new UDim2(0, 0, 0, 0)}
            backgroundTransparency={0}
            borderThickness={0}
          >
            {/* Title on the left */}
            <Label
              text="🌍 World Testing Lab"
              fontSize={16}
              bold={true}
              position={new UDim2(0, 16, 0.5, 0)}
              anchorPoint={new Vector2(0, 0.5)}
              size={new UDim2(1, -60, 0, 20)} // Leave space for close button
            />

            {/* Close button on the right */}
            <IconButton
              icon="✕"
              onClick={props.onClose || (() => {})}
              size={new UDim2(0, 32, 0, 32)}
              position={new UDim2(1, -16, 0.5, 0)}
              anchorPoint={new Vector2(1, 0.5)}
            />
          </ContainerFrame>

          {/* Content Area with Scrolling */}
          <ScrollingFrame
            size={new UDim2(1, 0, 1, -60)}
            position={new UDim2(0, 0, 0, 60)}
            backgroundTransparency={1}
            borderThickness={0}
            scrollingDirection={Enum.ScrollingDirection.Y}
            automaticCanvasSize={Enum.AutomaticSize.Y}
          >
            <VerticalFrame spacing={16} padding={16}>
              {/* Section Header */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🌍 Gravity Testing"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test different gravity levels to see how they affect the entire world."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Gravity Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🔮 Low Gravity (0.3x)"
                  onClick={testLowGravity}
                />
                <Button
                  text="🌪️ High Gravity (2.5x)"
                  onClick={testHighGravity}
                />
                <Button
                  text="🌀 Zero Gravity (0.05x)"
                  onClick={testZeroGravity}
                />
                <Button
                  text="🌍 Normal Gravity (1.0x)"
                  onClick={testNormalGravity}
                />
              </VerticalFrame>

              {/* Weather Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🌦️ Weather Testing"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test different weather conditions and atmospheric effects."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Weather Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="☀️ Clear Weather"
                  onClick={testClearWeather}
                />
                <Button
                  text="🌧️ Light Rain"
                  onClick={testRain}
                />
                <Button
                  text="🌧️ Heavy Rain"
                  onClick={testHeavyRain}
                />
                <Button
                  text="❄️ Snow"
                  onClick={testSnow}
                />
                <Button
                  text="🌨️ Blizzard"
                  onClick={testBlizzard}
                />
                <Button
                  text="🌪️ Storm"
                  onClick={testStorm}
                />
                <Button
                  text="⛈️ Thunderstorm"
                  onClick={testThunderstorm}
                />
                <Button
                  text="🌫️ Fog"
                  onClick={testFog}
                />
                <Button
                  text="🏜️ Sandstorm"
                  onClick={testSandstorm}
                />
              </VerticalFrame>

              {/* Day/Night Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🌅 Day/Night Cycle"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Control the time of day and lighting conditions."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Day/Night Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🌅 Dawn"
                  onClick={testDawn}
                />
                <Button
                  text="☀️ Noon"
                  onClick={testNoon}
                />
                <Button
                  text="🌇 Dusk"
                  onClick={testDusk}
                />
                <Button
                  text="🌙 Night"
                  onClick={testNight}
                />
              </VerticalFrame>

              {/* Entity Management Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🤖 Entity Management"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the new Entity Management system - spawn, track, and manage game entities."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Entity Spawn Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🤖 Spawn NPC"
                  onClick={spawnNPC}
                />
                <Button
                  text="🚀 Spawn Projectile"
                  onClick={spawnProjectile}
                />
                <Button
                  text="✨ Spawn Effect"
                  onClick={spawnEffect}
                />
                <Button
                  text="💎 Spawn Pickup"
                  onClick={spawnPickup}
                />
              </VerticalFrame>

              {/* Entity Management Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="📊 List All Entities"
                  onClick={listEntities}
                />
                <Button
                  text="🔍 Find Nearby Entities"
                  onClick={findNearbyEntities}
                />
                <Button
                  text="🧹 Cleanup All Entities"
                  onClick={cleanupAllEntities}
                />
              </VerticalFrame>

              {/* Data Persistence Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="💾 Data Persistence"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the DataStore system - save/load player data, currency, inventory, and settings."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Player Data Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="📊 Load Player Data"
                  onClick={testLoadPlayerData}
                />
                <Button
                  text="🎮 Update Game Data"
                  onClick={testUpdateGameData}
                />
                <Button
                  text="⚙️ Update Settings"
                  onClick={testUpdateSettings}
                />
                <Button
                  text="📈 Show DataStore Stats"
                  onClick={testDataStoreStats}
                />
                <Button
                  text="🔧 Enable Studio DataStore"
                  onClick={enableStudioDataStore}
                />
                <Button
                  text="🛑 Disable Auto-Save"
                  onClick={disableAutoSave}
                />
              </VerticalFrame>

              {/* AI System Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🧠 AI System"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the AI behavior system - spawn NPCs with different AI behaviors and watch them interact."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* AI Spawn Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🤖 Spawn AI NPC"
                  onClick={spawnAINPC}
                />
                <Button
                  text="🚶 Spawn Patrolling NPC"
                  onClick={spawnPatrollingNPC}
                />
                <Button
                  text="😱 Spawn Fleeing NPC"
                  onClick={spawnFleeingNPC}
                />
                <Button
                  text="🔍 Make NPC Investigate"
                  onClick={makeNPCInvestigate}
                />
              </VerticalFrame>

              {/* AI Management Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="📊 Show AI Stats"
                  onClick={showAIStats}
                />
                <Button
                  text="🔍 Debug System Demo"
                  onClick={demonstrateDebugSystem}
                />
                <Button
                  text="🧹 Cleanup All AI"
                  onClick={cleanupAllAI}
                />
              </VerticalFrame>

              {/* Animation System Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🎬 Animation System"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the animation system - create programmatic animations without animation assets."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Animation Setup Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🎬 Initialize Animation System"
                  onClick={initializeAnimationSystem}
                />
                <Button
                  text="👋 Test Limb Animation"
                  onClick={testLimbAnimation}
                />
                <Button
                  text="🦾 Test Preset Animations"
                  onClick={testPresetAnimations}
                />
              </VerticalFrame>

              {/* Animation Testing Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="� Show Animation Stats"
                  onClick={showAnimationStats}
                />
                <Button
                  text="� Cleanup Animation System"
                  onClick={cleanupAnimationSystem}
                />
              </VerticalFrame>


            </VerticalFrame>
          </ScrollingFrame>
        </ContainerFrame>
      )}
    </>
  );
}
