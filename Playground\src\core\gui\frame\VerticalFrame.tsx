import * as React from "@rbxts/react";
import { COLORS, SIZES } from "../../design";
import { VerticalFrameProps } from "./types";
import { ResponsiveManager } from "../layout/ResponsiveManager";

export function VerticalFrame(props: VerticalFrameProps): React.ReactElement {
  const backgroundColor = props.backgroundColor ?? COLORS.bg.base;
  const backgroundTransparency = props.backgroundTransparency ?? 1;

  // Responsive manager for dynamic calculations
  const responsiveManager = ResponsiveManager.getInstance();

  // Calculate responsive padding and spacing
  const basePadding = props.padding ?? SIZES.padding;
  const baseSpacing = props.spacing ?? SIZES.margin;

  const padding = props.responsiveMargin
    ? responsiveManager.getResponsiveMargin(basePadding)
    : basePadding;

  const spacing = props.responsiveMargin
    ? responsiveManager.getResponsiveMargin(baseSpacing)
    : baseSpacing;

  // Smart sizing: if fitContent is true or no size specified, use AutomaticSize
  const fitContent = props.fitContent ?? (props.size === undefined);
  const size = props.size ?? (fitContent ? new UDim2(1, 0, 0, 0) : new UDim2(1, 0, 1, 0));
  const autoSize = props.autoSize ?? (fitContent ? Enum.AutomaticSize.Y : Enum.AutomaticSize.None);

  return (
    <frame
      BackgroundColor3={Color3.fromHex(backgroundColor)}
      BackgroundTransparency={backgroundTransparency}
      Size={size}
      Position={props.position}
      AnchorPoint={props.anchorPoint}
      LayoutOrder={props.layoutOrder}
      BorderSizePixel={0}
      ZIndex={props.zIndex}
      AutomaticSize={autoSize}
      ClipsDescendants={true} // Prevent content from overflowing the frame
    >
      <uilistlayout
        Padding={new UDim(0, spacing)}
        SortOrder={Enum.SortOrder.LayoutOrder}
        FillDirection={Enum.FillDirection.Vertical}
        HorizontalAlignment={props.horizontalAlignment ?? Enum.HorizontalAlignment.Left}
        VerticalAlignment={Enum.VerticalAlignment.Top}
      />

      <uipadding
        PaddingTop={new UDim(0, padding)}
        PaddingBottom={new UDim(0, padding)}
        PaddingLeft={new UDim(0, padding)}
        PaddingRight={new UDim(0, padding)}
      />

      {/* Add size constraints if specified */}
      {(props.minSize || props.maxSize) && (
        <uisizeconstraint
          MinSize={props.minSize ? new Vector2(props.minSize.X.Scale * 1920 + props.minSize.X.Offset, props.minSize.Y.Scale * 1080 + props.minSize.Y.Offset) : undefined}
          MaxSize={props.maxSize ? new Vector2(props.maxSize.X.Scale * 1920 + props.maxSize.X.Offset, props.maxSize.Y.Scale * 1080 + props.maxSize.Y.Offset) : undefined}
        />
      )}

      {props.children}
    </frame>
  );
}