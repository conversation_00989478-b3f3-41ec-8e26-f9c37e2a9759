import * as React from "@rbxts/react";
export interface AbilitySlotData {
    id: string;
    name: string;
    icon?: string;
    cooldownEndTime?: number;
    isOnCooldown?: boolean;
    totalCooldownDuration?: number;
}
interface ActionBarProps {
    slots: AbilitySlotData[];
    onSlotClick: (slotIndex: number, abilityId: string) => void;
    layoutOrder?: number;
    position?: UDim2;
    anchorPoint?: Vector2;
    zIndex?: number;
}
export declare function ActionBar(props: ActionBarProps): React.ReactElement;
export {};
