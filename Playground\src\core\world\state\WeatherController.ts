import { Lighting, Workspace, TweenService } from "@rbxts/services";
import { WeatherOptions, WeatherType } from "./interfaces/WeatherOptions";
import { AtmosphereOptions } from "./interfaces/AtmosphereOptions";
import { EffectPartBuilder } from "../../effects/EffectPartBuilder";
import { EffectTweenBuilder } from "../../effects/EffectTweenBuilder";

/**
 * WeatherController - Manages weather effects and atmospheric conditions
 * Provides simple methods for changing weather states with smooth transitions
 */
export class WeatherController {
    private static instance: WeatherController;
    private currentWeather: WeatherType = "clear";
    private weatherEffects: Map<string, Instance> = new Map();

    private constructor() {}

    public static getInstance(): WeatherController {
        if (!WeatherController.instance) {
            WeatherController.instance = new WeatherController();
        }
        return WeatherController.instance;
    }

    /**
     * Change the weather with optional smooth transition
     */
    public setWeather(options: WeatherOptions): void {
        this.currentWeather = options.type;
        
        // Clear existing weather effects
        this.clearWeatherEffects();

        switch (options.type) {
            case "clear":
                this.setClearWeather();
                break;
            case "rain":
                this.setRainWeather(options.intensity || 0.5);
                break;
            case "heavy_rain":
                this.setRainWeather(options.intensity || 0.8);
                break;
            case "snow":
                this.setSnowWeather(options.intensity || 0.5);
                break;
            case "blizzard":
                this.setSnowWeather(options.intensity || 0.9);
                break;
            case "storm":
                this.setStormWeather(options.intensity || 0.7);
                break;
            case "thunderstorm":
                this.setThunderstormWeather(options.intensity || 0.8);
                break;
            case "fog":
                this.setFogWeather(options.intensity || 0.6);
                break;
            case "sandstorm":
                this.setSandstormWeather(options.intensity || 0.7);
                break;
        }

        print(`🌦️ Weather changed to: ${options.type}`);
    }

    /**
     * Get current weather type
     */
    public getCurrentWeather(): WeatherType {
        return this.currentWeather;
    }

    private setClearWeather(): void {
        // Clear, bright day
        Lighting.FogEnd = 100000;
        Lighting.FogStart = 0;
        Lighting.FogColor = new Color3(0.76, 0.76, 0.76);
        Lighting.Brightness = 1;
        Lighting.Ambient = new Color3(0.5, 0.5, 0.5);
    }

    private setRainWeather(intensity: number): void {
        // Overcast and rainy
        Lighting.FogEnd = math.max(500, 2000 - (intensity * 1500));
        Lighting.FogStart = 0;
        Lighting.FogColor = new Color3(0.6, 0.6, 0.6);
        Lighting.Brightness = math.max(0.3, 0.8 - (intensity * 0.4));
        Lighting.Ambient = new Color3(0.3, 0.3, 0.4);
        
        // Create rain particle effect
        this.createRainEffect(intensity);
    }

    private setSnowWeather(intensity: number): void {
        // Cold and snowy
        Lighting.FogEnd = math.max(300, 1500 - (intensity * 1200));
        Lighting.FogStart = 0;
        Lighting.FogColor = new Color3(0.8, 0.8, 0.9);
        Lighting.Brightness = math.max(0.4, 0.9 - (intensity * 0.3));
        Lighting.Ambient = new Color3(0.4, 0.4, 0.5);
        
        // Create snow particle effect
        this.createSnowEffect(intensity);
    }

    private setStormWeather(intensity: number): void {
        // Dark storm clouds
        Lighting.FogEnd = math.max(200, 1000 - (intensity * 800));
        Lighting.FogStart = 0;
        Lighting.FogColor = new Color3(0.3, 0.3, 0.3);
        Lighting.Brightness = math.max(0.1, 0.5 - (intensity * 0.3));
        Lighting.Ambient = new Color3(0.2, 0.2, 0.3);
        
        this.createStormEffect(intensity);
    }

    private setThunderstormWeather(intensity: number): void {
        // Very dark with lightning
        Lighting.FogEnd = math.max(150, 800 - (intensity * 650));
        Lighting.FogStart = 0;
        Lighting.FogColor = new Color3(0.2, 0.2, 0.2);
        Lighting.Brightness = math.max(0.05, 0.3 - (intensity * 0.2));
        Lighting.Ambient = new Color3(0.1, 0.1, 0.2);
        
        this.createThunderstormEffect(intensity);
    }

    private setFogWeather(intensity: number): void {
        // Heavy fog
        Lighting.FogEnd = math.max(50, 300 - (intensity * 250));
        Lighting.FogStart = 0;
        Lighting.FogColor = new Color3(0.7, 0.7, 0.7);
        Lighting.Brightness = math.max(0.2, 0.6 - (intensity * 0.3));
        Lighting.Ambient = new Color3(0.3, 0.3, 0.3);
    }

    private setSandstormWeather(intensity: number): void {
        // Sandy, dusty conditions
        Lighting.FogEnd = math.max(100, 600 - (intensity * 500));
        Lighting.FogStart = 0;
        Lighting.FogColor = new Color3(0.8, 0.7, 0.5);
        Lighting.Brightness = math.max(0.3, 0.7 - (intensity * 0.3));
        Lighting.Ambient = new Color3(0.4, 0.3, 0.2);
        
        this.createSandstormEffect(intensity);
    }

    private createRainEffect(intensity: number): void {
        // Create rain particles using Core framework
        const rainEffect = EffectPartBuilder.create()
            .size(new Vector3(200, 1, 200))
            .position(new Vector3(0, 100, 0))
            .transparency(1)
            .spawn();
        
        rainEffect.Name = "RainEffect";
        this.weatherEffects.set("rain", rainEffect);
        
        // Create rain particle effect using ParticleHelper approach
        const numRainDrops = math.floor(intensity * 100);
        this.createRainParticles(rainEffect, intensity, numRainDrops);
        
        print(`💧 Rain effect created with intensity: ${intensity}`);
    }

    private createRainParticles(parent: Part, intensity: number, numDrops: number): void {
        // Create continuous rain effect that falls from sky to ground
        const rainLoop = () => {
            if (this.currentWeather !== "rain" && this.currentWeather !== "heavy_rain") {
                return; // Stop if weather changed
            }

            // Create raindrops that start high and fall to ground level
            for (let i = 0; i < math.floor(intensity * 20); i++) {
                const startHeight = 200; // Start high in the sky
                const groundLevel = 0;   // Fall to ground level
                
                const raindrop = EffectPartBuilder.create()
                    .shape(Enum.PartType.Cylinder)
                    .size(new Vector3(0.3, 3, 0.3)) // Bigger, more visible raindrops
                    .color(new Color3(0.6, 0.8, 1))
                    .material(Enum.Material.Glass)
                    .transparency(0.1) // Very visible
                    .position(new Vector3(
                        math.random(-300, 300), // Wide area
                        startHeight,            // Start from sky
                        math.random(-300, 300)
                    ))
                    .spawn();

                // Rotate to look like falling rain
                raindrop.CFrame = raindrop.CFrame.mul(CFrame.Angles(math.rad(90), 0, 0));

                // Animate falling to the ground using tween for smooth motion
                EffectTweenBuilder.for(raindrop)
                    .move(new Vector3(
                        raindrop.Position.X + math.random(-20, 20) * intensity, // Slight wind drift
                        groundLevel - 10, // Fall below ground to ensure it hits
                        raindrop.Position.Z + math.random(-10, 10) * intensity
                    ))
                    .duration(2.5) // Time to fall from sky to ground
                    .easing(Enum.EasingStyle.Linear, Enum.EasingDirection.InOut)
                    .onComplete(() => {
                        if (raindrop.Parent) {
                            raindrop.Destroy();
                        }
                    })
                    .play();

                // Backup cleanup
                task.delay(4, () => {
                    if (raindrop.Parent) {
                        raindrop.Destroy();
                    }
                });
            }

            // Continue rain loop
            task.wait(0.3); // More frequent rain
            rainLoop();
        };

        // Start continuous rain
        task.spawn(() => rainLoop());
    }

    private createSnowEffect(intensity: number): void {
        // Create snow particles using Core framework
        const snowEffect = EffectPartBuilder.create()
            .size(new Vector3(200, 1, 200))
            .position(new Vector3(0, 100, 0))
            .transparency(1)
            .spawn();
        
        snowEffect.Name = "SnowEffect";
        this.weatherEffects.set("snow", snowEffect);
        
        // Create snow particle effect
        const numSnowflakes = math.floor(intensity * 80);
        this.createSnowParticles(snowEffect, intensity, numSnowflakes);
        
        print(`❄️ Snow effect created with intensity: ${intensity}`);
    }

    private createSnowParticles(parent: Part, intensity: number, numFlakes: number): void {
        // Create continuous snow effect that falls from sky to ground
        const snowLoop = () => {
            if (this.currentWeather !== "snow" && this.currentWeather !== "blizzard") {
                return; // Stop if weather changed
            }

            // Create snowflakes that start high and fall to ground level
            for (let i = 0; i < math.floor(intensity * 15); i++) {
                const startHeight = 180; // Start high in the sky
                const groundLevel = 0;   // Fall to ground level
                
                const snowflake = EffectPartBuilder.create()
                    .shape(Enum.PartType.Ball)
                    .size(new Vector3(0.8, 0.8, 0.8)) // Large, visible snowflakes
                    .color(new Color3(1, 1, 1))
                    .material(Enum.Material.Neon)
                    .transparency(0.05) // Very visible
                    .position(new Vector3(
                        math.random(-250, 250), // Wide area
                        startHeight,            // Start from sky
                        math.random(-250, 250)
                    ))
                    .spawn();

                // Animate gentle falling to the ground with drift
                EffectTweenBuilder.for(snowflake)
                    .move(new Vector3(
                        snowflake.Position.X + math.random(-30, 30) * intensity, // Wind drift
                        groundLevel - 5, // Fall to ground
                        snowflake.Position.Z + math.random(-30, 30) * intensity
                    ))
                    .duration(5) // Slow gentle fall
                    .easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
                    .onComplete(() => {
                        if (snowflake.Parent) {
                            snowflake.Destroy();
                        }
                    })
                    .play();

                // Backup cleanup
                task.delay(7, () => {
                    if (snowflake.Parent) {
                        snowflake.Destroy();
                    }
                });
            }

            // Continue snow loop
            task.wait(0.6); // Gentle snow frequency
            snowLoop();
        };

        // Start continuous snow
        task.spawn(() => snowLoop());
    }

    private createStormEffect(intensity: number): void {
        // Create storm particles using Core framework
        const stormEffect = EffectPartBuilder.create()
            .size(new Vector3(200, 1, 200))
            .position(new Vector3(0, 100, 0))
            .transparency(1)
            .spawn();
        
        stormEffect.Name = "StormEffect";
        this.weatherEffects.set("storm", stormEffect);
        
        // Create storm particle effect with wind and heavy rain
        const numStormParticles = math.floor(intensity * 120);
        this.createStormParticles(stormEffect, intensity, numStormParticles);
        
        print(`🌪️ Storm effect created with intensity: ${intensity}`);
    }

    private createStormParticles(parent: Part, intensity: number, numParticles: number): void {
        // Create continuous storm effect
        const stormLoop = () => {
            if (this.currentWeather !== "storm") {
                return; // Stop if weather changed
            }

            // Create intense storm particles that fall from sky to ground
            for (let i = 0; i < math.floor(intensity * 25); i++) {
                const startHeight = 220; // Start very high for storms
                const groundLevel = 0;   // Fall to ground level
                
                const stormParticle = EffectPartBuilder.create()
                    .shape(Enum.PartType.Cylinder)
                    .size(new Vector3(0.4, 4, 0.4)) // Large, dramatic raindrops
                    .color(new Color3(0.4, 0.5, 0.7))
                    .material(Enum.Material.Glass)
                    .transparency(0.1) // Very visible
                    .position(new Vector3(
                        math.random(-400, 400), // Very wide storm area
                        startHeight,            // Start from sky
                        math.random(-400, 400)
                    ))
                    .spawn();

                // Rotate for falling effect
                stormParticle.CFrame = stormParticle.CFrame.mul(CFrame.Angles(
                    math.rad(90 + math.random(-20, 20)), 
                    0, 
                    math.random(-0.5, 0.5)
                ));

                // Animate violent falling with strong wind
                EffectTweenBuilder.for(stormParticle)
                    .move(new Vector3(
                        stormParticle.Position.X + math.random(-60, 60) * intensity, // Strong wind drift
                        groundLevel - 15, // Fall deep to ground
                        stormParticle.Position.Z + math.random(-40, 40) * intensity
                    ))
                    .duration(1.8) // Very fast fall for storms
                    .easing(Enum.EasingStyle.Quad, Enum.EasingDirection.In)
                    .onComplete(() => {
                        if (stormParticle.Parent) {
                            stormParticle.Destroy();
                        }
                    })
                    .play();

                // Clean up quickly due to intensity
                task.delay(3, () => {
                    if (stormParticle.Parent) {
                        stormParticle.Destroy();
                    }
                });
            }

            // Continue storm loop rapidly
            task.wait(0.3); // Very frequent storms
            stormLoop();
        };

        // Start continuous storm
        task.spawn(() => stormLoop());
    }

    private createThunderstormEffect(intensity: number): void {
        // Create thunderstorm particles using Core framework
        const thunderEffect = EffectPartBuilder.create()
            .size(new Vector3(200, 1, 200))
            .position(new Vector3(0, 100, 0))
            .transparency(1)
            .spawn();
        
        thunderEffect.Name = "ThunderstormEffect";
        this.weatherEffects.set("thunderstorm", thunderEffect);
        
        // Create intense storm with lightning
        const numThunderParticles = math.floor(intensity * 150);
        this.createThunderstormParticles(thunderEffect, intensity, numThunderParticles);
        
        // Start lightning effects
        this.startLightningEffects(intensity);
        
        print(`⛈️ Thunderstorm effect created with intensity: ${intensity}`);
    }

    private createThunderstormParticles(parent: Part, intensity: number, numParticles: number): void {
        // Create continuous thunderstorm effect
        const thunderstormLoop = () => {
            if (this.currentWeather !== "thunderstorm") {
                return; // Stop if weather changed
            }

            // Heavy rain particles that fall from sky to ground
            for (let i = 0; i < math.floor(intensity * 30); i++) {
                const startHeight = 240; // Start very high for thunderstorms
                const groundLevel = 0;   // Fall to ground level
                
                const rainParticle = EffectPartBuilder.create()
                    .shape(Enum.PartType.Cylinder)
                    .size(new Vector3(0.35, 4.5, 0.35)) // Very large rain
                    .color(new Color3(0.3, 0.4, 0.6))
                    .material(Enum.Material.Glass)
                    .transparency(0.05) // Very visible
                    .position(new Vector3(
                        math.random(-500, 500), // Massive thunderstorm area
                        startHeight,            // Start from very high
                        math.random(-500, 500)
                    ))
                    .spawn();

                // Rotate for falling effect
                rainParticle.CFrame = rainParticle.CFrame.mul(CFrame.Angles(
                    math.rad(90 + math.random(-30, 30)), 
                    0, 
                    math.random(-1, 1)
                ));

                // Animate extremely fast falling with chaotic wind
                EffectTweenBuilder.for(rainParticle)
                    .move(new Vector3(
                        rainParticle.Position.X + math.random(-80, 80) * intensity, // Extreme wind drift
                        groundLevel - 20, // Fall deep below ground
                        rainParticle.Position.Z + math.random(-60, 60) * intensity
                    ))
                    .duration(1.2) // Extremely fast fall
                    .easing(Enum.EasingStyle.Quad, Enum.EasingDirection.In)
                    .onComplete(() => {
                        if (rainParticle.Parent) {
                            rainParticle.Destroy();
                        }
                    })
                    .play();

                // Clean up
                task.delay(2.5, () => {
                    if (rainParticle.Parent) {
                        rainParticle.Destroy();
                    }
                });
            }

            // Random lightning flash effects
            if (math.random() < 0.3) { // 30% chance per cycle
                this.createLightningFlash(parent);
            }

            // Continue thunderstorm loop
            task.wait(0.2); // Very rapid thunderstorm
            thunderstormLoop();
        };

        // Start continuous thunderstorm
        task.spawn(() => thunderstormLoop());
    }

    private createLightningFlash(parent: Part): void {
        // Create bright flash effect
        const flash = EffectPartBuilder.create()
            .shape(Enum.PartType.Ball)
            .size(new Vector3(500, 500, 500)) // Huge flash area
            .color(new Color3(1, 1, 0.8)) // Bright white-yellow
            .material(Enum.Material.Neon)
            .transparency(0.7)
            .position(parent.Position.add(new Vector3(0, 100, 0)))
            .spawn();

        // Flash effect - quick bright flash
        EffectTweenBuilder.for(flash)
            .fade(0.95)
            .duration(0.1)
            .easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
            .onComplete(() => {
                if (flash.Parent) {
                    flash.Destroy();
                }
            })
            .play();
    }

    private createSandstormEffect(intensity: number): void {
        // Create sandstorm particles using Core framework
        const sandEffect = EffectPartBuilder.create()
            .size(new Vector3(200, 1, 200))
            .position(new Vector3(0, 100, 0))
            .transparency(1)
            .spawn();
        
        sandEffect.Name = "SandstormEffect";
        this.weatherEffects.set("sandstorm", sandEffect);
        
        // Create sand particle effect
        const numSandParticles = math.floor(intensity * 200);
        this.createSandParticles(sandEffect, intensity, numSandParticles);
        
        print(`🏜️ Sandstorm effect created with intensity: ${intensity}`);
    }

    private createSandParticles(parent: Part, intensity: number, numParticles: number): void {
        // Create continuous sandstorm effect
        const sandstormLoop = () => {
            if (this.currentWeather !== "sandstorm") {
                return; // Stop if weather changed
            }

            // Create swirling sand particles that move horizontally at ground level
            for (let i = 0; i < math.floor(intensity * 20); i++) {
                const groundHeight = 10; // Stay near ground level
                
                const sandParticle = EffectPartBuilder.create()
                    .shape(Enum.PartType.Ball)
                    .size(new Vector3(1.2, 1.2, 1.2)) // Large, visible sand particles
                    .color(new Color3(0.9, 0.8, 0.6)) // More visible sand color
                    .material(Enum.Material.Sand)
                    .transparency(0.2) // More visible
                    .position(new Vector3(
                        math.random(-400, 400), // Wide sandstorm area
                        groundHeight + math.random(-5, 15), // Near ground level
                        math.random(-400, 400)
                    ))
                    .spawn();

                // Horizontal swirling sandstorm motion
                EffectTweenBuilder.for(sandParticle)
                    .move(new Vector3(
                        sandParticle.Position.X + math.random(-100, 100) * intensity, // Strong horizontal movement
                        groundHeight + math.random(-5, 25), // Stay near ground with some vertical drift
                        sandParticle.Position.Z + math.random(-100, 100) * intensity
                    ))
                    .duration(3) // Faster swirling movement
                    .easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
                    .onComplete(() => {
                        if (sandParticle.Parent) {
                            sandParticle.Destroy();
                        }
                    })
                    .play();

                // Clean up faster for continuous effect
                task.delay(4, () => {
                    if (sandParticle.Parent) {
                        sandParticle.Destroy();
                    }
                });
            }

            // Continue sandstorm loop
            task.wait(0.8); // Regular sandstorm intervals
            sandstormLoop();
        };

        // Start continuous sandstorm
        task.spawn(() => sandstormLoop());
    }

    private startLightningEffects(intensity: number): void {
        // Lightning flash effect using Core framework
        const lightningInterval = math.max(2, 10 - (intensity * 8)); // 2-10 seconds between flashes
        
        const createLightningBolt = () => {
            // Create lightning bolt visual effect
            const lightningBolt = EffectPartBuilder.create()
                .shape(Enum.PartType.Cylinder)
                .size(new Vector3(0.5, 100, 0.5))
                .color(new Color3(1, 1, 0.8))
                .material(Enum.Material.Neon)
                .transparency(0.1)
                .position(new Vector3(
                    math.random(-50, 50),
                    50,
                    math.random(-50, 50)
                ))
                .withLight(20, 10, new Color3(1, 1, 0.8))
                .spawn();

            // Flash effect with tween
            EffectTweenBuilder.for(lightningBolt)
                .fade(1)
                .duration(0.2)
                .onComplete(() => {
                    if (lightningBolt.Parent) {
                        lightningBolt.Destroy();
                    }
                })
                .play();

            // Play lightning sound
            const sound = new Instance("Sound");
            sound.SoundId = "rbxasset://sounds/electronicpingshort.wav";
            sound.Volume = 0.5 * intensity;
            sound.PlaybackSpeed = math.random(0.8, 1.2);
            sound.Parent = Workspace;
            sound.Play();
            sound.Ended.Connect(() => sound.Destroy());
        };
        
        const flashLightning = () => {
            // Brief bright flash
            const originalBrightness = Lighting.Brightness;
            
            // Create lightning bolt
            createLightningBolt();
            
            // Flash the sky using brightness method
            EffectTweenBuilder.for(Lighting)
                .brightness(2)
                .duration(0.1)
                .onComplete(() => {
                    EffectTweenBuilder.for(Lighting)
                        .brightness(originalBrightness)
                        .duration(0.1)
                        .play();
                })
                .play();
            
            // Schedule next lightning
            task.wait(lightningInterval + math.random() * 3);
            if (this.currentWeather === "thunderstorm") {
                flashLightning();
            }
        };
        
        // Start lightning effects
        task.spawn(() => flashLightning());
    }

    private clearWeatherEffects(): void {
        // Remove all weather effect instances
        for (const [key, effect] of this.weatherEffects) {
            if (effect && effect.Parent) {
                effect.Destroy();
            }
        }
        this.weatherEffects.clear();
    }

    /**
     * Cleanup all weather effects
     */
    public cleanup(): void {
        this.clearWeatherEffects();
        this.setClearWeather();
    }
}
