import * as React from "@rbxts/react";
import { Button, VerticalFrame } from "../../core";
import { WorldTestingPanel } from "./WorldTestingPanel";
import { DebugPanel } from "./DebugPanel";
import { ResponsiveManager } from "../../core/gui/layout/ResponsiveManager";

interface BottomLeftGridProps {
  onTestClick: () => void;
  onHelloClick: () => void;
}

export function BottomLeftGrid(_props: BottomLeftGridProps) {
  const [worldLabOpen, setWorldLabOpen] = React.useState(false);
  const [debugPanelOpen, setDebugPanelOpen] = React.useState(false);

  // Get responsive manager for dynamic positioning
  const responsiveManager = ResponsiveManager.getInstance();
  const safeAreaInsets = responsiveManager.getSafeAreaInsets();

  // Calculate responsive position and size
  const containerWidth = responsiveManager.isMobile() ? 100 : 120;
  const containerHeight = responsiveManager.isMobile() ? 160 : 200;
  const marginLeft = responsiveManager.getResponsiveMargin(16);
  const marginBottom = responsiveManager.getResponsiveMargin(20) + safeAreaInsets.bottom;

  return (
    <>
      <VerticalFrame
        backgroundTransparency={1}
        size={new UDim2(0, containerWidth, 0, containerHeight)}
        position={new UDim2(0, marginLeft, 1, -marginBottom)}
        anchorPoint={new Vector2(0, 1)}
        spacing={8}
        padding={0}
        responsive={true}
        responsiveMargin={true}
      >
 
        <Button
          text="🌍 World"
          onClick={() => setWorldLabOpen(true)}
          LayoutOrder={5}
        />

        <Button
          text="🔧 Debug"
          onClick={() => setDebugPanelOpen(true)}
          LayoutOrder={6}
        />
      </VerticalFrame>


      <WorldTestingPanel
        isOpen={worldLabOpen}
        onClose={() => setWorldLabOpen(false)}
      />

      <DebugPanel
        isOpen={debugPanelOpen}
        onClose={() => setDebugPanelOpen(false)}
      />
    </>
  );
}


