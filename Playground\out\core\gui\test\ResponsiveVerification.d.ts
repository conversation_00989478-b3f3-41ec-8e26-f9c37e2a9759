export declare class ResponsiveVerification {
    private responsiveManager;
    constructor();
    runAllTests(): void;
    private testScreenSizeDetection;
    private testDeviceTypeDetection;
    private testResponsivePositioning;
    private testResponsiveSizing;
    private testSafeAreaInsets;
    private testViewportChangeHandling;
    private testEdgeCases;
    simulateScreenSizes(): void;
}
export declare function runResponsiveTests(): void;
