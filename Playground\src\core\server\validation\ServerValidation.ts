import { Players } from "@rbxts/services";
import { ValidationRules } from "./interfaces/ValidationRules";
import { RangeValidationOptions } from "./interfaces/RangeValidationOptions";
import { PlayerStateValidation } from "./interfaces/PlayerStateValidation";

export class ServerValidation {
    private static playerCooldowns: Map<Player, Map<string, number>> = new Map();

    /**
     * Validate a player action with comprehensive checks
     */
    public static validatePlayerAction(
        player: Player,
        actionData: unknown,
        rules: ValidationRules
    ): { valid: boolean; reason?: string } {
        
        // Check if player has character
        if (rules.requireCharacter) {
            const character = player.Character;
            if (!character) {
                return { valid: false, reason: "Player has no character" };
            }

            // Check for HumanoidRootPart if required
            if (rules.requireHumanoidRootPart) {
                const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
                if (!humanoidRootPart) {
                    return { valid: false, reason: "Player has no HumanoidRootPart" };
                }
            }
        }

        // Range validation
        if (rules.maxRange !== undefined || rules.minRange !== undefined) {
            const rangeCheck = this.validateRange(player, actionData, rules);
            if (!rangeCheck.valid) {
                return rangeCheck;
            }
        }

        // Custom validation
        if (rules.customValidation) {
            if (!rules.customValidation(player, actionData)) {
                return { valid: false, reason: "Custom validation failed" };
            }
        }

        return { valid: true };
    }

    /**
     * Validate range between player and target position
     */
    public static validateRange(
        player: Player,
        actionData: unknown,
        rules: ValidationRules
    ): { valid: boolean; reason?: string } {
        const character = player.Character;
        if (!character) {
            return { valid: false, reason: "Player has no character for range validation" };
        }

        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (!humanoidRootPart) {
            return { valid: false, reason: "Player has no HumanoidRootPart for range validation" };
        }

        // Extract position from action data
        let targetPosition: Vector3;
        if (actionData && typeIs(actionData, "table")) {
            const actionObj = actionData as Record<string, unknown>;
            if (actionObj.position && typeIs(actionObj.position, "Vector3")) {
                targetPosition = actionObj.position;
            } else if (actionObj.targetPosition && typeIs(actionObj.targetPosition, "Vector3")) {
                targetPosition = actionObj.targetPosition;
            } else {
                return { valid: false, reason: "No position found in action data for range validation" };
            }
        } else {
            return { valid: false, reason: "Invalid action data for range validation" };
        }

        const distance = humanoidRootPart.Position.sub(targetPosition).Magnitude;

        // Check maximum range
        if (rules.maxRange !== undefined && distance > rules.maxRange) {
            return { 
                valid: false, 
                reason: `Target too far: ${math.floor(distance)} > ${rules.maxRange}` 
            };
        }

        // Check minimum range
        if (rules.minRange !== undefined && distance < rules.minRange) {
            return { 
                valid: false, 
                reason: `Target too close: ${math.floor(distance)} < ${rules.minRange}` 
            };
        }

        return { valid: true };
    }

    /**
     * Validate range between two specific positions
     */
    public static validateRangeBetweenPositions(options: RangeValidationOptions): { valid: boolean; reason?: string } {
        const distance = options.fromPosition.sub(options.toPosition).Magnitude;

        if (distance > options.maxRange) {
            return { 
                valid: false, 
                reason: `Distance too far: ${math.floor(distance)} > ${options.maxRange}` 
            };
        }

        if (options.minRange !== undefined && distance < options.minRange) {
            return { 
                valid: false, 
                reason: `Distance too close: ${math.floor(distance)} < ${options.minRange}` 
            };
        }

        return { valid: true };
    }

    /**
     * Validate player state (health, grounded, etc.)
     */
    public static validatePlayerState(
        player: Player, 
        validation: PlayerStateValidation
    ): { valid: boolean; reason?: string } {
        const character = player.Character;
        if (!character) {
            return { valid: false, reason: "Player has no character for state validation" };
        }

        const humanoid = character.FindFirstChild("Humanoid") as Humanoid;
        if (!humanoid) {
            return { valid: false, reason: "Player has no Humanoid for state validation" };
        }

        // Check if player is alive
        if (validation.requireAlive && humanoid.Health <= 0) {
            return { valid: false, reason: "Player is not alive" };
        }

        // Check health range
        if (validation.maxHealth !== undefined && humanoid.Health > validation.maxHealth) {
            return { valid: false, reason: `Player health too high: ${humanoid.Health} > ${validation.maxHealth}` };
        }

        if (validation.minHealth !== undefined && humanoid.Health < validation.minHealth) {
            return { valid: false, reason: `Player health too low: ${humanoid.Health} < ${validation.minHealth}` };
        }

        // Check if player is grounded (simplified check)
        if (validation.requireGrounded) {
            const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
            if (humanoidRootPart) {
                // Simple raycast downward to check if grounded
                const raycast = game.Workspace.Raycast(
                    humanoidRootPart.Position,
                    new Vector3(0, -10, 0)
                );
                
                if (!raycast) {
                    return { valid: false, reason: "Player is not grounded" };
                }
            }
        }

        return { valid: true };
    }

    /**
     * Validate cooldown for a specific action
     */
    public static validateCooldown(
        player: Player, 
        actionName: string, 
        cooldownTime: number
    ): { valid: boolean; reason?: string; remainingTime?: number } {
        let playerCooldowns = this.playerCooldowns.get(player);
        if (!playerCooldowns) {
            playerCooldowns = new Map();
            this.playerCooldowns.set(player, playerCooldowns);
        }

        const lastUsed = playerCooldowns.get(actionName);
        if (lastUsed !== undefined) {
            const timeSinceLastUse = tick() - lastUsed;
            if (timeSinceLastUse < cooldownTime) {
                const remainingTime = cooldownTime - timeSinceLastUse;
                return { 
                    valid: false, 
                    reason: `Action on cooldown for ${math.ceil(remainingTime)} seconds`,
                    remainingTime: remainingTime
                };
            }
        }

        return { valid: true };
    }

    /**
     * Set cooldown for a specific action
     */
    public static setCooldown(player: Player, actionName: string): void {
        let playerCooldowns = this.playerCooldowns.get(player);
        if (!playerCooldowns) {
            playerCooldowns = new Map();
            this.playerCooldowns.set(player, playerCooldowns);
        }

        playerCooldowns.set(actionName, tick());
    }

    /**
     * Clear all cooldowns for a player (useful for cleanup)
     */
    public static clearPlayerCooldowns(player: Player): void {
        this.playerCooldowns.delete(player);
    }

    /**
     * Initialize cleanup for when players leave
     */
    public static initializeCleanup(): void {
        Players.PlayerRemoving.Connect((player) => {
            this.clearPlayerCooldowns(player);
        });
    }

    /**
     * Get remaining cooldown time for an action
     */
    public static getRemainingCooldown(player: Player, actionName: string, cooldownTime: number): number {
        const playerCooldowns = this.playerCooldowns.get(player);
        if (!playerCooldowns) return 0;

        const lastUsed = playerCooldowns.get(actionName);
        if (lastUsed === undefined) return 0;

        const timeSinceLastUse = tick() - lastUsed;
        const remainingTime = cooldownTime - timeSinceLastUse;
        
        return math.max(0, remainingTime);
    }
}
