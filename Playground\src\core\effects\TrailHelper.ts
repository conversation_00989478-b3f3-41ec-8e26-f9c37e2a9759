export class TrailHelper {
    private trail: Trail;
    private attachment0: Attachment;
    private attachment1?: Attachment;

    constructor(parent: Instance) {
        // Create attachments
        this.attachment0 = new Instance("Attachment");
        this.attachment0.Parent = parent;

        // Create trail
        this.trail = new Instance("Trail");
        this.trail.Attachment0 = this.attachment0;
        this.trail.Attachment1 = this.attachment0; // Default to same attachment
        this.trail.Parent = parent;
    }

    static create(parent: Instance): TrailHelper {
        return new TrailHelper(parent);
    }

    color(color: Color3): this {
        this.trail.Color = new ColorSequence(color);
        return this;
    }

    transparency(transparency: number): this {
        this.trail.Transparency = new NumberSequence(transparency);
        return this;
    }

    lifetime(lifetime: number): this {
        this.trail.Lifetime = lifetime;
        return this;
    }

    width(width: number): this {
        this.trail.WidthScale = new NumberSequence(width);
        return this;
    }

    texture(textureId: string): this {
        this.trail.Texture = textureId;
        return this;
    }

    minLength(length: number): this {
        this.trail.MinLength = length;
        return this;
    }

    faceCamera(enabled = true): this {
        this.trail.FaceCamera = enabled;
        return this;
    }

    // Create a second attachment for more complex trails
    addSecondAttachment(offset = new Vector3(0, 0, 0)): this {
        if (this.attachment0.Parent) {
            this.attachment1 = new Instance("Attachment");
            this.attachment1.Position = offset;
            this.attachment1.Parent = this.attachment0.Parent;
            this.trail.Attachment1 = this.attachment1;
        }
        return this;
    }

    spawn(): Trail {
        return this.trail;
    }

    // Preset trail types
    static createProjectileTrail(parent: Instance, color = Color3.fromRGB(255, 100, 100)): Trail {
        return TrailHelper.create(parent)
            .color(color)
            .transparency(0.5)
            .lifetime(0.5)
            .width(1)
            .faceCamera(true)
            .spawn();
    }

    static createMagicTrail(parent: Instance, color = Color3.fromRGB(100, 255, 255)): Trail {
        return TrailHelper.create(parent)
            .color(color)
            .transparency(0.3)
            .lifetime(1)
            .width(2)
            .faceCamera(true)
            .spawn();
    }

    static createSmokeTrail(parent: Instance): Trail {
        return TrailHelper.create(parent)
            .color(Color3.fromRGB(128, 128, 128))
            .transparency(0.7)
            .lifetime(2)
            .width(3)
            .texture("rbxassetid://241650934") // Smoke texture
            .spawn();
    }
}
