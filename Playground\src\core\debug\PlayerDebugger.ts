import { Players } from "@rbxts/services";
import { DebugRenderer } from "./DebugRenderer";
import { DebugConfig } from "./DebugManager";
import { PositionHelper } from "../helper/PositionHelper";

export class PlayerDebugger {
	private renderer: DebugRenderer;
	private playerInfoLabel?: TextLabel;

	constructor(renderer: DebugRenderer) {
		this.renderer = renderer;
		this.setupGUI();
	}

	public update(config: DebugConfig): void {
		const players = Players.GetPlayers();
		
		// Update player info display
		this.updatePlayerInfo(players.size());

		// Debug each player
		players.forEach((player, index) => {
			this.debugPlayer(player, config, index);
		});
	}

	private setupGUI(): void {
		this.playerInfoLabel = this.renderer.createGUIElement(
			"PlayerInfo",
			UDim2.fromScale(1, 0).sub(UDim2.fromOffset(380, -380)), // Below AIDebugger
			UDim2.fromOffset(370, 180),
			"Player Debug Info"
		);
	}

	private updatePlayerInfo(playerCount: number): void {
		if (!this.playerInfoLabel) return;

		const localPlayer = Players.LocalPlayer;
		const info = [
			"=== PLAYER DEBUG INFO ===",
			`Total Players: ${playerCount}`,
			`Local Player: ${localPlayer?.Name || "None"}`,
			""
		];

		// Add individual player info
		Players.GetPlayers().forEach((player, index) => {
			if (player.Character && player.Character.PrimaryPart) {
				const pos = player.Character.PrimaryPart.Position;
				const humanoid = player.Character.FindFirstChild("Humanoid") as Humanoid;
				const health = humanoid ? `${math.floor(humanoid.Health)}/${math.floor(humanoid.MaxHealth)}` : "N/A";
				const walkSpeed = humanoid ? math.floor(humanoid.WalkSpeed) : "N/A";
				
				info.push(`${player.Name}:`);
				info.push(`  Pos: (${math.floor(pos.X)}, ${math.floor(pos.Y)}, ${math.floor(pos.Z)})`);
				info.push(`  Health: ${health}`);
				info.push(`  Speed: ${walkSpeed}`);
				info.push("");
			} else {
				info.push(`${player.Name}: No Character`);
				info.push("");
			}
		});

		this.playerInfoLabel.Text = info.join("\n");
	}

	private debugPlayer(player: Player, config: DebugConfig, index: number): void {
		if (!player.Character || !player.Character.PrimaryPart) return;

		const character = player.Character;
		const humanoidRootPart = character.PrimaryPart;
		const position = humanoidRootPart!.Position;
		const humanoid = character.FindFirstChild("Humanoid") as Humanoid;

		// Show player position and basic info
		if (config.showPositions) {
			const isLocalPlayer = player === Players.LocalPlayer;
			const playerColor = isLocalPlayer ? Color3.fromRGB(0, 255, 0) : Color3.fromRGB(0, 0, 255);
			
			this.renderer.drawSphere(position, 1.5, playerColor, 0.1);
			
			const playerInfo = [
				`Player: ${player.Name}`,
				`Pos: ${math.floor(position.X)}, ${math.floor(position.Y)}, ${math.floor(position.Z)}`,
				humanoid ? `Health: ${math.floor(humanoid.Health)}/${math.floor(humanoid.MaxHealth)}` : "",
				humanoid ? `Speed: ${math.floor(humanoid.WalkSpeed)}` : ""
			].filter(line => line !== "").join("\n");
			
			this.renderer.drawText(position.add(new Vector3(0, 4, 0)), playerInfo, Color3.fromRGB(255, 255, 255), 0.1);
		}

		// Show player movement vector
		if (config.showPlayers && humanoid) {
			const moveVector = humanoid.MoveDirection;
			if (moveVector.Magnitude > 0) {
				const endPos = position.add(moveVector.mul(5));
				this.renderer.drawLine(position, endPos, Color3.fromRGB(255, 255, 0), 0.1);
				this.renderer.drawText(endPos, "Movement", Color3.fromRGB(255, 255, 0), 0.1);
			}
		}

		// Show player look direction
		if (config.showPlayers) {
			const lookDirection = humanoidRootPart!.CFrame.LookVector;
			const lookEndPos = position.add(lookDirection.mul(3));
			this.renderer.drawLine(position, lookEndPos, Color3.fromRGB(255, 0, 0), 0.1);
		}

		// Show player's interaction range
		if (config.showPlayers) {
			this.renderer.drawSphere(position, 5, Color3.fromRGB(0, 255, 255), 0.1); // 5 stud interaction range
		}

		// Debug player's body parts (if enabled)
		if (config.showPositions) {
			this.debugPlayerBodyParts(character);
		}

		// Show player connections/relationships
		this.debugPlayerRelationships(player, position, config);
	}

	private debugPlayerBodyParts(character: Model): void {
		const bodyParts = [
			"Head", "Torso", "HumanoidRootPart",
			"Left Arm", "Right Arm", "Left Leg", "Right Leg"
		];

		bodyParts.forEach(partName => {
			const part = character.FindFirstChild(partName) as BasePart;
			if (part) {
				// Draw small markers for body parts
				this.renderer.drawSphere(part.Position, 0.3, Color3.fromRGB(255, 255, 255), 0.1);
				this.renderer.drawText(part.Position.add(new Vector3(0, 1, 0)), partName, Color3.fromRGB(200, 200, 200), 0.1);
			}
		});
	}

	private debugPlayerRelationships(player: Player, position: Vector3, config: DebugConfig): void {
		if (!config.showPlayers) return;

		// Show distance to other players
		Players.GetPlayers().forEach(otherPlayer => {
			if (otherPlayer === player || !otherPlayer.Character || !otherPlayer.Character.PrimaryPart) return;

			const otherPosition = otherPlayer.Character.PrimaryPart.Position;
			const distance = position.sub(otherPosition).Magnitude;

			// Only show if players are relatively close
			if (distance < 50) {
				const midpoint = position.add(otherPosition).div(2);
				const distanceText = `${math.floor(distance)} studs`;
				
				// Color based on distance (green = close, red = far)
				const normalizedDistance = math.min(distance / 50, 1);
				const lineColor = Color3.fromRGB(
					math.floor(255 * normalizedDistance),
					math.floor(255 * (1 - normalizedDistance)),
					0
				);
				
				this.renderer.drawLine(position, otherPosition, lineColor, 0.1);
				this.renderer.drawText(midpoint, distanceText, lineColor, 0.1);
			}
		});
	}

	// Debug player's current tool/equipment
	private debugPlayerEquipment(player: Player, position: Vector3): void {
		if (!player.Character) return;

		const tool = player.Character.FindFirstChildOfClass("Tool");
		if (tool) {
			const toolInfo = `Tool: ${tool.Name}`;
			this.renderer.drawText(position.add(new Vector3(0, -2, 0)), toolInfo, Color3.fromRGB(255, 255, 0), 0.1);
		}
	}

	// Debug player's current animation state
	private debugPlayerAnimations(player: Player, position: Vector3): void {
		if (!player.Character) return;

		const humanoid = player.Character.FindFirstChild("Humanoid") as Humanoid;
		if (!humanoid) return;

		// Get current animation tracks (this is simplified - actual implementation would be more complex)
		const animationInfo = [
			`Jump: ${humanoid.Jump ? "YES" : "NO"}`,
			`Sit: ${humanoid.Sit ? "YES" : "NO"}`,
			`PlatformStand: ${humanoid.PlatformStand ? "YES" : "NO"}`
		].join("\n");

		this.renderer.drawText(position.add(new Vector3(-3, 0, 0)), animationInfo, Color3.fromRGB(200, 200, 255), 0.1);
	}

	public cleanup(): void {
		// Cleanup any resources
	}
}
