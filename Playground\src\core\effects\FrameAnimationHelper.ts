import { RunService } from "@rbxts/services";

export class FrameAnimationHelper {
    private static activeAnimations = new Map<string, RBXScriptConnection>();

    static animate(
        id: string,
        duration: number,
        updateCallback: (progress: number, elapsed: number) => void,
        onComplete?: () => void,
        delay = 0
    ): RBXScriptConnection | undefined {
        // Stop any existing animation with the same ID
        this.stopAnimation(id);

        const startAnimation = (): RBXScriptConnection => {
            const startTime = tick();

            const connection = RunService.RenderStepped.Connect(() => {
                const elapsed = tick() - startTime;
                const progress = math.min(elapsed / duration, 1);

                updateCallback(progress, elapsed);

                if (progress >= 1) {
                    connection.Disconnect();
                    this.activeAnimations.delete(id);
                    if (onComplete) {
                        onComplete();
                    }
                }
            });

            this.activeAnimations.set(id, connection);
            return connection;
        };

        if (delay > 0) {
            task.delay(delay, () => startAnimation());
            return undefined; // Can't return connection for delayed animations
        } else {
            return startAnimation();
        }
    }

    static createConnection(
        updateCallback: (elapsed: number) => void,
        id?: string
    ): RBXScriptConnection {
        const startTime = tick();

        const connection = RunService.RenderStepped.Connect(() => {
            const elapsed = tick() - startTime;
            updateCallback(elapsed);
        });

        if (id) {
            this.activeAnimations.set(id, connection);
        }

        return connection;
    }

    static stopAnimation(id: string): void {
        const connection = this.activeAnimations.get(id);
        if (connection) {
            connection.Disconnect();
            this.activeAnimations.delete(id);
        }
    }

    static stopAllAnimations(): void {
        for (const [id, connection] of this.activeAnimations) {
            connection.Disconnect();
        }
        this.activeAnimations.clear();
    }

    static isAnimating(id: string): boolean {
        return this.activeAnimations.has(id);
    }
}
