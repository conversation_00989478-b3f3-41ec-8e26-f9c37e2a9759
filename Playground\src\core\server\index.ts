// Core Server Helpers - Universal utilities for any game project
// These helpers work for abilities, inventory, trading, chat, etc.

// Import new organized classes
import { NetworkSync } from "./networking/NetworkSync";
import { EffectReplication } from "./effects/EffectReplication";
import { ServerValidation } from "./validation/ServerValidation";
import { PlayerDataSync } from "./player/PlayerDataSync";

// Export classes with new names
export { NetworkSync as NetworkSyncHelper };
export { EffectReplication as EffectReplicationHelper };
export { ServerValidation as ServerValidationHelper };
export { PlayerDataSync as PlayerDataSyncHelper };

// Export networking types
export {
    type SyncOptions,
    type SyncedAction
} from "./networking/interfaces";

// Export effects types
export {
    type EffectData,
    type ReplicationOptions
} from "./effects/interfaces";

// Export validation types
export {
    type ValidationRules,
    type RangeValidationOptions,
    type PlayerStateValidation
} from "./validation/interfaces";

// Export player data types
export {
    type PlayerDataUpdate,
    type BroadcastUpdate,
    type SyncOptions as DataSyncOptions
} from "./player/interfaces";

/**
 * Initialize all Core server helpers
 * Call this once in your main server script
 */
export function initializeCoreServer(): void {
    NetworkSync.initializeCleanup();
    EffectReplication.initialize();
    ServerValidation.initializeCleanup();
    PlayerDataSync.initialize();

    print("🔧 Core Server helpers initialized successfully!");
}
