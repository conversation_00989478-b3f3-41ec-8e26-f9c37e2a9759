import * as React from "@rbxts/react";

interface OverlayProps {
  children?: React.ReactNode;
  onBackdropClick?: () => void;
  backgroundColor?: Color3;
  backgroundTransparency?: number;
  zIndex?: number;
  fullScreen?: boolean;
}

export function Overlay(props: OverlayProps): React.ReactElement {
  const backgroundColor = props.backgroundColor ?? Color3.fromRGB(0, 0, 0);
  const backgroundTransparency = props.backgroundTransparency ?? 0.5;
  const zIndex = props.zIndex ?? 10;
  const fullScreen = props.fullScreen ?? true;

  // For full screen overlays, we need to break out of parent constraints
  const frameProps = fullScreen ? {
    BackgroundTransparency: 1,
    Size: new UDim2(1, 0, 1, 0),
    Position: new UDim2(0, 0, 0, 0),
    AnchorPoint: new Vector2(0, 0),
    ZIndex: zIndex,
    // This helps ensure it covers the full screen
    ClipsDescendants: false
  } : {
    BackgroundTransparency: 1,
    Size: new UDim2(1, 0, 1, 0),
    Position: new UDim2(0, 0, 0, 0),
    AnchorPoint: new Vector2(0, 0),
    ZIndex: zIndex
  };

  return (
    <frame {...frameProps}>
      {/* Backdrop */}
      <textbutton
        BackgroundColor3={backgroundColor}
        BackgroundTransparency={backgroundTransparency}
        Size={new UDim2(1, 0, 1, 0)}
        Position={new UDim2(0, 0, 0, 0)}
        ZIndex={zIndex}
        Text=""
        AutoButtonColor={false}
        Event={{
          Activated: props.onBackdropClick
        }}
      />

      {props.children}
    </frame>
  );
}
