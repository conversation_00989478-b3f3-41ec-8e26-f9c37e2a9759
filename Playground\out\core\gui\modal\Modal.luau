-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local IconButton = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button").IconButton
local Label = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "label", "Label").Label
local _frame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame")
local ContainerFrame = _frame.ContainerFrame
local HorizontalFrame = _frame.HorizontalFrame
local Overlay = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "overlay", "Overlay").Overlay
local function Modal(props)
	local _condition = props.width
	if _condition == nil then
		_condition = 300
	end
	local width = _condition
	local _condition_1 = props.height
	if _condition_1 == nil then
		_condition_1 = 200
	end
	local height = _condition_1
	if not props.isOpen then
		return React.createElement(React.Fragment)
	end
	return React.createElement(Overlay, {
		onBackdropClick = props.onClose,
	}, React.createElement("textbutton", {
		Text = "",
		BackgroundColor3 = Color3.fromHex(COLORS.bg.base),
		Size = UDim2.new(0, width, 0, height),
		Position = UDim2.new(0.5, 0, 0.5, 0),
		AnchorPoint = Vector2.new(0.5, 0.5),
		ZIndex = 12,
		AutoButtonColor = false,
		BorderSizePixel = 0,
		Event = {
			Activated = function()
				-- Stop propagation by doing nothing - this prevents backdrop click
			end,
		},
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.border.l2),
		Thickness = 1,
	}), React.createElement(ContainerFrame, {
		backgroundTransparency = 1,
		size = UDim2.new(1, 0, 1, 0),
		padding = 0,
		borderThickness = 0,
		autoSize = Enum.AutomaticSize.None,
	}, React.createElement(HorizontalFrame, {
		backgroundColor = COLORS.bg.secondary,
		size = UDim2.new(1, 0, 0, 40),
		backgroundTransparency = 0,
		padding = SIZES.padding,
		spacing = 0,
		horizontalAlignment = Enum.HorizontalAlignment.Left,
		verticalAlignment = Enum.VerticalAlignment.Center,
	}, React.createElement(Label, {
		text = props.title,
		fontSize = SIZES.fontSize + 2,
		size = UDim2.new(1, -50, 1, 0),
		bold = true,
		layoutOrder = 1,
	}), React.createElement(IconButton, {
		icon = "X",
		onClick = props.onClose,
		layoutOrder = 2,
		size = UDim2.new(0, 30, 0, 30),
	})), React.createElement(ContainerFrame, {
		backgroundTransparency = 1,
		size = UDim2.new(1, 0, 1, -40),
		position = UDim2.new(0, 0, 0, 40),
		padding = SIZES.padding,
		borderThickness = 0,
		autoSize = Enum.AutomaticSize.None,
	}, props.children))))
end
return {
	Modal = Modal,
}
