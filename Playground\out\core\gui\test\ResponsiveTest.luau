-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local ContainerFrame = _core.ContainerFrame
local VerticalFrame = _core.VerticalFrame
local Button = _core.Button
local Label = _core.Label
local function ResponsiveTest(props)
	local screenInfo, setScreenInfo = React.useState("")
	local responsiveManager = ResponsiveManager:getInstance()
	-- Update screen info when component mounts or screen changes
	React.useEffect(function()
		local updateScreenInfo = function()
			local screenSize = responsiveManager:getScreenSize()
			local deviceType = responsiveManager:getDeviceType()
			local safeAreaInsets = responsiveManager:getSafeAreaInsets()
			local info = table.concat({ `Screen: {math.floor(screenSize.width)}x{math.floor(screenSize.height)}`, `Device: {deviceType}`, `Aspect: {math.floor(screenSize.aspectRatio * 100) / 100}`, `Safe Areas: T:{safeAreaInsets.top} B:{safeAreaInsets.bottom} L:{safeAreaInsets.left} R:{safeAreaInsets.right}`, `Mobile: {if responsiveManager:isMobile() then "Yes" else "No"}` }, "\n")
			setScreenInfo(info)
		end
		updateScreenInfo()
		-- Listen for screen size changes
		local unsubscribe = responsiveManager:onScreenSizeChange(updateScreenInfo)
		return unsubscribe
	end, { responsiveManager })
	if not props.isOpen then
		return React.createElement(React.Fragment)
	end
	return React.createElement(ContainerFrame, {
		size = UDim2.new(0, 400, 0, 300),
		position = UDim2.new(0.5, 0, 0.5, 0),
		anchorPoint = Vector2.new(0.5, 0.5),
		responsive = true,
		responsiveMargin = true,
		minSize = UDim2.new(0, 300, 0, 250),
		maxSize = UDim2.new(0, 500, 0, 400),
	}, React.createElement(VerticalFrame, {
		spacing = 12,
		padding = 16,
		responsive = true,
		responsiveMargin = true,
	}, React.createElement(Label, {
		text = "📱 Responsive Test Panel",
		fontSize = 16,
		bold = true,
		autoSize = true,
	}), React.createElement(ContainerFrame, {
		backgroundColor = "#2A2A2A",
		backgroundTransparency = 0,
		cornerRadius = 8,
		padding = 12,
		fitContent = true,
	}, React.createElement(Label, {
		text = screenInfo,
		fontSize = 12,
		textWrapped = true,
		autoSize = true,
		size = UDim2.new(1, 0, 0, 0),
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
		responsive = true,
	}, React.createElement(Button, {
		text = "📏 Responsive Button",
		onClick = function()
			return print("Responsive button clicked")
		end,
		responsive = true,
	}), React.createElement(Button, {
		text = "📱 Mobile Optimized",
		onClick = function()
			return print("Mobile button clicked")
		end,
		responsive = true,
	}), React.createElement(Button, {
		text = "🖥️ Desktop Sized",
		onClick = function()
			return print("Desktop button clicked")
		end,
		responsive = false,
	})), React.createElement(Button, {
		text = "❌ Close",
		onClick = function()
			local _result = props.onClose
			if _result ~= nil then
				_result = _result()
			end
			return _result
		end,
		responsive = true,
	})))
end
-- Test function to simulate different screen sizes
local function testResponsiveBehavior()
	local responsiveManager = ResponsiveManager:getInstance()
	print("=== RESPONSIVE BEHAVIOR TEST ===")
	-- Test screen size calculations
	local screenSize = responsiveManager:getScreenSize()
	print(`Current Screen: {screenSize.width}x{screenSize.height}`)
	print(`Device Type: {responsiveManager:getDeviceType()}`)
	print(`Is Mobile: {responsiveManager:isMobile()}`)
	-- Test responsive positioning
	local testPosition = responsiveManager:getRelativePosition({
		x = 100,
		y = 50,
	})
	print(`Relative Position for (100,50): {testPosition.X.Scale}, {testPosition.Y.Scale}`)
	-- Test responsive sizing
	local testSize = responsiveManager:getResponsiveSize({
		width = 200,
		height = 100,
	}, {
		width = 150,
		height = 80,
	}, {
		width = 300,
		height = 150,
	})
	print(`Responsive Size for 200x100: {testSize.X.Scale}, {testSize.Y.Scale}`)
	-- Test responsive margins
	local margin = responsiveManager:getResponsiveMargin(16)
	print(`Responsive Margin for 16px: {margin}px`)
	-- Test safe area insets
	local safeArea = responsiveManager:getSafeAreaInsets()
	print(`Safe Area Insets: Top:{safeArea.top} Bottom:{safeArea.bottom} Left:{safeArea.left} Right:{safeArea.right}`)
	print("=== TEST COMPLETE ===")
end
return {
	ResponsiveTest = ResponsiveTest,
	testResponsiveBehavior = testResponsiveBehavior,
}
