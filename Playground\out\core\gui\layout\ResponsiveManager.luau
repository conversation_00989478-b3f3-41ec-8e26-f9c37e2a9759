-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local UserInputService = _services.UserInputService
local Workspace = _services.Workspace
local ResponsiveManager
do
	ResponsiveManager = setmetatable({}, {
		__tostring = function()
			return "ResponsiveManager"
		end,
	})
	ResponsiveManager.__index = ResponsiveManager
	function ResponsiveManager.new(...)
		local self = setmetatable({}, ResponsiveManager)
		return self:constructor(...) or self
	end
	function ResponsiveManager:constructor()
		self.callbacks = {}
		self.breakpoints = {
			mobile = 768,
			tablet = 1024,
			desktop = 1024,
		}
		self.currentScreenSize = self:calculateScreenSize()
		self:setupViewportListener()
	end
	function ResponsiveManager:getInstance()
		if not ResponsiveManager.instance then
			ResponsiveManager.instance = ResponsiveManager.new()
		end
		return ResponsiveManager.instance
	end
	function ResponsiveManager:calculateScreenSize()
		-- Get the camera viewport size which represents the actual screen size
		local camera = Workspace.CurrentCamera
		if not camera then
			-- Fallback to default screen size
			return {
				width = 1920,
				height = 1080,
				aspectRatio = 1920 / 1080,
			}
		end
		local viewportSize = camera.ViewportSize
		local width = viewportSize.X
		local height = viewportSize.Y
		return {
			width = width,
			height = height,
			aspectRatio = width / height,
		}
	end
	function ResponsiveManager:setupViewportListener()
		-- Listen for camera viewport size changes
		local camera = Workspace.CurrentCamera
		if not camera then
			return nil
		end
		camera:GetPropertyChangedSignal("ViewportSize"):Connect(function()
			local newScreenSize = self:calculateScreenSize()
			-- Only update if size actually changed
			if newScreenSize.width ~= self.currentScreenSize.width or newScreenSize.height ~= self.currentScreenSize.height then
				self.currentScreenSize = newScreenSize
				self:notifyCallbacks()
			end
		end)
	end
	function ResponsiveManager:notifyCallbacks()
		local _exp = self.callbacks
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(callback)
			TS.try(function()
				callback(self.currentScreenSize)
			end, function(error)
				warn(`ResponsiveManager callback error: {error}`)
			end)
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	function ResponsiveManager:getScreenSize()
		local _object = table.clone(self.currentScreenSize)
		setmetatable(_object, nil)
		return _object
	end
	function ResponsiveManager:getDeviceType()
		local width = self.currentScreenSize.width
		if width < self.breakpoints.mobile then
			return "mobile"
		elseif width < self.breakpoints.tablet then
			return "tablet"
		else
			return "desktop"
		end
	end
	function ResponsiveManager:isMobile()
		return self:getDeviceType() == "mobile" or UserInputService.TouchEnabled
	end
	function ResponsiveManager:isTablet()
		return self:getDeviceType() == "tablet"
	end
	function ResponsiveManager:isDesktop()
		return self:getDeviceType() == "desktop"
	end
	function ResponsiveManager:onScreenSizeChange(callback)
		local _callbacks = self.callbacks
		local _callback = callback
		table.insert(_callbacks, _callback)
		-- Return unsubscribe function
		return function()
			local _callbacks_1 = self.callbacks
			local _callback_1 = callback
			local index = (table.find(_callbacks_1, _callback_1) or 0) - 1
			if index > -1 then
				-- Remove callback from array
				local _array = {}
				local _length = #_array
				local _array_1 = self.callbacks
				table.move(_array_1, 1, #_array_1, _length + 1, _array)
				local newCallbacks = _array
				table.remove(newCallbacks, index + 1)
				self.callbacks = newCallbacks
			end
		end
	end
	function ResponsiveManager:getRelativePosition(desiredPosition)
		local screenSize = self:getScreenSize()
		-- Convert pixel positions to scale values
		local scaleX = desiredPosition.x / screenSize.width
		local scaleY = desiredPosition.y / screenSize.height
		return UDim2.new(scaleX, 0, scaleY, 0)
	end
	function ResponsiveManager:getResponsiveSize(desiredSize, minSize, maxSize)
		local screenSize = self:getScreenSize()
		-- Convert pixel sizes to scale values
		local scaleWidth = desiredSize.width / screenSize.width
		local scaleHeight = desiredSize.height / screenSize.height
		-- Apply constraints if provided
		if minSize then
			local minScaleWidth = minSize.width / screenSize.width
			local minScaleHeight = minSize.height / screenSize.height
			scaleWidth = math.max(scaleWidth, minScaleWidth)
			scaleHeight = math.max(scaleHeight, minScaleHeight)
		end
		if maxSize then
			local maxScaleWidth = maxSize.width / screenSize.width
			local maxScaleHeight = maxSize.height / screenSize.height
			scaleWidth = math.min(scaleWidth, maxScaleWidth)
			scaleHeight = math.min(scaleHeight, maxScaleHeight)
		end
		return UDim2.new(scaleWidth, 0, scaleHeight, 0)
	end
	function ResponsiveManager:getResponsiveMargin(pixelMargin)
		local deviceType = self:getDeviceType()
		-- Adjust margin based on device type
		local multiplier = 1
		repeat
			if deviceType == "mobile" then
				multiplier = 0.8
				break
			end
			if deviceType == "tablet" then
				multiplier = 1.0
				break
			end
			if deviceType == "desktop" then
				multiplier = 1.2
				break
			end
		until true
		return pixelMargin * multiplier
	end
	function ResponsiveManager:getSafeAreaInsets()
		local deviceType = self:getDeviceType()
		if deviceType == "mobile" then
			-- Mobile devices have reserved areas for controls
			return {
				top = 0,
				bottom = 80,
				left = 80,
				right = 80,
			}
		end
		return {
			top = 0,
			bottom = 0,
			left = 0,
			right = 0,
		}
	end
end
return {
	ResponsiveManager = ResponsiveManager,
}
