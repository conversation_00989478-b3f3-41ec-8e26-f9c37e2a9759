export interface AIBehavior {
	name: string;
	priority: number;
	canExecute(context: AIContext): boolean;
	execute(context: AIContext): AIBehaviorResult;
	onEnter?(context: AIContext): void;
	onExit?(context: AIContext): void;
}

export interface AIContext {
	entityId: string;
	entity: Instance;
	position: Vector3;
	target?: Instance;
	targetPosition?: Vector3;
	blackboard: Record<string, unknown>;
	deltaTime: number;
}

export interface AIBehaviorResult {
	success: boolean;
	completed: boolean;
	nextBehavior?: string;
	data?: Record<string, unknown>;
}

export enum AIState {
	Idle = "Idle",
	Moving = "Moving",
	Following = "Following",
	Patrolling = "Patrolling",
	Attacking = "Attacking",
	Fleeing = "Fleeing",
	Investigating = "Investigating",
	Dead = "Dead"
}

export interface AIConfig {
	detectionRange: number;
	followRange: number;
	attackRange: number;
	moveSpeed: number;
	patrolRadius: number;
	reactionTime: number;
	aggroTime: number;
	memoryDuration: number;
}
