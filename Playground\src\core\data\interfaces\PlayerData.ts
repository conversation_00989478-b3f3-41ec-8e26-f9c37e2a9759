// Core player data that every game needs
export interface CorePlayerData {
	userId: number;
	lastLogin: number;
	playtime: number;
	createdAt: number;
	version: string; // For data migration
}

// Generic player data structure that games can extend
export interface BasePlayerData extends CorePlayerData {
	gameData: Record<string, unknown>; // Game-specific data
	settings: PlayerSettings;
	metadata: PlayerMetadata;
}

export interface PlayerSettings {
	musicVolume: number;
	sfxVolume: number;
	language: string;
	[key: string]: unknown; // Allow games to add custom settings
}

export interface PlayerMetadata {
	totalSessions: number;
	averageSessionTime: number;
	lastSaveTime: number;
	dataVersion: number;
	[key: string]: unknown; // Allow games to add custom metadata
}

// Helper type for games to define their own player data
export type GamePlayerData<T = Record<string, unknown>> = BasePlayerData & {
	gameData: T;
};
