import * as React from "@rbxts/react";
import { COLORS, SIZES, BORDER_RADIUS } from "../../design";
import { ContainerFrame } from "../frame";
import { ResponsiveManager } from "../layout/ResponsiveManager";

interface GridProps {
  rows: number;
  cols: number;
  children?: Array<React.Element>;
  layoutOrder?: number;
  cellType?: "button" | "label";
  responsive?: boolean; // Enable responsive cell sizing
  maxWidth?: number; // Maximum grid width
}

export function Grid(props: GridProps) {
  // Use larger cells for buttons, smaller for labels
  const isButtonGrid = props.cellType === "button";
  const responsiveManager = ResponsiveManager.getInstance();

  // Calculate responsive cell sizes
  let cellWidth: number;
  let cellHeight: number;

  if (props.responsive) {
    const screenSize = responsiveManager.getScreenSize();
    const deviceType = responsiveManager.getDeviceType();

    // Base sizes adjusted for device type
    const baseCellWidth = isButtonGrid ? 80 : SIZES.gridCell.width;
    const baseCellHeight = isButtonGrid ? 50 : SIZES.gridCell.height;

    // Scale based on device type
    const scaleFactor = deviceType === "mobile" ? 0.8 : deviceType === "tablet" ? 0.9 : 1.0;

    cellWidth = baseCellWidth * scaleFactor;
    cellHeight = baseCellHeight * scaleFactor;

    // Ensure grid doesn't exceed max width if specified
    if (props.maxWidth) {
      const maxCellWidth = (props.maxWidth - (5 * (props.cols - 1))) / props.cols;
      cellWidth = math.min(cellWidth, maxCellWidth);
    }
  } else {
    cellWidth = isButtonGrid ? 80 : SIZES.gridCell.width;
    cellHeight = isButtonGrid ? 50 : SIZES.gridCell.height;
  }

  const cellPadding = responsiveManager.getResponsiveMargin(5);
  const gridWidth = (cellWidth * props.cols) + (cellPadding * (props.cols - 1));
  const gridHeight = (cellHeight * props.rows) + (cellPadding * (props.rows - 1));

  return (
    <ContainerFrame
      backgroundTransparency={1}
      size={new UDim2(0, gridWidth, 0, gridHeight)}
      padding={0}
      borderThickness={0}
      layoutOrder={props.layoutOrder}
    >
      <uigridlayout
        CellPadding={new UDim2(0, cellPadding, 0, cellPadding)}
        CellSize={new UDim2(0, cellWidth, 0, cellHeight)}
        SortOrder={Enum.SortOrder.LayoutOrder}
      />

      {props.children?.map((child, index) => (
        <ContainerFrame
          key={`cell-${index}`}
          backgroundColor={isButtonGrid ? "transparent" : COLORS.bg.base}
          backgroundTransparency={isButtonGrid ? 1 : 0}
          cornerRadius={isButtonGrid ? 0 : BORDER_RADIUS.sm}
          borderColor={COLORS.border.l2}
          borderThickness={isButtonGrid ? 0 : 1}
          borderTransparency={0.2}
          padding={isButtonGrid ? 0 : 2}
          layoutOrder={index}
        >
          {child}
        </ContainerFrame>
      ))}
    </ContainerFrame>
  );
}