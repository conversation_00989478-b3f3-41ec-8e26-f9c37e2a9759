export declare function createAirCracks(position: Vector3): void;
export declare function createEnhancedAirCracks(position: Vector3, direction: Vector3): void;
export declare function createEnhancedGlassCracks(position: Vector3, direction: Vector3): void;
export declare function createGlassImpactCenter(position: Vector3): void;
export declare function createMainGlassCracks(position: Vector3, direction: Vector3): void;
export declare function createEnhancedGlassCrackLine(startPoint: Vector3, angle: number, maxLength: number, isMainCrack: boolean): void;
export declare function createEnhancedGlassWebPattern(position: Vector3): void;
export declare function createGlassFragments(position: Vector3, direction: Vector3): void;
export declare function createRealisticGlassBreaking(position: Vector3): void;
export declare function createIceImpactCrater(position: Vector3): void;
export declare function createExpandingIceCracks(impactPoint: Vector3): void;
export declare function createVerticalIceCrack(startPoint: Vector3, endPoint: Vector3, isMainCrack: boolean): void;
export declare function createIceCrackLine(startPoint: Vector3, angle: number, maxLength: number, isMainCrack: boolean): void;
export declare function createIceCrystals(centerPosition: Vector3): void;
export declare function createIceShards(centerPosition: Vector3): void;
export declare function createGlassCrackLine(startPoint: Vector3, angle: number, maxLength: number, isMainCrack: boolean, crackLines: Part[]): void;
export declare function createGlassWebConnections(impactPoint: Vector3, crackLines: Part[]): void;
export declare function createGlassWebSegment(pos1: Vector3, pos2: Vector3): void;
export declare function createGlassImpactCrater(position: Vector3): void;
export declare function animateCrackLine(nodes: Vector3[], delay: number, isMainCrack: boolean): void;
export declare function createCrackSegment(startPos: Vector3, endPos: Vector3, isMainCrack: boolean): void;
export declare function createGlassWebPattern(centerPoint: Vector3, existingNodes: Vector3[]): void;
export declare function createImpactFlash(position: Vector3): void;
export declare function createFlyingObjects(centerPosition: Vector3): void;
export declare function createDebrisObjects(centerPosition: Vector3): void;
export declare function createFlyingParticles(centerPosition: Vector3): void;
export declare function createCrackSparklingEffects(position: Vector3, direction: Vector3): void;
export declare function createCrackSparkles(position: Vector3): void;
export declare function createEnergyMotes(position: Vector3, direction: Vector3): void;
export declare function createShimmeringDust(position: Vector3): void;
export declare function createCrackGlitter(position: Vector3, direction: Vector3): void;
export declare function createFloatingSparkles(position: Vector3): void;
export declare function createGlassShards(centerPosition: Vector3): void;
export declare function createEnhancedShockwave(position: Vector3): void;
export declare function createRealisticAirFractures(centerPos: Vector3, direction: Vector3): void;
export declare function createImpactCenter(impactPoint: Vector3): void;
export declare function createRadiatingCracks(impactPoint: Vector3, direction: Vector3): void;
export declare function createCrackWeb(impactPoint: Vector3, direction: Vector3): void;
export declare function createGlassDust(position: Vector3): void;
export declare function createMassiveImpactFlash(position: Vector3): void;
export declare function createFloatingDebris(position: Vector3, direction: Vector3): void;
export declare function createDimensionalCracks(position: Vector3): void;
export declare function createAtmosphericDistortion(position: Vector3, direction: Vector3): void;
export declare function createEnergyLightning(position: Vector3): void;
export declare function createEnergyParticleStorm(position: Vector3): void;
export declare function createRealityShatter(position: Vector3, direction: Vector3): void;
