import { Players, ReplicatedStorage } from "@rbxts/services";
import { PlayerDataUpdate } from "./interfaces/PlayerDataUpdate";
import { BroadcastUpdate } from "./interfaces/BroadcastUpdate";
import { SyncOptions } from "./interfaces/SyncOptions";

export class PlayerDataSync {
    private static playerDataEvent?: RemoteEvent;
    private static playerBroadcastEvent?: RemoteEvent;
    private static playerDataCache: Map<Player, Map<string, any>> = new Map();

    /**
     * Initialize the player data sync system
     */
    public static initialize(): void {
        if (this.playerDataEvent) return; // Already initialized

        // Create or get RemoteEvents folder
        let remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
        if (!remoteEventsFolder) {
            remoteEventsFolder = new Instance("Folder");
            remoteEventsFolder.Name = "RemoteEvents";
            remoteEventsFolder.Parent = ReplicatedStorage;
        }

        // Create player data sync event
        this.playerDataEvent = new Instance("RemoteEvent");
        this.playerDataEvent.Name = "PlayerDataSync";
        this.playerDataEvent.Parent = remoteEventsFolder;

        // Create player broadcast event
        this.playerBroadcastEvent = new Instance("RemoteEvent");
        this.playerBroadcastEvent.Name = "PlayerBroadcast";
        this.playerBroadcastEvent.Parent = remoteEventsFolder;

        // Set up cleanup
        this.initializeCleanup();

        print("PlayerDataSync initialized");
    }

    /**
     * Sync player data to client(s)
     * Universal helper for health, mana, inventory, stats, etc.
     */
    public static syncPlayerData(
        player: Player, 
        dataKey: string, 
        newValue: any, 
        options: SyncOptions = {}
    ): void {
        this.ensureInitialized();

        // Validate data if validator provided
        if (options.validateData && !options.validateData(newValue)) {
            warn(`Invalid data for ${dataKey}: ${newValue}`);
            return;
        }

        // Update cache
        this.updatePlayerDataCache(player, dataKey, newValue);

        // Create update object
        const update: PlayerDataUpdate = {
            playerId: player.UserId,
            dataKey: dataKey,
            newValue: newValue,
            timestamp: tick()
        };

        // Send to appropriate targets
        if (options.broadcastToAll) {
            if (options.excludeOriginator) {
                // Send to all except the originator
                for (const otherPlayer of Players.GetPlayers()) {
                    if (otherPlayer !== player) {
                        this.playerDataEvent!.FireClient(otherPlayer, update);
                    }
                }
            } else {
                // Send to all players
                this.playerDataEvent!.FireAllClients(update);
            }
        } else if (options.broadcastToPlayer !== false) {
            // Default: send to the player themselves
            this.playerDataEvent!.FireClient(player, update);
        }
    }

    /**
     * Broadcast player update to other players
     * For things like player actions, status changes, etc.
     */
    public static broadcastPlayerUpdate(
        player: Player, 
        updateType: string, 
        data: any, 
        options: SyncOptions = {}
    ): void {
        this.ensureInitialized();

        const broadcast: BroadcastUpdate = {
            playerId: player.UserId,
            updateType: updateType,
            data: data,
            timestamp: tick()
        };

        if (options.excludeOriginator) {
            // Send to all except the originator
            for (const otherPlayer of Players.GetPlayers()) {
                if (otherPlayer !== player) {
                    this.playerBroadcastEvent!.FireClient(otherPlayer, broadcast);
                }
            }
        } else {
            // Send to all players
            this.playerBroadcastEvent!.FireAllClients(broadcast);
        }
    }

    /**
     * Sync multiple data keys at once for efficiency
     */
    public static syncPlayerDataBatch(
        player: Player, 
        dataUpdates: Record<string, any>, 
        options: SyncOptions = {}
    ): void {
        this.ensureInitialized();

        // Update cache for all keys
        for (const [key, value] of pairs(dataUpdates)) {
            this.updatePlayerDataCache(player, key, value);
        }

        const batchUpdate = {
            playerId: player.UserId,
            batchData: dataUpdates,
            timestamp: tick()
        };

        // Send batch update
        if (options.broadcastToAll) {
            if (options.excludeOriginator) {
                for (const otherPlayer of Players.GetPlayers()) {
                    if (otherPlayer !== player) {
                        this.playerDataEvent!.FireClient(otherPlayer, batchUpdate);
                    }
                }
            } else {
                this.playerDataEvent!.FireAllClients(batchUpdate);
            }
        } else if (options.broadcastToPlayer !== false) {
            this.playerDataEvent!.FireClient(player, batchUpdate);
        }
    }

    /**
     * Get cached player data
     */
    public static getPlayerData(player: Player, dataKey: string): any {
        const playerData = this.playerDataCache.get(player);
        if (!playerData) return undefined;
        
        return playerData.get(dataKey);
    }

    /**
     * Get all cached data for a player
     */
    public static getAllPlayerData(player: Player): Map<string, any> | undefined {
        return this.playerDataCache.get(player);
    }

    /**
     * Update player data cache
     */
    private static updatePlayerDataCache(player: Player, dataKey: string, newValue: any): void {
        let playerData = this.playerDataCache.get(player);
        if (!playerData) {
            playerData = new Map();
            this.playerDataCache.set(player, playerData);
        }

        playerData.set(dataKey, newValue);
    }

    /**
     * Send initial player data to a newly joined player
     */
    public static sendInitialDataToPlayer(newPlayer: Player): void {
        this.ensureInitialized();

        // Send all other players' data to the new player
        for (const [player, playerData] of this.playerDataCache) {
            if (player !== newPlayer && playerData.size() > 0) {
                const initialData = {
                    playerId: player.UserId,
                    initialData: playerData,
                    timestamp: tick()
                };
                
                this.playerDataEvent!.FireClient(newPlayer, initialData);
            }
        }
    }

    /**
     * Clean up player data when they leave
     */
    private static initializeCleanup(): void {
        Players.PlayerRemoving.Connect((player) => {
            this.playerDataCache.delete(player);
        });

        Players.PlayerAdded.Connect((player) => {
            // Send initial data to new player after a short delay
            task.delay(1, () => {
                this.sendInitialDataToPlayer(player);
            });
        });
    }

    /**
     * Ensure the helper is initialized
     */
    private static ensureInitialized(): void {
        if (!this.playerDataEvent) {
            this.initialize();
        }
    }

    /**
     * Get the RemoteEvents for advanced usage
     */
    public static getRemoteEvents(): { dataEvent?: RemoteEvent; broadcastEvent?: RemoteEvent } {
        return {
            dataEvent: this.playerDataEvent,
            broadcastEvent: this.playerBroadcastEvent
        };
    }
}
