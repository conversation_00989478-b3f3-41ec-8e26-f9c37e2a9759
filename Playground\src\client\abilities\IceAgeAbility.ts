import { AbilityBase } from "./AbilityBase";
import { TweenService, RunService, Workspace, Players, Lighting } from "@rbxts/services";

export class IceAgeAbility extends AbilityBase {
    private iceEffects: Part[] = [];
    private iceConnection?: RBXScriptConnection;
    private cooldownEndTime = 0;
    private isActive = false;
    private frozenObjects: Map<Instance, {
        originalMaterial: Enum.Material;
        originalColor: Color3;
        originalTransparency: number;
        iceShell?: Part;
    }> = new Map();
    private originalLighting: {
        Ambient: Color3;
        OutdoorAmbient: Color3;
        Brightness: number;
        ColorShift_Top: Color3;
    };

    constructor() {
        super("ICE_AGE", 30);
        
        // Store original lighting values
        this.originalLighting = {
            Ambient: Lighting.Ambient,
            OutdoorAmbient: Lighting.OutdoorAmbient,
            Brightness: Lighting.Brightness,
            ColorShift_Top: Lighting.ColorShift_Top
        };
    }

    public isOnCooldown(): boolean {
        return tick() < this.cooldownEndTime;
    }

    private startCooldown(): void {
        this.cooldownEndTime = tick() + this.getCooldownTime();
    }

    public activate(): void {
        if (this.isOnCooldown() || this.isActive) return;

        const player = Players.LocalPlayer;
        const character = player.Character;
        if (!character) return;

        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (!humanoidRootPart) return;

        print("❄️ Activating Aokiji's Ice Age - Freezing the world!");
        this.isActive = true;

        // Phase 1: Character ice preparation animation
        this.createIceAgeAnimation(character);

        // Phase 2: Character ice aura
        this.createCharacterIceAura(character);

        // Phase 2: Create expanding ice wave
        this.createExpandingIceWave(humanoidRootPart.Position);

        // Phase 3: Transform environment to ice
        this.createIceEnvironment();

        // Phase 4: Create ice atmosphere
        this.createIceAtmosphere();

        // Phase 5: Create floating ice crystals
        this.createFloatingIceCrystals();

        // Phase 6: Create ice spears
        this.createIceSpears();

        // Phase 7: Freeze all objects on the map
        this.freezeAllObjects();

        // Duration: 12 seconds
        task.delay(12, () => {
            this.cleanupEffects();
            this.isActive = false;
        });

        this.startCooldown();
    }

    private createIceAgeAnimation(character: Model): void {
        print("🧊 Creating Aokiji's Ice Age animation");

        const humanoid = character.FindFirstChild("Humanoid") as Humanoid;
        if (!humanoid) return;

        // Create proper Motor6D animation like QuakeAbility
        this.createCharacterAnimation(character);
    }

    private createCharacterAnimation(character: Model): void {
        const torso = character.FindFirstChild("Torso") as Part;
        const upperTorso = character.FindFirstChild("UpperTorso") as Part;

        if (torso) {
            // R6 character
            this.animateR6Character(torso);
        } else if (upperTorso) {
            // R15 character
            this.animateR15Character(upperTorso, character);
        }
    }

    private animateR6Character(torso: Part): void {
        const rightShoulder = torso.FindFirstChild("Right Shoulder") as Motor6D;
        const leftShoulder = torso.FindFirstChild("Left Shoulder") as Motor6D;
        const rightHip = torso.FindFirstChild("Right Hip") as Motor6D;
        const leftHip = torso.FindFirstChild("Left Hip") as Motor6D;
        const neck = torso.FindFirstChild("Neck") as Motor6D;

        if (!rightShoulder || !leftShoulder) return;

        // Store original C0 values
        const originalRightC0 = rightShoulder.C0;
        const originalLeftC0 = leftShoulder.C0;
        const originalRightHipC0 = rightHip ? rightHip.C0 : undefined;
        const originalLeftHipC0 = leftHip ? leftHip.C0 : undefined;
        const originalNeckC0 = neck ? neck.C0 : undefined;

        // Phase 1: Ice preparation stance (0-1s) - Arms spread wide
        print("❄️ Ice preparation stance");

        const rightArmPrep = TweenService.Create(rightShoulder, new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
            C0: originalRightC0.mul(CFrame.Angles(-math.pi/4, 0, -math.pi/2))
        });
        const leftArmPrep = TweenService.Create(leftShoulder, new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
            C0: originalLeftC0.mul(CFrame.Angles(-math.pi/4, 0, math.pi/2))
        });

        rightArmPrep.Play();
        leftArmPrep.Play();

        // Phase 2: Ice channeling (1-2s) - Arms down, channeling cold
        task.delay(1, () => {
            print("🧊 Ice channeling pose");

            const rightArmChannel = TweenService.Create(rightShoulder, new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                C0: originalRightC0.mul(CFrame.Angles(math.pi/3, 0, -math.pi/6))
            });
            const leftArmChannel = TweenService.Create(leftShoulder, new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                C0: originalLeftC0.mul(CFrame.Angles(math.pi/3, 0, math.pi/6))
            });

            rightArmChannel.Play();
            leftArmChannel.Play();
        });

        // Phase 3: Ice release (2-3s) - Dramatic arm sweep
        task.delay(2, () => {
            print("❄️ Ice release gesture!");

            const rightArmRelease = TweenService.Create(rightShoulder, new TweenInfo(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
                C0: originalRightC0.mul(CFrame.Angles(-math.pi/2, 0, -math.pi/3))
            });
            const leftArmRelease = TweenService.Create(leftShoulder, new TweenInfo(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
                C0: originalLeftC0.mul(CFrame.Angles(-math.pi/2, 0, math.pi/3))
            });

            rightArmRelease.Play();
            leftArmRelease.Play();

            // Add leg stance for power
            if (rightHip && leftHip && originalRightHipC0 && originalLeftHipC0) {
                const rightLegStance = TweenService.Create(rightHip, new TweenInfo(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
                    C0: originalRightHipC0.mul(CFrame.Angles(0, 0, math.pi/12))
                });
                const leftLegStance = TweenService.Create(leftHip, new TweenInfo(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
                    C0: originalLeftHipC0.mul(CFrame.Angles(0, 0, -math.pi/12))
                });

                rightLegStance.Play();
                leftLegStance.Play();
            }
        });

        // Phase 4: Return to normal (10s) - Restore all joints
        task.delay(10, () => {
            print("🧊 Returning to normal stance");

            const rightArmReturn = TweenService.Create(rightShoulder, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                C0: originalRightC0
            });
            const leftArmReturn = TweenService.Create(leftShoulder, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                C0: originalLeftC0
            });

            rightArmReturn.Play();
            leftArmReturn.Play();

            // Restore legs
            if (rightHip && leftHip && originalRightHipC0 && originalLeftHipC0) {
                const rightLegReturn = TweenService.Create(rightHip, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                    C0: originalRightHipC0
                });
                const leftLegReturn = TweenService.Create(leftHip, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                    C0: originalLeftHipC0
                });

                rightLegReturn.Play();
                leftLegReturn.Play();
            }
        });
    }

    private animateR15Character(upperTorso: Part, character: Model): void {
        const rightShoulder = upperTorso.FindFirstChild("RightShoulder") as Motor6D;
        const leftShoulder = upperTorso.FindFirstChild("LeftShoulder") as Motor6D;
        const lowerTorso = character.FindFirstChild("LowerTorso") as Part;

        if (!rightShoulder || !leftShoulder) return;

        // Store original C0 values
        const originalRightC0 = rightShoulder.C0;
        const originalLeftC0 = leftShoulder.C0;

        // Similar animation sequence for R15
        print("❄️ R15 Ice Age animation");

        // Phase 1: Preparation
        const rightArmPrep = TweenService.Create(rightShoulder, new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
            C0: originalRightC0.mul(CFrame.Angles(-math.pi/4, 0, -math.pi/2))
        });
        const leftArmPrep = TweenService.Create(leftShoulder, new TweenInfo(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
            C0: originalLeftC0.mul(CFrame.Angles(-math.pi/4, 0, math.pi/2))
        });

        rightArmPrep.Play();
        leftArmPrep.Play();

        // Phase 2: Channeling (1s delay)
        task.delay(1, () => {
            const rightArmChannel = TweenService.Create(rightShoulder, new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                C0: originalRightC0.mul(CFrame.Angles(math.pi/3, 0, -math.pi/6))
            });
            const leftArmChannel = TweenService.Create(leftShoulder, new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
                C0: originalLeftC0.mul(CFrame.Angles(math.pi/3, 0, math.pi/6))
            });

            rightArmChannel.Play();
            leftArmChannel.Play();
        });

        // Phase 3: Release (2s delay)
        task.delay(2, () => {
            const rightArmRelease = TweenService.Create(rightShoulder, new TweenInfo(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
                C0: originalRightC0.mul(CFrame.Angles(-math.pi/2, 0, -math.pi/3))
            });
            const leftArmRelease = TweenService.Create(leftShoulder, new TweenInfo(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
                C0: originalLeftC0.mul(CFrame.Angles(-math.pi/2, 0, math.pi/3))
            });

            rightArmRelease.Play();
            leftArmRelease.Play();
        });

        // Phase 4: Return (10s delay)
        task.delay(10, () => {
            const rightArmReturn = TweenService.Create(rightShoulder, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                C0: originalRightC0
            });
            const leftArmReturn = TweenService.Create(leftShoulder, new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
                C0: originalLeftC0
            });

            rightArmReturn.Play();
            leftArmReturn.Play();
        });
    }

    private createCharacterIceAura(character: Model): void {
        print("❄️ Creating Aokiji's ice aura");

        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (!humanoidRootPart) return;

        // Create ice aura around character
        const iceAura = new Instance("Part");
        iceAura.Name = "IceAura";
        iceAura.Shape = Enum.PartType.Ball;
        iceAura.Size = new Vector3(12, 12, 12);
        iceAura.Color = Color3.fromRGB(150, 200, 255); // Light blue ice
        iceAura.Material = Enum.Material.Ice;
        iceAura.Transparency = 0.6;
        iceAura.CanCollide = false;
        iceAura.Anchored = true;
        iceAura.Position = humanoidRootPart.Position;
        iceAura.Parent = Workspace;
        this.iceEffects.push(iceAura);

        // Add cold blue light
        const auraLight = new Instance("PointLight");
        auraLight.Color = Color3.fromRGB(150, 200, 255);
        auraLight.Brightness = 4;
        auraLight.Range = 25;
        auraLight.Parent = iceAura;

        // Pulsing effect
        const pulseTween = TweenService.Create(
            iceAura,
            new TweenInfo(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
            { 
                Size: new Vector3(15, 15, 15),
                Transparency: 0.4
            }
        );
        pulseTween.Play();

        // Follow character
        const followConnection = RunService.Heartbeat.Connect(() => {
            if (humanoidRootPart.Parent && iceAura.Parent) {
                iceAura.Position = humanoidRootPart.Position;
            }
        });

        // Cleanup after duration
        task.delay(12, () => {
            followConnection.Disconnect();
            pulseTween.Cancel();
            
            const fadeTween = TweenService.Create(
                iceAura,
                new TweenInfo(2, Enum.EasingStyle.Quad),
                { Transparency: 1 }
            );
            fadeTween.Play();
            fadeTween.Completed.Connect(() => {
                iceAura.Destroy();
                const index = this.iceEffects.indexOf(iceAura);
                if (index > -1) {
                    this.iceEffects.remove(index);
                }
            });
        });
    }

    private createExpandingIceWave(centerPosition: Vector3): void {
        print("🌊 Creating expanding ice wave");

        // Create multiple expanding ice rings
        for (let ring = 0; ring < 5; ring++) {
            task.delay(ring * 0.3, () => {
                const iceRing = new Instance("Part");
                iceRing.Name = "IceWaveRing";
                iceRing.Shape = Enum.PartType.Cylinder;
                iceRing.Size = new Vector3(0.5, 10, 10);
                iceRing.Color = Color3.fromRGB(200, 230, 255);
                iceRing.Material = Enum.Material.Ice;
                iceRing.Transparency = 0.3;
                iceRing.CanCollide = false;
                iceRing.Anchored = true;
                iceRing.Position = centerPosition.add(new Vector3(0, 0.5, 0));
                iceRing.CFrame = iceRing.CFrame.mul(CFrame.Angles(0, 0, math.pi / 2)); // Rotate to lay flat
                iceRing.Parent = Workspace;
                this.iceEffects.push(iceRing);

                // Expand the ring outward
                const expandTween = TweenService.Create(
                    iceRing,
                    new TweenInfo(3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                    { 
                        Size: new Vector3(0.5, 200, 200),
                        Transparency: 0.8
                    }
                );
                expandTween.Play();

                // Remove after expansion
                expandTween.Completed.Connect(() => {
                    iceRing.Destroy();
                    const index = this.iceEffects.indexOf(iceRing);
                    if (index > -1) {
                        this.iceEffects.remove(index);
                    }
                });
            });
        }
    }

    private createIceEnvironment(): void {
        print("🧊 Creating ice environment transformation");

        // Create ice floor patches across the map
        for (let i = 0; i < 25; i++) {
            task.delay(i * 0.2, () => {
                const randomX = math.random(-150, 150);
                const randomZ = math.random(-150, 150);
                const icePosition = new Vector3(randomX, 0.5, randomZ);

                const iceFloor = new Instance("Part");
                iceFloor.Name = "IceFloor";
                iceFloor.Shape = Enum.PartType.Block;
                iceFloor.Size = new Vector3(
                    math.random(8, 15),
                    1,
                    math.random(8, 15)
                );
                iceFloor.Color = Color3.fromRGB(220, 240, 255);
                iceFloor.Material = Enum.Material.Ice;
                iceFloor.Transparency = 0.2;
                iceFloor.CanCollide = true;
                iceFloor.Anchored = true;
                iceFloor.Position = icePosition;
                iceFloor.Parent = Workspace;
                this.iceEffects.push(iceFloor);

                // Add frost effect
                const frostDecal = new Instance("Decal");
                frostDecal.Texture = "rbxasset://textures/face.png"; // Placeholder - would use ice texture
                frostDecal.Face = Enum.NormalId.Top;
                frostDecal.Transparency = 0.7;
                frostDecal.Color3 = Color3.fromRGB(200, 230, 255);
                frostDecal.Parent = iceFloor;
            });
        }
    }

    private createIceAtmosphere(): void {
        print("❄️ Creating ice atmosphere");

        // Change lighting to cold ice atmosphere
        const lightingTween = TweenService.Create(
            Lighting,
            new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            {
                Ambient: Color3.fromRGB(100, 150, 200), // Cold blue ambient
                OutdoorAmbient: Color3.fromRGB(120, 170, 220), // Cold blue outdoor
                Brightness: 1.5, // Brighter for ice reflection
                ColorShift_Top: Color3.fromRGB(150, 200, 255) // Blue color shift
            }
        );
        lightingTween.Play();

        // Create atmospheric fog effect
        const atmosphere = Lighting.FindFirstChild("Atmosphere") as Atmosphere;
        if (atmosphere) {
            const originalAtmosphere = {
                Color: atmosphere.Color,
                Haze: atmosphere.Haze,
                Glare: atmosphere.Glare
            };

            const atmosphereTween = TweenService.Create(
                atmosphere,
                new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                {
                    Color: Color3.fromRGB(200, 230, 255), // Cold blue atmosphere
                    Haze: 1.5,
                    Glare: 0.5
                }
            );
            atmosphereTween.Play();

            // Restore atmosphere after duration
            task.delay(10, () => {
                const restoreAtmosphereTween = TweenService.Create(
                    atmosphere,
                    new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                    originalAtmosphere
                );
                restoreAtmosphereTween.Play();
            });
        }

        // Restore lighting after duration
        task.delay(10, () => {
            const restoreLightingTween = TweenService.Create(
                Lighting,
                new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                this.originalLighting
            );
            restoreLightingTween.Play();
        });
    }

    private createFloatingIceCrystals(): void {
        print("💎 Creating floating ice crystals");

        // Create floating ice crystals across the map
        for (let i = 0; i < 20; i++) {
            task.delay(i * 0.3, () => {
                const randomPosition = new Vector3(
                    math.random(-100, 100),
                    math.random(10, 40),
                    math.random(-100, 100)
                );

                const crystal = new Instance("Part");
                crystal.Name = "IceCrystal";
                crystal.Shape = Enum.PartType.Block;
                crystal.Size = new Vector3(
                    math.random(1, 3),
                    math.random(3, 6),
                    math.random(1, 3)
                );
                crystal.Color = Color3.fromRGB(180, 220, 255);
                crystal.Material = Enum.Material.Ice;
                crystal.Transparency = 0.1;
                crystal.CanCollide = false;
                crystal.Anchored = true;
                crystal.Position = randomPosition;
                
                // Random crystal rotation
                crystal.CFrame = crystal.CFrame.mul(CFrame.Angles(
                    math.random() * math.pi,
                    math.random() * math.pi * 2,
                    math.random() * math.pi
                ));

                crystal.Parent = Workspace;
                this.iceEffects.push(crystal);

                // Add cold light
                const crystalLight = new Instance("PointLight");
                crystalLight.Color = Color3.fromRGB(150, 200, 255);
                crystalLight.Brightness = 2;
                crystalLight.Range = 12;
                crystalLight.Parent = crystal;

                // Floating animation
                const floatTween = TweenService.Create(
                    crystal,
                    new TweenInfo(4, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
                    { 
                        Position: randomPosition.add(new Vector3(0, 3, 0))
                    }
                );
                floatTween.Play();

                // Slow rotation
                const rotateTween = TweenService.Create(
                    crystal,
                    new TweenInfo(6, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1),
                    { 
                        CFrame: crystal.CFrame.mul(CFrame.Angles(0, math.pi * 2, 0))
                    }
                );
                rotateTween.Play();
            });
        }
    }

    private createIceSpears(): void {
        print("🗡️ Creating ice spears");

        // Create ice spears that shoot up from the ground
        for (let i = 0; i < 15; i++) {
            task.delay(i * 0.4, () => {
                const randomPosition = new Vector3(
                    math.random(-80, 80),
                    0,
                    math.random(-80, 80)
                );

                const spear = new Instance("Part");
                spear.Name = "IceSpear";
                spear.Shape = Enum.PartType.Block;
                spear.Size = new Vector3(1, 0.5, 1); // Start small
                spear.Color = Color3.fromRGB(200, 230, 255);
                spear.Material = Enum.Material.Ice;
                spear.Transparency = 0.1;
                spear.CanCollide = false;
                spear.Anchored = true;
                spear.Position = randomPosition;
                spear.Parent = Workspace;
                this.iceEffects.push(spear);

                // Add sharp light
                const spearLight = new Instance("PointLight");
                spearLight.Color = Color3.fromRGB(150, 200, 255);
                spearLight.Brightness = 3;
                spearLight.Range = 10;
                spearLight.Parent = spear;

                // Shoot up animation
                const shootTween = TweenService.Create(
                    spear,
                    new TweenInfo(0.8, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
                    {
                        Size: new Vector3(1.5, 8, 1.5),
                        Position: randomPosition.add(new Vector3(0, 4, 0))
                    }
                );
                shootTween.Play();

                // Create ice impact effect
                const impactEffect = new Instance("Part");
                impactEffect.Name = "IceImpact";
                impactEffect.Shape = Enum.PartType.Ball;
                impactEffect.Size = new Vector3(0.5, 0.5, 0.5);
                impactEffect.Color = Color3.fromRGB(255, 255, 255);
                impactEffect.Material = Enum.Material.Neon;
                impactEffect.Transparency = 0.2;
                impactEffect.CanCollide = false;
                impactEffect.Anchored = true;
                impactEffect.Position = randomPosition;
                impactEffect.Parent = Workspace;
                this.iceEffects.push(impactEffect);

                // Impact expansion
                const impactTween = TweenService.Create(
                    impactEffect,
                    new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                    {
                        Size: new Vector3(6, 6, 6),
                        Transparency: 1
                    }
                );
                impactTween.Play();
                impactTween.Completed.Connect(() => impactEffect.Destroy());
            });
        }
    }

    private cleanupEffects(): void {
        print("🧹 Cleaning up Ice Age effects");

        // Clean up all ice effects
        for (const effect of this.iceEffects) {
            if (effect.Parent) {
                // Fade out effect
                const fadeTween = TweenService.Create(
                    effect,
                    new TweenInfo(2, Enum.EasingStyle.Quad),
                    { Transparency: 1 }
                );
                fadeTween.Play();
                fadeTween.Completed.Connect(() => effect.Destroy());
            }
        }
        this.iceEffects = [];

        // Disconnect any remaining connections
        if (this.iceConnection) {
            this.iceConnection.Disconnect();
            this.iceConnection = undefined;
        }

        // Restore all frozen objects
        this.restoreAllFrozenObjects();
    }

    private freezeAllObjects(): void {
        print("🧊 Freezing all objects on the map");

        // Get all parts in the workspace
        const allParts = this.getAllPartsInWorkspace();

        for (const part of allParts) {
            // Skip if it's a player character part or already an ice effect
            if (this.shouldSkipPart(part)) continue;

            // Store original properties
            this.frozenObjects.set(part, {
                originalMaterial: part.Material,
                originalColor: part.Color,
                originalTransparency: part.Transparency
            });

            // Apply ice transformation
            this.applyIceTransformation(part);
        }

        print(`❄️ Froze ${this.frozenObjects.size()} objects on the map`);
    }

    private getAllPartsInWorkspace(): Part[] {
        const parts: Part[] = [];

        const scanDescendants = (parent: Instance): void => {
            for (const child of parent.GetChildren()) {
                if (child.IsA("Part")) {
                    parts.push(child);
                }
                // Recursively scan children
                scanDescendants(child);
            }
        };

        scanDescendants(Workspace);
        return parts;
    }

    private shouldSkipPart(part: Part): boolean {
        // Skip player character parts
        const character = part.FindFirstAncestorOfClass("Model");
        if (character && Players.GetPlayerFromCharacter(character)) {
            return true;
        }

        // Skip our own ice effects
        if (part.Name.find("Ice")[0] || part.Name.find("Haki")[0]) {
            return true;
        }

        // Skip terrain and other special parts
        if (part.Name === "Terrain" || part.Name === "SpawnLocation") {
            return true;
        }

        // Skip very small parts (likely decorative)
        const size = part.Size;
        if (size.X < 0.5 && size.Y < 0.5 && size.Z < 0.5) {
            return true;
        }

        return false;
    }

    private applyIceTransformation(part: Part): void {
        // Transform the part to look frozen
        part.Material = Enum.Material.Ice;
        part.Color = Color3.fromRGB(200, 230, 255); // Light blue ice color
        part.Transparency = math.min(part.Transparency + 0.2, 0.8); // Make slightly more transparent

        // Create ice shell around the object
        this.createObjectIceShell(part);

        // Add frost particles if the part is large enough
        if (part.Size.Magnitude > 5) {
            this.addFrostParticles(part);
        }
    }

    private createObjectIceShell(part: Part): void {
        // Create ice shell around the frozen object
        const iceShell = new Instance("Part");
        iceShell.Name = "ObjectIceShell";
        iceShell.Shape = part.Shape === Enum.PartType.Ball ? Enum.PartType.Ball : Enum.PartType.Block;
        iceShell.Size = part.Size.add(new Vector3(0.5, 0.5, 0.5)); // Slightly larger
        iceShell.Color = Color3.fromRGB(180, 220, 255);
        iceShell.Material = Enum.Material.Ice;
        iceShell.Transparency = 0.6;
        iceShell.CanCollide = false;
        iceShell.Anchored = true;
        iceShell.CFrame = part.CFrame;
        iceShell.Parent = Workspace;
        this.iceEffects.push(iceShell);

        // Store reference to ice shell
        const frozenData = this.frozenObjects.get(part);
        if (frozenData) {
            frozenData.iceShell = iceShell;
        }

        // Add cold light
        const iceLight = new Instance("PointLight");
        iceLight.Color = Color3.fromRGB(150, 200, 255);
        iceLight.Brightness = 1;
        iceLight.Range = 8;
        iceLight.Parent = iceShell;

        // Follow the original part if it moves
        const followConnection = RunService.Heartbeat.Connect(() => {
            if (part.Parent && iceShell.Parent) {
                iceShell.CFrame = part.CFrame;
            } else {
                followConnection.Disconnect();
            }
        });

        // Cleanup connection after duration
        task.delay(12, () => {
            followConnection.Disconnect();
        });
    }

    private addFrostParticles(part: Part): void {
        // Create frost particle effect for large objects
        const attachment = new Instance("Attachment");
        attachment.Name = "FrostAttachment";
        attachment.Parent = part;

        // Create particle emitter for frost effect
        const particles = new Instance("ParticleEmitter");
        particles.Name = "FrostParticles";
        particles.Texture = "rbxasset://textures/particles/sparkles_main.dds";
        particles.Color = new ColorSequence(Color3.fromRGB(200, 230, 255));
        particles.Size = new NumberSequence(0.2, 0.5);
        particles.Lifetime = new NumberRange(2, 4);
        particles.Rate = 20;
        particles.SpreadAngle = new Vector2(45, 45);
        particles.Speed = new NumberRange(2, 5);
        particles.Parent = attachment;

        // Stop particles after duration
        task.delay(12, () => {
            particles.Enabled = false;
            task.delay(5, () => {
                if (attachment.Parent) {
                    attachment.Destroy();
                }
            });
        });
    }

    private restoreAllFrozenObjects(): void {
        print("🔥 Restoring all frozen objects");

        for (const [part, frozenData] of this.frozenObjects) {
            if (part.Parent && part.IsA("Part")) {
                const partInstance = part as Part;

                // Restore original properties with smooth transition
                const restoreTween = TweenService.Create(
                    partInstance,
                    new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
                    {
                        Color: frozenData.originalColor,
                        Transparency: frozenData.originalTransparency
                    }
                );
                restoreTween.Play();

                // Restore material immediately (can't tween enums)
                partInstance.Material = frozenData.originalMaterial;

                // Remove ice shell
                if (frozenData.iceShell && frozenData.iceShell.Parent) {
                    const shellFadeTween = TweenService.Create(
                        frozenData.iceShell,
                        new TweenInfo(2, Enum.EasingStyle.Quad),
                        { Transparency: 1 }
                    );
                    shellFadeTween.Play();
                    shellFadeTween.Completed.Connect(() => {
                        if (frozenData.iceShell && frozenData.iceShell.Parent) {
                            frozenData.iceShell.Destroy();
                        }
                    });
                }

                // Remove frost particles
                const frostAttachment = partInstance.FindFirstChild("FrostAttachment") as Attachment;
                if (frostAttachment) {
                    const particles = frostAttachment.FindFirstChild("FrostParticles") as ParticleEmitter;
                    if (particles) {
                        particles.Enabled = false;
                    }
                    task.delay(3, () => {
                        if (frostAttachment.Parent) {
                            frostAttachment.Destroy();
                        }
                    });
                }
            }
        }

        // Clear the frozen objects map
        this.frozenObjects.clear();
        print("✅ All objects restored to original state");
    }
}
