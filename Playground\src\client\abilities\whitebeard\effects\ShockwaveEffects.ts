import { TweenService, RunService, Workspace } from "@rbxts/services";

import { createDustCloudShockwave } from "./DustCloud";
import { createGroundCracks } from "./GroundCracks";

export function createMapShockwave(centerPosition: Vector3, maxRadius: number = 200, duration: number = 2): void {
    // Create beautiful dust cloud shockwave effect
    createDustCloudShockwave(centerPosition, maxRadius, duration);

    // Create ground crack pattern
    createGroundCracks(centerPosition, maxRadius * 0.7);

    print(`🌊 Enhanced shockwave system created: radius=${maxRadius}, duration=${duration}`);
}