-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local CrackEffectHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core").CrackEffectHelper
local function createGroundCracks(centerPosition, maxRadius)
	-- Use Core framework CrackEffectHelper - exact same behavior
	CrackEffectHelper:createGroundCracks(centerPosition, maxRadius, 12)
end
return {
	createGroundCracks = createGroundCracks,
}
