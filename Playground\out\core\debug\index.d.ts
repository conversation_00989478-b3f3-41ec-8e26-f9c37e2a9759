export { DebugManager, type DebugConfig } from "./DebugManager";
export { Debug<PERSON><PERSON><PERSON>, type DebugLine, type DebugSphere, type DebugText } from "./DebugRenderer";
export { AIDebugger } from "./AIDebugger";
export { PlayerDebugger } from "./PlayerDebugger";
export { PerformanceMonitor, type PerformanceData } from "./PerformanceMonitor";
/**
 * Quick setup function to initialize debug system
 * Call this in your main client script to enable debugging
 */
export declare function initializeDebugSystem(): void;
/**
 * Quick debug utilities for external use
 */
export declare const DebugUtils: {
    drawLine: (from: Vector3, to: Vector3, color?: Color3, duration?: number) => void;
    drawSphere: (position: Vector3, radius?: number, color?: Color3, duration?: number) => void;
    drawText: (position: Vector3, text: string, color?: Color3, duration?: number) => void;
    log: (category: string, message: string, data?: unknown) => void;
    isEnabled: () => boolean;
    toggle: () => void;
};
