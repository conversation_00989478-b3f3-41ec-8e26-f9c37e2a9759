import { Workspace } from "@rbxts/services";

export class EffectPartBuilder {
    private part: Part;

    constructor() {
        this.part = new Instance("Part");
        this.part.Anchored = true;
        this.part.CanCollide = false;
        this.part.Parent = Workspace;
    }

    static create(): EffectPartBuilder {
        return new EffectPartBuilder();
    }

    shape(shape: Enum.PartType): this {
        this.part.Shape = shape;
        return this;
    }

    size(size: Vector3): this {
        this.part.Size = size;
        return this;
    }

    color(color: Color3): this {
        this.part.Color = color;
        return this;
    }

    material(material: Enum.Material): this {
        this.part.Material = material;
        return this;
    }

    transparency(trans: number): this {
        this.part.Transparency = trans;
        return this;
    }

    position(pos: Vector3): this {
        this.part.Position = pos;
        return this;
    }

    cframe(cf: CFrame): this {
        this.part.CFrame = cf;
        return this;
    }

    withLight(range = 10, brightness = 5, color = Color3.fromRGB(255, 255, 255)): this {
        const light = new Instance("PointLight");
        light.Range = range;
        light.Brightness = brightness;
        light.Color = color;
        light.Parent = this.part;
        return this;
    }

    spawn(): Part {
        return this.part;
    }
}