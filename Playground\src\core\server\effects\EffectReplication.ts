import { Players, ReplicatedStorage } from "@rbxts/services";
import { EffectData } from "./interfaces/EffectData";
import { ReplicationOptions } from "./interfaces/ReplicationOptions";

export class EffectReplication {
    private static effectEvent?: RemoteEvent;

    /**
     * Initialize the effect replication system
     */
    public static initialize(): void {
        if (this.effectEvent) return; // Already initialized

        // Create or get RemoteEvents folder
        let remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
        if (!remoteEventsFolder) {
            remoteEventsFolder = new Instance("Folder");
            remoteEventsFolder.Name = "RemoteEvents";
            remoteEventsFolder.Parent = ReplicatedStorage;
        }

        // Create effect replication event
        this.effectEvent = new Instance("RemoteEvent");
        this.effectEvent.Name = "EffectReplication";
        this.effectEvent.Parent = remoteEventsFolder;

        print("EffectReplication initialized");
    }

    /**
     * Ensure the helper is initialized
     */
    private static ensureInitialized(): void {
        if (!this.effectEvent) {
            this.initialize();
        }
    }

    /**
     * Replicate effect to all players in the server
     */
    public static replicateToAll(effectData: EffectData, options: ReplicationOptions = {}): void {
        this.ensureInitialized();

        if (options.excludePlayer) {
            // Send to all players except the excluded one
            for (const player of Players.GetPlayers()) {
                if (player !== options.excludePlayer) {
                    this.effectEvent!.FireClient(player, effectData);
                }
            }
        } else {
            // Send to all players
            this.effectEvent!.FireAllClients(effectData);
        }
    }

    /**
     * Replicate effect to specific player
     */
    public static replicateToPlayer(player: Player, effectData: EffectData): void {
        this.ensureInitialized();
        this.effectEvent!.FireClient(player, effectData);
    }

    /**
     * Replicate effect to multiple specific players
     */
    public static replicateToPlayers(players: Player[], effectData: EffectData): void {
        this.ensureInitialized();
        
        for (const player of players) {
            this.effectEvent!.FireClient(player, effectData);
        }
    }

    /**
     * Replicate effect to all players within a radius of a position
     */
    public static replicateToRadius(
        center: Vector3, 
        radius: number, 
        effectData: EffectData, 
        options: ReplicationOptions = {}
    ): void {
        this.ensureInitialized();

        const playersInRadius = this.getPlayersInRadius(center, radius);
        
        for (const player of playersInRadius) {
            if (options.excludePlayer && player === options.excludePlayer) {
                continue;
            }
            this.effectEvent!.FireClient(player, effectData);
        }
    }

    /**
     * Replicate effect to all players within radius of a specific player
     */
    public static replicateToPlayerRadius(
        centerPlayer: Player, 
        radius: number, 
        effectData: EffectData, 
        options: ReplicationOptions = {}
    ): void {
        this.ensureInitialized();

        const character = centerPlayer.Character;
        if (!character) return;

        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (!humanoidRootPart) return;

        this.replicateToRadius(humanoidRootPart.Position, radius, effectData, options);
    }

    /**
     * Get all players within a radius of a position
     */
    private static getPlayersInRadius(center: Vector3, radius: number): Player[] {
        const playersInRadius: Player[] = [];

        for (const player of Players.GetPlayers()) {
            const character = player.Character;
            if (!character) continue;

            const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
            if (!humanoidRootPart) continue;

            const distance = center.sub(humanoidRootPart.Position).Magnitude;
            if (distance <= radius) {
                playersInRadius.push(player);
            }
        }

        return playersInRadius;
    }

    /**
     * Replicate effect to players in a specific team
     */
    public static replicateToTeam(teamName: string, effectData: EffectData): void {
        this.ensureInitialized();

        for (const player of Players.GetPlayers()) {
            if (player.Team && player.Team.Name === teamName) {
                this.effectEvent!.FireClient(player, effectData);
            }
        }
    }

    /**
     * Replicate effect to all players except those in a specific team
     */
    public static replicateToAllExceptTeam(teamName: string, effectData: EffectData): void {
        this.ensureInitialized();

        for (const player of Players.GetPlayers()) {
            if (!player.Team || player.Team.Name !== teamName) {
                this.effectEvent!.FireClient(player, effectData);
            }
        }
    }

    /**
     * Replicate effect with delay
     */
    public static replicateWithDelay(
        delay: number, 
        replicationFunction: () => void
    ): void {
        task.delay(delay, replicationFunction);
    }

    /**
     * Replicate effect in sequence to create wave effects
     */
    public static replicateInSequence(
        players: Player[], 
        effectData: EffectData, 
        delayBetween: number = 0.1
    ): void {
        this.ensureInitialized();

        for (let i = 0; i < players.size(); i++) {
            const player = players[i];
            task.delay(i * delayBetween, () => {
                this.effectEvent!.FireClient(player, effectData);
            });
        }
    }

    /**
     * Get the effect replication RemoteEvent for advanced usage
     */
    public static getEffectEvent(): RemoteEvent | undefined {
        return this.effectEvent;
    }

    /**
     * Create a custom effect replication event
     */
    public static createCustomEffectEvent(eventName: string): RemoteEvent {
        // Create or get RemoteEvents folder
        let remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
        if (!remoteEventsFolder) {
            remoteEventsFolder = new Instance("Folder");
            remoteEventsFolder.Name = "RemoteEvents";
            remoteEventsFolder.Parent = ReplicatedStorage;
        }

        // Create custom effect event
        const customEvent = new Instance("RemoteEvent");
        customEvent.Name = eventName;
        customEvent.Parent = remoteEventsFolder;

        print(`Created custom effect event: ${eventName}`);
        return customEvent;
    }
}
