export interface SyncOptions {
    // Basic validation
    validateCooldown?: boolean;
    cooldownTime?: number;
    validateRange?: boolean;
    maxRange?: number;

    // Replication options
    replicateToAll?: boolean;
    replicateToRadius?: number;

    // Enhanced synchronization options
    enableSequenceValidation?: boolean;
    enableTimestampValidation?: boolean;
    maxTimestampDrift?: number; // Maximum allowed time difference in seconds
    enableBatching?: boolean;
    batchInterval?: number; // Batch interval in seconds (default: 0.016 for 60fps)

    // Performance options
    enableSpatialOptimization?: boolean;
    maxPlayersPerBatch?: number;
    prioritizeNearbyPlayers?: boolean;

    // Reliability options
    enableRetry?: boolean;
    maxRetries?: number;
    retryDelay?: number;
}
