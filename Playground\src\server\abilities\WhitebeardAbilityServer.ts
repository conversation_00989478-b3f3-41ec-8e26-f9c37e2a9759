// Whitebeard visual synchronization server using Core helpers
// This syncs visual effects across all clients without adding damage

import {
    NetworkSync<PERSON>elper,
    EffectReplicationHelper,
    ServerValidationHelper
} from "../../core/server";

interface VisualSyncRequest {
    punchType: "single" | "double";
    casterUserId: number;
    position: Vector3;
    timestamp: number;
}

export class WhitebeardAbilityServer {
    constructor() {
        this.setupVisualSync();
        print("🥊 Whitebeard visual sync server initialized");
    }

    private setupVisualSync(): void {
        // Create simple visual sync action - no damage, just visual effects
        NetworkSyncHelper.createSyncedAction<VisualSyncRequest, void>(
            "WHITEBEARD_VISUAL_SYNC",
            (player, request) => this.handleVisualSync(player, request),
            {
                validateCooldown: true,
                cooldownTime: 12, // 12 second cooldown to prevent spam
                replicateToAll: false // We handle replication manually
            }
        );
    }

    private handleVisualSync(player: Player, request: VisualSyncRequest): void {
        print(`🥊 ${player.Name} requesting ${request.punchType} punch visual sync`);

        // Simple validation - just check if player has character
        const validation = ServerValidationHelper.validatePlayerAction(player, request, {
            requireCharacter: true,
            requireHumanoidRootPart: true
        });

        if (!validation.valid) {
            print(`❌ Visual sync validation failed for ${player.Name}: ${validation.reason}`);
            return;
        }

        // Replicate visual effects to ALL clients (including the originator for consistency)
        EffectReplicationHelper.replicateToAll({
            type: "WHITEBEARD_QUAKE_PUNCH",
            casterUserId: player.UserId,
            punchType: request.punchType,
            position: request.position,
            timestamp: tick()
        });

        print(`✅ Visual effects replicated to all clients for ${player.Name}'s ${request.punchType} punch`);
    }

}
