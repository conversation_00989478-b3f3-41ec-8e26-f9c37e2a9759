-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local EffectPartBuilder = _core.EffectPartBuilder
local FrameAnimationHelper = _core.FrameAnimationHelper
local function createDustCloudShockwave(centerPosition, maxRadius, duration)
	-- Create multiple dust cloud rings for realistic effect
	local numClouds = 8
	for cloud = 0, numClouds - 1 do
		-- Position clouds in a circle pattern at ground level
		local angle = (cloud / numClouds) * math.pi * 2
		local startRadius = 5
		local cloudPos = Vector3.new(centerPosition.X + math.cos(angle) * startRadius, centerPosition.Y - 2, centerPosition.Z + math.sin(angle) * startRadius)
		-- Create dust cloud using EffectPartBuilder
		local dustCloud = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(2, 1, 2)):color(Color3.fromRGB(180, 160, 140)):material(Enum.Material.Sand):transparency(0.4):position(cloudPos):spawn()
		dustCloud.Name = `DustCloud_{cloud}`
		-- Animate dust cloud expansion using FrameAnimationHelper
		local cloudDelay = cloud * 0.05
		FrameAnimationHelper:animate(`dustcloud_{cloud}_{tick()}`, duration, function(progress)
			-- Expand and move outward
			local currentRadius = startRadius + (maxRadius * progress * 0.8)
			local newPos = Vector3.new(centerPosition.X + math.cos(angle) * currentRadius, centerPosition.Y - 2 + (progress * 3), centerPosition.Z + math.sin(angle) * currentRadius)
			dustCloud.Position = newPos
			-- Scale up the dust cloud
			local scale = 2 + (progress * 4)
			dustCloud.Size = Vector3.new(scale, scale * 0.5, scale)
			-- Fade out
			dustCloud.Transparency = 0.4 + (progress * 0.6)
		end, function()
			-- Cleanup when animation completes
			dustCloud:Destroy()
		end, cloudDelay)
	end
end
return {
	createDustCloudShockwave = createDustCloudShockwave,
}
