-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local TweenService = _services.TweenService
local Workspace = _services.Workspace
local _PunchEffects = TS.import(script, script.Parent.Parent, "effects", "PunchEffects")
local createPunchParticleExplosion = _PunchEffects.createPunchParticleExplosion
local createEnergyTrails = _PunchEffects.createEnergyTrails
local _CrackEffects = TS.import(script, script.Parent.Parent, "effects", "CrackEffects")
local createAirCracks = _CrackEffects.createAirCracks
local createEnhancedAirCracks = _CrackEffects.createEnhancedAirCracks
local createCameraShake = TS.import(script, script.Parent.Parent, "effects", "CameraShake").createCameraShake
local createMapShockwave = TS.import(script, script.Parent.Parent, "effects", "ShockwaveEffects").createMapShockwave
local _AnimationEffects = TS.import(script, script.Parent, "AnimationEffects")
local findBodyJoints = _AnimationEffects.findBodyJoints
local animateFullBodyPunch = _AnimationEffects.animateFullBodyPunch
local animateCrossPunch = _AnimationEffects.animateCrossPunch
local restoreBodyJoints = _AnimationEffects.restoreBodyJoints
local function executeQuakePunch(ability, character, rightHand, selectedPunchType)
	print(`🔥 Starting executeQuakePunch with {selectedPunchType} punch`)
	-- Keep sphere(s) for a few more seconds during punch, clean up later
	-- Don't destroy sphere(s) immediately
	-- Find the right shoulder Motor6D - comprehensive search
	local rightShoulder
	-- First check if it's an R6 character (has Torso)
	local torso = character:FindFirstChild("Torso")
	if torso then
		-- R6 character - Motor6D is in Torso
		rightShoulder = torso:FindFirstChild("Right Shoulder")
		print("🔍 Checking R6 character for Right Shoulder in Torso")
	else
		-- R15 character - search everywhere for right arm connection
		print("🔍 R15 character detected, searching for right arm Motor6D...")
		local rightUpperArm = character:FindFirstChild("RightUpperArm")
		local rightLowerArm = character:FindFirstChild("RightLowerArm")
		local rightHand = character:FindFirstChild("RightHand")
		-- Search through all parts in the character for Motor6D joints
		local partsToCheck = { character:FindFirstChild("UpperTorso"), rightUpperArm, rightLowerArm, rightHand }
		for _, part in partsToCheck do
			if part and part:IsA("BasePart") then
				for name, child in pairs(part:GetChildren()) do
					if child:IsA("Motor6D") then
						local motor = child
						-- Check if this Motor6D connects UpperTorso to RightUpperArm
						local _result = motor.Part0
						if _result ~= nil then
							_result = _result.Name
						end
						local _condition = _result == "UpperTorso"
						if _condition then
							local _result_1 = motor.Part1
							if _result_1 ~= nil then
								_result_1 = _result_1.Name
							end
							_condition = _result_1 == "RightUpperArm"
						end
						local _condition_1 = _condition
						if not _condition_1 then
							local _result_1 = motor.Part1
							if _result_1 ~= nil then
								_result_1 = _result_1.Name
							end
							local _condition_2 = _result_1 == "UpperTorso"
							if _condition_2 then
								local _result_2 = motor.Part0
								if _result_2 ~= nil then
									_result_2 = _result_2.Name
								end
								_condition_2 = _result_2 == "RightUpperArm"
							end
							_condition_1 = _condition_2
						end
						if _condition_1 then
							rightShoulder = motor
							local _exp = part.Name
							local _result_1 = motor.Part0
							if _result_1 ~= nil then
								_result_1 = _result_1.Name
							end
							local _result_2 = motor.Part1
							if _result_2 ~= nil then
								_result_2 = _result_2.Name
							end
							print(`✅ Found right shoulder Motor6D: {name} in {_exp} (connects {_result_1} to {_result_2})`)
							break
						end
					end
				end
				if rightShoulder then
					break
				end
			end
		end
	end
	-- Debug: Comprehensive search for all Motor6D joints
	if not rightShoulder then
		print("🔍 Debugging - Comprehensive Motor6D search:")
		-- Check all parts in character for Motor6D joints
		local _exp = character:GetDescendants()
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(obj)
			return obj:IsA("BasePart")
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		local allParts = _newValue
		for _, part in allParts do
			local _exp_1 = part:GetChildren()
			-- ▼ ReadonlyArray.filter ▼
			local _newValue_1 = {}
			local _callback_1 = function(child)
				return child:IsA("Motor6D")
			end
			local _length_1 = 0
			for _k, _v in _exp_1 do
				if _callback_1(_v, _k - 1, _exp_1) == true then
					_length_1 += 1
					_newValue_1[_length_1] = _v
				end
			end
			-- ▲ ReadonlyArray.filter ▲
			local motors = _newValue_1
			if #motors > 0 then
				print(`🔍 Motor6D joints in {part.Name}:`)
				for _1, motor in motors do
					local _exp_2 = motor.Name
					local _result = motor.Part0
					if _result ~= nil then
						_result = _result.Name
					end
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					print(`  - {_exp_2}: (Part0: {_result}, Part1: {_result_1})`)
				end
			end
		end
		-- Also list all right arm parts
		print("🔍 Right arm parts found:")
		local rightParts = { "RightUpperArm", "RightLowerArm", "RightHand" }
		for _, partName in rightParts do
			local part = character:FindFirstChild(partName)
			print(`  - {partName}: {if part then "✅ Found" else "❌ Missing"}`)
		end
	end
	if not rightShoulder then
		print("❌ Right Shoulder not found! Proceeding without arm animation...")
		-- Still create the cracks even if we can't animate the arm - use current position
		task.delay(0.2, function()
			print("🔥 Creating air cracks (no arm animation)")
			local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
			if humanoidRootPart then
				local lookDirection = humanoidRootPart.CFrame.LookVector
				local horizontalLookDirection = Vector3.new(lookDirection.X, 0, lookDirection.Z).Unit
				local _position = humanoidRootPart.Position
				local _arg0 = horizontalLookDirection * 8
				local crackPosition = _position + _arg0
				print(`✅ Crack position (horizontal): {crackPosition}`)
				createAirCracks(crackPosition)
			else
				print("❌ HumanoidRootPart not found for cracks!")
			end
		end)
		return nil
	end
	print("✅ Right Shoulder found, starting full body punch animation")
	-- Store original position using attributes
	local originalC0 = rightShoulder.C0
	rightShoulder:SetAttribute("OriginalC0", originalC0)
	-- Find additional joints for full body animation
	local bodyJoints = findBodyJoints(character)
	-- Store original positions for all joints
	for jointName, joint in pairs(bodyJoints) do
		if joint then
			joint:SetAttribute(`Original_{tostring(jointName)}`, joint.C0)
			print(`✅ Stored original position for {tostring(jointName)}`)
		else
			print(`❌ Joint {tostring(jointName)} not found`)
		end
	end
	-- Use the punch type passed from executeQuakePunch
	if selectedPunchType == "single" then
		-- Original single forward punch
		animateFullBodyPunch(rightShoulder, bodyJoints)
	else
		-- One Piece style cross punch
		animateCrossPunch(rightShoulder, bodyJoints)
	end
	-- Play punch sound effect
	local punchSound = Instance.new("Sound")
	punchSound.SoundId = "rbxassetid://84539241826189"
	punchSound.Volume = 0.8
	punchSound.PlaybackSpeed = 1
	punchSound.Parent = character:FindFirstChild("HumanoidRootPart") or Workspace
	punchSound:Play()
	-- Clean up sound after it finishes
	punchSound.Ended:Connect(function()
		punchSound:Destroy()
	end)
	-- Create enhanced air cracks during punch (slight delay for impact)
	task.delay(0.3, function()
		print("🔥 Creating enhanced air cracks")
		-- Use current player position at punch time for accurate crack placement
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if humanoidRootPart then
			local currentPosition = humanoidRootPart.Position
			local currentLookDirection = humanoidRootPart.CFrame.LookVector
			local currentRightVector = humanoidRootPart.CFrame.RightVector
			-- Add dramatic punch particles
			createPunchParticleExplosion(currentPosition, currentLookDirection)
			-- Create energy trails from fist to impact point
			local rightHand = character:FindFirstChild("RightHand")
			if rightHand then
				local _arg0 = currentLookDirection * 10
				local impactPoint = currentPosition + _arg0
				createEnergyTrails(rightHand.Position, impactPoint)
			end
			if selectedPunchType == "single" then
				-- Single punch: crack appears directly in front of player where the punch hits
				local horizontalLookDirection = Vector3.new(currentLookDirection.X, 0, currentLookDirection.Z).Unit
				local _arg0 = horizontalLookDirection * 6
				local crackPosition = currentPosition + _arg0
				print(`✅ Single punch crack position (front): {crackPosition}`)
				createEnhancedAirCracks(crackPosition, horizontalLookDirection)
				-- Single punch camera shake
				createCameraShake(8, 0.6)
				-- Single punch map shockwave
				createMapShockwave(currentPosition, 250, 2.5)
			else
				-- Cross punch: cracks appear to the LEFT and RIGHT sides where each punch hits
				-- Left punch hits to the LEFT side of the player
				local leftDirection = currentRightVector * (-1)
				local _arg0 = leftDirection * 8
				local leftCrackPos = currentPosition + _arg0
				-- Right punch hits to the RIGHT side of the player
				local rightDirection = currentRightVector
				local _arg0_1 = rightDirection * 8
				local rightCrackPos = currentPosition + _arg0_1
				print(`✅ Cross punch crack positions: LEFT={leftCrackPos}, RIGHT={rightCrackPos}`)
				-- Create LEFT crack immediately (for left punch)
				createEnhancedAirCracks(leftCrackPos, leftDirection)
				-- Left punch camera shake
				createCameraShake(6, 0.4)
				-- Left punch shockwave (moderate)
				createMapShockwave(currentPosition, 180, 1.8)
				-- Create RIGHT crack with 0.5s delay (to match right punch timing)
				task.delay(0.5, function()
					-- Get current position again for the delayed right crack
					local humanoidRootPartDelayed = character:FindFirstChild("HumanoidRootPart")
					if humanoidRootPartDelayed then
						local delayedPosition = humanoidRootPartDelayed.Position
						local delayedRightVector = humanoidRootPartDelayed.CFrame.RightVector
						local delayedRightDirection = delayedRightVector
						local _arg0_2 = delayedRightDirection * 8
						local delayedRightCrackPos = delayedPosition + _arg0_2
						createEnhancedAirCracks(delayedRightCrackPos, delayedRightDirection)
						-- Right punch camera shake (stronger for final impact)
						createCameraShake(10, 0.7)
						-- Right punch shockwave (massive final impact)
						createMapShockwave(delayedPosition, 300, 3)
						print(`✅ Right crack created with delay at position: {delayedRightCrackPos}`)
					end
				end)
			end
		else
			print("❌ HumanoidRootPart not found for cracks!")
		end
	end)
	-- Hold punch position for 3 seconds, then restore all joints
	task.delay(3.5, function()
		restoreBodyJoints(rightShoulder, bodyJoints)
	end)
	-- Clean up sphere effect(s) after punch is done (5 seconds total)
	task.delay(5, function()
		if ability.quakeEffect then
			-- Fade out right sphere
			local fadeOut = TweenService:Create(ability.quakeEffect, TweenInfo.new(1, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				if ability.quakeEffect then
					ability.quakeEffect:Destroy()
					ability.quakeEffect = nil
				end
			end)
		end
		if ability.leftQuakeEffect then
			-- Fade out left sphere
			local leftFadeOut = TweenService:Create(ability.leftQuakeEffect, TweenInfo.new(1, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			leftFadeOut:Play()
			leftFadeOut.Completed:Connect(function()
				if ability.leftQuakeEffect then
					ability.leftQuakeEffect:Destroy()
					ability.leftQuakeEffect = nil
				end
			end)
		end
		if ability.quakeConnection then
			ability.quakeConnection:Disconnect()
			ability.quakeConnection = nil
		end
	end)
end
return {
	executeQuakePunch = executeQuakePunch,
}
