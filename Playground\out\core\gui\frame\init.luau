-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
exports.Frame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame", "Frame").Frame
exports.ContainerFrame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame", "ContainerFrame").ContainerFrame
exports.HorizontalFrame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame", "HorizontalFrame").HorizontalFrame
exports.VerticalFrame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame", "VerticalFrame").VerticalFrame
exports.ScrollingFrame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame", "ScrollingFrame").ScrollingFrame
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame", "types") or {} do
	exports[_k] = _v
end
return exports
