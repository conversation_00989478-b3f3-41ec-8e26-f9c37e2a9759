-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local AbilityBase = TS.import(script, script.Parent.Parent, "AbilityBase").AbilityBase
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Players = _services.Players
local ReplicatedStorage = _services.ReplicatedStorage
local createCameraShake = TS.import(script, script.Parent, "effects", "CameraShake").createCameraShake
local createMapShockwave = TS.import(script, script.Parent, "effects", "ShockwaveEffects").createMapShockwave
local _SphereVisuals = TS.import(script, script.Parent, "effects", "SphereVisuals")
local createQuakeSphere = _SphereVisuals.createQuakeSphere
local createSequentialQuakeSpheres = _SphereVisuals.createSequentialQuakeSpheres
local executeQuakePunch = TS.import(script, script.Parent, "animations", "PunchExecution").executeQuakePunch
local QuakeAbility
do
	local super = AbilityBase
	QuakeAbility = setmetatable({}, {
		__tostring = function()
			return "QuakeAbility"
		end,
		__index = super,
	})
	QuakeAbility.__index = QuakeAbility
	function QuakeAbility.new(...)
		local self = setmetatable({}, QuakeAbility)
		return self:constructor(...) or self
	end
	function QuakeAbility:constructor()
		super.constructor(self, "QUAKE_PUNCH", 12)
		self.cooldownEndTime = 0
		self:initializeNetworking()
	end
	function QuakeAbility:initializeNetworking()
		-- Wait for the effect replication event created by Core server helpers
		local remoteEventsFolder = ReplicatedStorage:WaitForChild("RemoteEvents")
		self.effectReplicationEvent = remoteEventsFolder:WaitForChild("EffectReplication")
		-- Listen for effect replications from server
		self.effectReplicationEvent.OnClientEvent:Connect(function(effectData)
			local data = effectData
			if data.type == "WHITEBEARD_QUAKE_PUNCH" then
				self:createVisualEffectsFromServer(data)
			end
		end)
	end
	function QuakeAbility:isOnCooldown()
		return tick() < self.cooldownEndTime
	end
	function QuakeAbility:startCooldown()
		self.cooldownEndTime = tick() + self:getCooldownTime()
	end
	function QuakeAbility:createVisualEffectsFromServer(effectData)
		-- Get the player who cast the ability
		local caster = Players:GetPlayerByUserId(effectData.casterUserId)
		local _result = caster
		if _result ~= nil then
			_result = _result.Character
		end
		if not _result then
			return nil
		end
		print(`🥊 Creating {effectData.punchType} quake punch effects for {caster.Name}`)
		local character = caster.Character
		local rightHand = character:FindFirstChild("RightHand")
		if not rightHand then
			return nil
		end
		-- Phase 1: Create sphere(s) based on punch type (same as original)
		if effectData.punchType == "single" then
			createQuakeSphere(self, character)
		else
			createSequentialQuakeSpheres(self, character)
		end
		-- Phase 2: Execute punch animation and effects (after 2 seconds like original)
		task.delay(2, function()
			if character.Parent then
				executeQuakePunch(self, character, rightHand, effectData.punchType)
				-- Create environmental effects
				local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
				if humanoidRootPart then
					createCameraShake(5, 0.5)
					createMapShockwave(humanoidRootPart.Position, 200, 2)
				end
			end
		end)
	end
	function QuakeAbility:activate(punchType)
		if punchType == nil then
			punchType = "single"
		end
		if self:isOnCooldown() then
			return nil
		end
		local player = Players.LocalPlayer
		local character = player.Character
		if not character then
			return nil
		end
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		print(`🥊 Requesting {punchType} punch from server for visual sync`)
		-- Send request to server to replicate visual effects to all clients
		-- The server will handle replication back to ALL clients including this one
		local remoteEventsFolder = ReplicatedStorage:FindFirstChild("RemoteEvents")
		local _result = remoteEventsFolder
		if _result ~= nil then
			_result = _result:FindFirstChild("WHITEBEARD_VISUAL_SYNC")
		end
		local visualSyncEvent = _result
		if visualSyncEvent then
			visualSyncEvent:FireServer({
				request = {
					punchType = punchType,
					casterUserId = player.UserId,
					position = humanoidRootPart.Position,
					timestamp = tick(),
				},
				clientTimestamp = tick(),
				sequenceNumber = 1,
			})
		end
		-- Start cooldown immediately to prevent spam while waiting for server response
		self:startCooldown()
	end
end
return {
	QuakeAbility = QuakeAbility,
}
