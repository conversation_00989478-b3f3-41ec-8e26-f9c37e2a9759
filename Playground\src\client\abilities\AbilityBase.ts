export abstract class AbilityBase {
    protected name: string;
    protected cooldownTime: number;

    constructor(name: string, cooldownTime: number) {
        this.name = name;
        this.cooldownTime = cooldownTime;
    }

    public getName(): string {
        return this.name;
    }

    public getCooldownTime(): number {
        return this.cooldownTime;
    }

    public abstract activate(): void;
    public abstract isOnCooldown(): boolean;
}
