import { Workspace, Players } from "@rbxts/services";
import { GravityOptions, GravityLevel } from "./interfaces/GravityOptions";
import { EffectPartBuilder } from "../../effects/EffectPartBuilder";
import { EffectTweenBuilder } from "../../effects/EffectTweenBuilder";

/**
 * GravityController - Manages gravity levels for world and players
 * Provides simple methods for changing gravity with different presets
 */
export class GravityController {
    private static instance: GravityController;
    private currentGravityLevel: GravityLevel = "normal";
    private originalGravity: number = 196.2; // Default Roblox gravity

    private constructor() {
        this.originalGravity = Workspace.Gravity;
    }

    public static getInstance(): GravityController {
        if (!GravityController.instance) {
            GravityController.instance = new GravityController();
        }
        return GravityController.instance;
    }

    /**
     * Set gravity level with options
     */
    public setGravity(options: GravityOptions): void {
        this.currentGravityLevel = options.level;
        
        let gravityValue: number;
        
        if (options.customValue !== undefined) {
            gravityValue = options.customValue * this.originalGravity;
        } else {
            gravityValue = this.getGravityValueForLevel(options.level);
        }

        // Apply gravity to workspace
        Workspace.Gravity = gravityValue;

        // Apply gravity effects to players if specified
        if (options.affectsPlayers !== false) { // Default true
            this.applyGravityToPlayers(gravityValue);
        }

        // Apply gravity effects to objects if specified
        if (options.affectsObjects !== false) { // Default true
            this.applyGravityToObjects(gravityValue);
        }

        print(`🌍 Gravity changed to: ${options.level} (${gravityValue} units, ${string.format("%.2f", gravityValue / this.originalGravity)}x)`);
    }

    /**
     * Get current gravity level
     */
    public getCurrentGravityLevel(): GravityLevel {
        return this.currentGravityLevel;
    }

    /**
     * Get current gravity value
     */
    public getCurrentGravityValue(): number {
        return Workspace.Gravity;
    }

    /**
     * Get gravity multiplier compared to normal
     */
    public getCurrentGravityMultiplier(): number {
        return Workspace.Gravity / this.originalGravity;
    }

    private getGravityValueForLevel(level: GravityLevel): number {
        switch (level) {
            case "zero":
                return this.originalGravity * 0.05; // Very low gravity
            case "low":
                return this.originalGravity * 0.3;  // Moon-like gravity
            case "normal":
                return this.originalGravity * 1.0;  // Earth gravity
            case "high":
                return this.originalGravity * 2.5;  // Heavy gravity
            default:
                return this.originalGravity;
        }
    }

    private applyGravityToPlayers(gravityValue: number): void {
        const gravityMultiplier = gravityValue / this.originalGravity;
        
        for (const player of Players.GetPlayers()) {
            const character = player.Character;
            if (!character) continue;

            const humanoid = character.FindFirstChild("Humanoid") as Humanoid;
            if (!humanoid) continue;

            // Adjust jump power based on gravity
            // Lower gravity = higher jump power
            const baseJumpPower = 50; // Default jump power
            const adjustedJumpPower = baseJumpPower / math.sqrt(gravityMultiplier);
            humanoid.JumpPower = math.max(5, math.min(200, adjustedJumpPower));

            // Adjust walk speed slightly based on gravity
            const baseWalkSpeed = 16; // Default walk speed
            const adjustedWalkSpeed = baseWalkSpeed * math.sqrt(1 / gravityMultiplier);
            humanoid.WalkSpeed = math.max(5, math.min(50, adjustedWalkSpeed));
        }
    }

    private applyGravityToObjects(gravityValue: number): void {
        // Find all unanchored parts and apply gravity effects
        const parts = Workspace.GetDescendants().filter((obj): obj is Part => 
            obj.IsA("Part") && !obj.Anchored && obj.Parent !== Players
        );

        for (const part of parts) {
            // Gravity affects mass and physics behavior
            // Parts will naturally respond to the workspace gravity change
            
            // Optional: Add special effects for extreme gravity changes
            const gravityMultiplier = gravityValue / this.originalGravity;
            
            if (gravityMultiplier < 0.2) {
                // Very low gravity - parts float more
                const bodyVelocity = part.FindFirstChild("FloatEffect") as BodyVelocity;
                if (!bodyVelocity) {
                    // Create floating particles for visual effect
                    this.createFloatingParticles(part.Position, gravityMultiplier);
                    
                    // Add float effect using raw BodyVelocity (needed for physics)
                    const floatEffect = new Instance("BodyVelocity");
                    floatEffect.Name = "FloatEffect";
                    floatEffect.MaxForce = new Vector3(0, math.huge, 0);
                    floatEffect.Velocity = new Vector3(0, 2, 0); // Slight upward drift
                    floatEffect.Parent = part;
                }
            } else {
                // Remove float effects for normal/high gravity
                const floatEffect = part.FindFirstChild("FloatEffect");
                if (floatEffect) {
                    floatEffect.Destroy();
                }
            }
        }
    }

    /**
     * Create gravity zones (areas with different gravity)
     */
    public createGravityZone(position: Vector3, size: Vector3, gravityLevel: GravityLevel): Part {
        // Create zone using Core framework
        const zone = EffectPartBuilder.create()
            .size(size)
            .position(position)
            .transparency(0.8)
            .material(Enum.Material.ForceField)
            .color(this.getGravityZoneColorAsColor3(gravityLevel))
            .spawn();
        
        zone.Name = `GravityZone_${gravityLevel}`;
        zone.CanCollide = false;

        // Add zone detection
        const gravityValue = this.getGravityValueForLevel(gravityLevel);
        
        zone.Touched.Connect((hit) => {
            const character = hit.Parent;
            const humanoid = character?.FindFirstChild("Humanoid") as Humanoid;
            
            if (humanoid) {
                // Apply zone-specific gravity effects to player
                const baseJumpPower = 50;
                const gravityMultiplier = gravityValue / this.originalGravity;
                const adjustedJumpPower = baseJumpPower / math.sqrt(gravityMultiplier);
                humanoid.JumpPower = math.max(5, math.min(200, adjustedJumpPower));
                
                // Create visual effect when entering zone
                this.createGravityZoneEffect(zone.Position, gravityLevel);
                
                print(`Player entered ${gravityLevel} gravity zone`);
            }
        });

        print(`🌀 Created ${gravityLevel} gravity zone at ${position}`);
        return zone;
    }

    private createFloatingParticles(position: Vector3, gravityMultiplier: number): void {
        // Create floating particles for low gravity areas
        const numParticles = math.floor((1 - gravityMultiplier) * 10);
        
        for (let i = 0; i < numParticles; i++) {
            const particle = EffectPartBuilder.create()
                .shape(Enum.PartType.Ball)
                .size(new Vector3(0.3, 0.3, 0.3))
                .color(new Color3(0.8, 0.9, 1))
                .material(Enum.Material.Neon)
                .transparency(0.6)
                .position(position.add(new Vector3(
                    math.random(-5, 5),
                    math.random(-2, 5),
                    math.random(-5, 5)
                )))
                .spawn();

            // Make particles float upward slowly
            EffectTweenBuilder.for(particle)
                .move(particle.Position.add(new Vector3(0, 20, 0)))
                .duration(8)
                .easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
                .onComplete(() => {
                    if (particle.Parent) {
                        particle.Destroy();
                    }
                })
                .play();

            // Fade out over time
            EffectTweenBuilder.for(particle)
                .fade(1)
                .duration(6)
                .delay(2)
                .play();
        }
    }

    private createGravityZoneEffect(position: Vector3, gravityLevel: GravityLevel): void {
        // Create visual effect when entering gravity zone
        const effectColor = this.getGravityZoneColorAsColor3(gravityLevel);
        
        for (let i = 0; i < 5; i++) {
            const effect = EffectPartBuilder.create()
                .shape(Enum.PartType.Ball)
                .size(new Vector3(0.5, 0.5, 0.5))
                .color(effectColor)
                .material(Enum.Material.Neon)
                .transparency(0.3)
                .position(position.add(new Vector3(
                    math.random(-3, 3),
                    math.random(-1, 3),
                    math.random(-3, 3)
                )))
                .spawn();

            // Expand and fade effect
            EffectTweenBuilder.for(effect)
                .expand(new Vector3(2, 2, 2))
                .fade(1)
                .duration(1.5)
                .onComplete(() => {
                    if (effect.Parent) {
                        effect.Destroy();
                    }
                })
                .play();
        }
    }

    private getGravityZoneColorAsColor3(level: GravityLevel): Color3 {
        switch (level) {
            case "zero":
                return new Color3(0, 0.7, 1); // Bright blue
            case "low":
                return new Color3(0, 1, 1); // Cyan
            case "normal":
                return new Color3(0, 1, 0); // Bright green
            case "high":
                return new Color3(1, 0, 0); // Bright red
            default:
                return new Color3(0.5, 0.5, 0.5); // Medium grey
        }
    }

    private getGravityZoneColor(level: GravityLevel): BrickColor {
        switch (level) {
            case "zero":
                return new BrickColor("Bright blue");
            case "low":
                return new BrickColor("Cyan");
            case "normal":
                return new BrickColor("Bright green");
            case "high":
                return new BrickColor("Bright red");
            default:
                return new BrickColor("Medium stone grey");
        }
    }

    /**
     * Reset gravity to normal
     */
    public resetGravity(): void {
        this.setGravity({ level: "normal" });
    }

    /**
     * Cleanup all gravity effects
     */
    public cleanup(): void {
        this.resetGravity();
        
        // Remove all gravity zones
        const gravityZones = Workspace.GetChildren().filter(obj => 
            string.find(obj.Name, "GravityZone_")[0] !== undefined
        );
        
        for (const zone of gravityZones) {
            zone.Destroy();
        }

        // Remove float effects from all parts
        const parts = Workspace.GetDescendants().filter((obj): obj is Part => obj.IsA("Part"));
        for (const part of parts) {
            const floatEffect = part.FindFirstChild("FloatEffect");
            if (floatEffect) {
                floatEffect.Destroy();
            }
        }
        
        print("🌍 Gravity effects cleaned up");
    }
}
