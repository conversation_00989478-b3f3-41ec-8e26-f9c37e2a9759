import { AbilityBase } from "../AbilityBase";
export declare class QuakeAbility extends AbilityBase {
    quakeEffect?: Part;
    leftQuakeEffect?: Part;
    quakeConnection?: RBXScriptConnection;
    private cooldownEndTime;
    private effectReplicationEvent?;
    constructor();
    private initializeNetworking;
    isOnCooldown(): boolean;
    private startCooldown;
    private createVisualEffectsFromServer;
    activate(punchType?: "single" | "double"): void;
}
