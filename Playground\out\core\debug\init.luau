-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
-- Debug System - Visual debugging tools for development
local DebugManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug", "DebugManager").DebugManager
exports.DebugManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug", "DebugManager").DebugManager
exports.DebugRenderer = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug", "DebugRenderer").DebugRenderer
exports.AIDebugger = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug", "AIDebugger").AIDebugger
exports.PlayerDebugger = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug", "PlayerDebugger").PlayerDebugger
exports.PerformanceMonitor = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug", "PerformanceMonitor").PerformanceMonitor
--[[
	*
	 * Quick setup function to initialize debug system
	 * Call this in your main client script to enable debugging
	 
]]
local function initializeDebugSystem()
	local debugManager = DebugManager:getInstance()
	debugManager:initialize()
	print("🔧 Debug System initialized!")
	print("🔍 Use the Debug Panel button to control debug features")
	print("📱 Click the 🔧 Debug button in the bottom-left corner")
end
--[[
	*
	 * Quick debug utilities for external use
	 
]]
local DebugUtils = {
	drawLine = function(from, to, color, duration)
		DebugManager:getInstance():drawLine(from, to, color, duration)
	end,
	drawSphere = function(position, radius, color, duration)
		DebugManager:getInstance():drawSphere(position, radius, color, duration)
	end,
	drawText = function(position, text, color, duration)
		DebugManager:getInstance():drawText(position, text, color, duration)
	end,
	log = function(category, message, data)
		DebugManager:getInstance():logDebug(category, message, data)
	end,
	isEnabled = function()
		return DebugManager:getInstance():isEnabled()
	end,
	toggle = function()
		DebugManager:getInstance():toggle()
	end,
}
exports.initializeDebugSystem = initializeDebugSystem
exports.DebugUtils = DebugUtils
return exports
