// src/core/design/index.ts

export const COLORS = {
  text: {
    "main": "#F1F5F9",
    "secondary": "#A0AEC0",
  },
  bg: {
    "base": "#1F2023",
    "secondary": "#18191B",
    "surface": "#242529",
    "surface-hover": "#2E3033",
    "avatar": "#3B3C40",
    "badge": "#2F3237",
    "hover": "#2E3033",
  },
  label: {
    "text": "#BCCCDC",
    "focus": "#E5EFF9",
    "bg": "#2F3134",
    "border": "#393C3F",
    "muted": "#A7B0BF",
    "hover": "#D4DCE3",
  },
  span: {
    "default": "#F1F5F9",
    "muted": "#A0AEC0",
    "highlight": "#6FC2B2",
    "subtle": "#9AA3B5",
    "hover": "#FFFFFF",
  },
  border: {
    "base": "#404040",
    "l1": "#4A4A4A",
    "l2": "#555555",
    "l3": "#666666",
    "strong": "#777777",
    "focus": "#5CB2BC",
  },
  "primary": "#5CB2BC",
  "primary-dark": "#4AA3A3",
  "success": "#AADB99",
  "warning": "#E5A73D",
  "error": "#DF6B75",
  "info": "#5CB2BC",
  "progress-bg": "#16191C",
  "progress-fill": "#5CB2BC",
  "account-active": "#2C2D2F",
  "account-active-hover": "#2C2D2F",
  badge: {
    "bg": "#2F3237",
    "text": "#D7E6F4",
    "border": "#3B3E45",
  },
  ring: {
    "focus-accent": "#5CB2BC",
  },
};

export const SIZES = {
  padding: 10,  // Standard padding in pixels
  margin: 5,    // Standard margin
  fontSize: 12, // Reduced from 14 to 12
  button: { width: 120, height: 40 },  // Button size
  input: { width: 200, height: 30 },   // Input size
  gridCell: { width: 50, height: 50 }, // Grid cell size
};

export const TYPOGRAPHY = {
  font: Enum.Font.Arimo,  // Modern sans-serif (closest to Inter/Jakarta)
  weight: Enum.FontWeight.Regular,
  fontFamily: {
    inter: Enum.Font.Gotham,
    signika: Enum.Font.FredokaOne,
    jakarta: Enum.Font.JosefinSans,
  },
};

export const BORDER_RADIUS = {
  sm: 4,   // 0.25rem (4px)
  md: 8,   // 0.5rem (8px)
  lg: 12,  // 0.75rem (12px)
  xl: 16,  // 1rem (16px)
};

// Note: Box shadows, rings, animations, and transitions aren't directly supported in Roblox UI.
// Use Roblox equivalents like UIGradient, UIStroke for outlines, or TweenService for animations.
