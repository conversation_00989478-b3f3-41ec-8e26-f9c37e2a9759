import * as React from "@rbxts/react";
import { COLORS, SIZES, BORDER_RADIUS } from "../../design";
import { IconButton } from "../button";
import { Label } from "../label/Label";
import { ContainerFrame, HorizontalFrame } from "../frame";
import { Overlay } from "../overlay/Overlay";

interface ModalProps {
  title: string;
  isOpen: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  width?: number;
  height?: number;
}

export function Modal(props: ModalProps) {
  const width = props.width ?? 300;
  const height = props.height ?? 200;
  
  if (!props.isOpen) return <></>;
  
  return (
    <Overlay onBackdropClick={props.onClose}>
      {/* Modal container with click blocking - using textbutton to stop propagation */}
      <textbutton
        Text=""
        BackgroundColor3={Color3.fromHex(COLORS.bg.base)}
        Size={new UDim2(0, width, 0, height)}
        Position={new UDim2(0.5, 0, 0.5, 0)}
        AnchorPoint={new Vector2(0.5, 0.5)}
        ZIndex={12}
        AutoButtonColor={false}
        BorderSizePixel={0}
        Event={{
          Activated: () => {
            // Stop propagation by doing nothing - this prevents backdrop click
          }
        }}
      >
        <uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />
        <uistroke
          Color={Color3.fromHex(COLORS.border.l2)}
          Thickness={1}
        />

        <ContainerFrame
          backgroundTransparency={1}
          size={new UDim2(1, 0, 1, 0)}
          padding={0}
          borderThickness={0}
          autoSize={Enum.AutomaticSize.None}
        >
        {/* Title bar */}
        <HorizontalFrame
          backgroundColor={COLORS.bg.secondary}
          size={new UDim2(1, 0, 0, 40)}
          backgroundTransparency={0}
          padding={SIZES.padding}
          spacing={0}
          horizontalAlignment={Enum.HorizontalAlignment.Left}
          verticalAlignment={Enum.VerticalAlignment.Center}
        >
          <Label
            text={props.title}
            fontSize={SIZES.fontSize + 2}
            size={new UDim2(1, -50, 1, 0)}
            bold={true}
            layoutOrder={1}
          />

          <IconButton
            icon="X"
            onClick={props.onClose}
            layoutOrder={2}
            size={new UDim2(0, 30, 0, 30)}
          />
        </HorizontalFrame>

          {/* Content area */}
          <ContainerFrame
            backgroundTransparency={1}
            size={new UDim2(1, 0, 1, -40)}
            position={new UDim2(0, 0, 0, 40)}
            padding={SIZES.padding}
            borderThickness={0}
            autoSize={Enum.AutomaticSize.None}
          >
            {props.children}
          </ContainerFrame>
        </ContainerFrame>
      </textbutton>
    </Overlay>
  );
}







