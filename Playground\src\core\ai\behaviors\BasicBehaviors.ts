import { <PERSON><PERSON><PERSON><PERSON>or, <PERSON><PERSON>ontext, AIBehaviorResult } from "../interfaces/AIBehavior";
import { PositionHelper } from "../../helper/PositionHelper";

export class IdleBehavior implements AIBehavior {
	name = "Idle";
	priority = 1;

	canExecute(_context: AIContext): boolean {
		// Always can idle, but lowest priority
		return true;
	}

	execute(context: AIContext): AIBehaviorResult {
		// Just stand there and occasionally look around
		const idleTime = context.blackboard.idleTime as number || 0;
		const newIdleTime = idleTime + context.deltaTime;
		
		// Look around every 3 seconds
		if (newIdleTime > 3) {
			this.lookAround(context);
			context.blackboard.idleTime = 0;
		} else {
			context.blackboard.idleTime = newIdleTime;
		}

		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		context.blackboard.idleTime = 0;
		print(`😴 ${context.entityId} is now idle`);
	}

	private lookAround(context: AIContext): void {
		// Random rotation for looking around
		const randomAngle = math.random() * math.pi * 2;
		const lookDirection = new Vector3(math.cos(randomAngle), 0, math.sin(randomAngle));
		const lookPosition = context.position.add(lookDirection.mul(10));
		
		PositionHelper.lookAt(context.entity, lookPosition);
	}
}

export class FollowBehavior implements AIBehavior {
	name = "Follow";
	priority = 5;

	canExecute(context: AIContext): boolean {
		if (!context.target || !context.targetPosition) return false;
		
		const distance = context.position.sub(context.targetPosition).Magnitude;
		return distance <= 30 && distance > 5; // Follow if within 30 studs but not too close
	}

	execute(context: AIContext): AIBehaviorResult {
		if (!context.target || !context.targetPosition) {
			return { success: false, completed: true };
		}

		const distance = context.position.sub(context.targetPosition).Magnitude;
		
		// Move towards target
		this.moveTowards(context, context.targetPosition);
		
		// Look at target
		PositionHelper.lookAt(context.entity, context.targetPosition);

		return { success: true, completed: distance <= 5 };
	}

	onEnter(context: AIContext): void {
		print(`🏃 ${context.entityId} is following target`);
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		// Use simple obstacle avoidance
		const clearTarget = PositionHelper.findClearPath(context.position, targetPosition, [context.entity]);

		// Use smooth Roblox movement instead of teleportation
		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				// Use Humanoid.MoveTo for natural character movement
				humanoid.MoveTo(clearTarget);
			} else {
				// Fallback: smooth CFrame movement for non-humanoid models
				this.smoothMoveTo(context.entity.PrimaryPart, clearTarget, context.deltaTime);
			}
		} else if (context.entity.IsA("BasePart")) {
			// Smooth movement for single parts
			this.smoothMoveTo(context.entity as BasePart, clearTarget, context.deltaTime);
		}
	}

	private smoothMoveTo(part: BasePart, targetPosition: Vector3, deltaTime: number): void {
		const currentPosition = part.Position;
		const direction = targetPosition.sub(currentPosition);
		const distance = direction.Magnitude;

		if (distance > 0.1) {
			const moveSpeed = 16; // studs per second
			const maxMove = moveSpeed * deltaTime;
			const moveDistance = math.min(maxMove, distance);
			const newPosition = currentPosition.add(direction.Unit.mul(moveDistance));

			// Use CFrame for smooth rotation towards movement direction
			const lookDirection = direction.Unit;
			const newCFrame = CFrame.lookAt(newPosition, newPosition.add(lookDirection));
			part.CFrame = newCFrame;
		}
	}
}

export class PatrolBehavior implements AIBehavior {
	name = "Patrol";
	priority = 3;

	canExecute(context: AIContext): boolean {
		// Patrol if no target nearby
		return !context.target;
	}

	execute(context: AIContext): AIBehaviorResult {
		let patrolPoints = context.blackboard.patrolPoints as Vector3[];
		let currentPatrolIndex = context.blackboard.currentPatrolIndex as number || 0;

		// Initialize patrol points if not set
		if (!patrolPoints) {
			patrolPoints = this.generatePatrolPoints(context.position);
			context.blackboard.patrolPoints = patrolPoints;
			context.blackboard.currentPatrolIndex = 0;
			currentPatrolIndex = 0;
		}

		const targetPoint = patrolPoints[currentPatrolIndex];
		const distance = context.position.sub(targetPoint).Magnitude;

		if (distance <= 3) {
			// Reached patrol point, move to next
			currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.size();
			context.blackboard.currentPatrolIndex = currentPatrolIndex;
			
			// Wait a bit at each patrol point
			context.blackboard.patrolWaitTime = 2;
		} else {
			// Move towards current patrol point
			this.moveTowards(context, targetPoint);
			PositionHelper.lookAt(context.entity, targetPoint);
		}

		// Handle waiting at patrol points
		const waitTime = context.blackboard.patrolWaitTime as number || 0;
		if (waitTime > 0) {
			context.blackboard.patrolWaitTime = waitTime - context.deltaTime;
		}

		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		print(`🚶 ${context.entityId} is patrolling`);
	}

	private generatePatrolPoints(centerPosition: Vector3): Vector3[] {
		const points: Vector3[] = [];
		const radius = 15;
		const numPoints = 4;

		for (let i = 0; i < numPoints; i++) {
			const angle = (i / numPoints) * math.pi * 2;
			const x = centerPosition.X + math.cos(angle) * radius;
			const z = centerPosition.Z + math.sin(angle) * radius;
			points.push(new Vector3(x, centerPosition.Y, z));
		}

		return points;
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		const waitTime = context.blackboard.patrolWaitTime as number || 0;
		if (waitTime > 0) return; // Don't move while waiting

		// Use simple obstacle avoidance for patrol
		const clearTarget = PositionHelper.findClearPath(context.position, targetPosition, [context.entity]);

		// Use smooth Roblox movement for patrol
		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				humanoid.MoveTo(clearTarget);
			} else {
				this.smoothPatrolMove(context.entity.PrimaryPart, clearTarget, context.deltaTime);
			}
		} else if (context.entity.IsA("BasePart")) {
			this.smoothPatrolMove(context.entity as BasePart, clearTarget, context.deltaTime);
		}
	}

	private smoothPatrolMove(part: BasePart, targetPosition: Vector3, deltaTime: number): void {
		const currentPosition = part.Position;
		const direction = targetPosition.sub(currentPosition);
		const distance = direction.Magnitude;

		if (distance > 0.1) {
			const moveSpeed = 8; // Slower patrol speed
			const maxMove = moveSpeed * deltaTime;
			const moveDistance = math.min(maxMove, distance);
			const newPosition = currentPosition.add(direction.Unit.mul(moveDistance));

			const lookDirection = direction.Unit;
			const newCFrame = CFrame.lookAt(newPosition, newPosition.add(lookDirection));
			part.CFrame = newCFrame;
		}
	}
}

export class InvestigateBehavior implements AIBehavior {
	name = "Investigate";
	priority = 4;

	canExecute(context: AIContext): boolean {
		const investigatePosition = context.blackboard.investigatePosition as Vector3;
		return investigatePosition !== undefined;
	}

	execute(context: AIContext): AIBehaviorResult {
		const investigatePosition = context.blackboard.investigatePosition as Vector3;
		if (!investigatePosition) {
			return { success: false, completed: true };
		}

		const distance = context.position.sub(investigatePosition).Magnitude;

		if (distance <= 3) {
			// Reached investigation point
			const investigateTime = context.blackboard.investigateTime as number || 0;
			const newInvestigateTime = investigateTime + context.deltaTime;

			if (newInvestigateTime >= 3) {
				// Finished investigating
				context.blackboard.investigatePosition = undefined;
				context.blackboard.investigateTime = 0;
				return { success: true, completed: true };
			} else {
				// Look around while investigating
				this.lookAround(context);
				context.blackboard.investigateTime = newInvestigateTime;
			}
		} else {
			// Move towards investigation point
			this.moveTowards(context, investigatePosition);
			PositionHelper.lookAt(context.entity, investigatePosition);
		}

		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		print(`🔍 ${context.entityId} is investigating`);
		context.blackboard.investigateTime = 0;
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		// Use simple obstacle avoidance for investigation
		const clearTarget = PositionHelper.findClearPath(context.position, targetPosition, [context.entity]);

		// Use smooth Roblox movement for investigation
		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				humanoid.MoveTo(clearTarget);
			} else {
				this.smoothInvestigateMove(context.entity.PrimaryPart, clearTarget, context.deltaTime);
			}
		} else if (context.entity.IsA("BasePart")) {
			this.smoothInvestigateMove(context.entity as BasePart, clearTarget, context.deltaTime);
		}
	}

	private smoothInvestigateMove(part: BasePart, targetPosition: Vector3, deltaTime: number): void {
		const currentPosition = part.Position;
		const direction = targetPosition.sub(currentPosition);
		const distance = direction.Magnitude;

		if (distance > 0.1) {
			const moveSpeed = 12; // Medium speed for investigation
			const maxMove = moveSpeed * deltaTime;
			const moveDistance = math.min(maxMove, distance);
			const newPosition = currentPosition.add(direction.Unit.mul(moveDistance));

			const lookDirection = direction.Unit;
			const newCFrame = CFrame.lookAt(newPosition, newPosition.add(lookDirection));
			part.CFrame = newCFrame;
		}
	}

	private lookAround(context: AIContext): void {
		const time = tick();
		const angle = time * 2; // Rotate over time
		const lookDirection = new Vector3(math.cos(angle), 0, math.sin(angle));
		const lookPosition = context.position.add(lookDirection.mul(10));
		
		PositionHelper.lookAt(context.entity, lookPosition);
	}
}

export class FleeBehavior implements AIBehavior {
	name = "Flee";
	priority = 8;

	canExecute(context: AIContext): boolean {
		if (!context.target || !context.targetPosition) return false;
		
		const distance = context.position.sub(context.targetPosition).Magnitude;
		const shouldFlee = context.blackboard.shouldFlee as boolean || false;
		
		return shouldFlee || distance < 8; // Flee if too close or marked to flee
	}

	execute(context: AIContext): AIBehaviorResult {
		if (!context.target || !context.targetPosition) {
			return { success: false, completed: true };
		}

		// Run away from target using smooth movement
		const fleeDirection = context.position.sub(context.targetPosition).Unit;
		const fleeTarget = context.position.add(fleeDirection.mul(10)); // Flee 10 studs away

		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				humanoid.MoveTo(fleeTarget);
				humanoid.WalkSpeed = 20; // Fast flee speed
			} else {
				this.smoothFleeMove(context.entity.PrimaryPart, fleeTarget, context.deltaTime);
			}
		} else if (context.entity.IsA("BasePart")) {
			this.smoothFleeMove(context.entity as BasePart, fleeTarget, context.deltaTime);
		}

		// Check if we've fled far enough
		const distance = context.position.sub(context.targetPosition).Magnitude;
		if (distance > 25) {
			context.blackboard.shouldFlee = false;
			return { success: true, completed: true };
		}

		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		print(`😱 ${context.entityId} is fleeing!`);
	}

	private smoothFleeMove(part: BasePart, targetPosition: Vector3, deltaTime: number): void {
		const currentPosition = part.Position;
		const direction = targetPosition.sub(currentPosition);
		const distance = direction.Magnitude;

		if (distance > 0.1) {
			const moveSpeed = 20; // Fast flee speed
			const maxMove = moveSpeed * deltaTime;
			const moveDistance = math.min(maxMove, distance);
			const newPosition = currentPosition.add(direction.Unit.mul(moveDistance));

			const lookDirection = direction.Unit;
			const newCFrame = CFrame.lookAt(newPosition, newPosition.add(lookDirection));
			part.CFrame = newCFrame;
		}
	}
}

export class AttackBehavior implements AIBehavior {
	name = "Attack";
	priority = 7;

	canExecute(context: AIContext): boolean {
		if (!context.target || !context.targetPosition) return false;

		const distance = context.position.sub(context.targetPosition).Magnitude;
		return distance <= 8; // Attack if within 8 studs
	}

	execute(context: AIContext): AIBehaviorResult {
		if (!context.target || !context.targetPosition) {
			return { success: false, completed: true };
		}

		const distance = context.position.sub(context.targetPosition).Magnitude;

		// Move closer if not in attack range
		if (distance > 5) {
			this.moveTowards(context, context.targetPosition);
			PositionHelper.lookAt(context.entity, context.targetPosition);
			return { success: true, completed: false };
		}

		// Perform attack
		const lastAttackTime = context.blackboard.lastAttackTime as number || 0;
		const currentTime = tick();
		const attackCooldown = 2; // 2 seconds between attacks

		if (currentTime - lastAttackTime >= attackCooldown) {
			this.performAttack(context);
			context.blackboard.lastAttackTime = currentTime;
		}

		// Look at target while attacking
		PositionHelper.lookAt(context.entity, context.targetPosition);

		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		print(`⚔️ ${context.entityId} is attacking!`);
		context.blackboard.lastAttackTime = 0;
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		const clearTarget = PositionHelper.findClearPath(context.position, targetPosition, [context.entity]);

		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				humanoid.MoveTo(clearTarget);
				humanoid.WalkSpeed = 18; // Aggressive movement speed
			} else {
				this.smoothAttackMove(context.entity.PrimaryPart, clearTarget, context.deltaTime);
			}
		} else if (context.entity.IsA("BasePart")) {
			this.smoothAttackMove(context.entity as BasePart, clearTarget, context.deltaTime);
		}
	}

	private smoothAttackMove(part: BasePart, targetPosition: Vector3, deltaTime: number): void {
		const currentPosition = part.Position;
		const direction = targetPosition.sub(currentPosition);
		const distance = direction.Magnitude;

		if (distance > 0.1) {
			const moveSpeed = 18; // Aggressive movement speed
			const maxMove = moveSpeed * deltaTime;
			const moveDistance = math.min(maxMove, distance);
			const newPosition = currentPosition.add(direction.Unit.mul(moveDistance));

			const lookDirection = direction.Unit;
			const newCFrame = CFrame.lookAt(newPosition, newPosition.add(lookDirection));
			part.CFrame = newCFrame;
		}
	}

	private performAttack(context: AIContext): void {
		// Simple attack effect - you can customize this
		print(`💥 ${context.entityId} attacks!`);

		// Add visual effect at target position
		if (context.targetPosition) {
			// You could spawn an effect here using your EffectPartBuilder
			// For now, just print the attack
		}
	}
}

export class WanderBehavior implements AIBehavior {
	name = "Wander";
	priority = 2;

	canExecute(context: AIContext): boolean {
		// Wander if no target and not patrolling
		return !context.target && !context.blackboard.patrolPoints;
	}

	execute(context: AIContext): AIBehaviorResult {
		const wanderTarget = context.blackboard.wanderTarget as Vector3;
		const wanderTime = context.blackboard.wanderTime as number || 0;
		const maxWanderTime = 5; // Change direction every 5 seconds

		// Generate new wander target if needed
		if (!wanderTarget || wanderTime >= maxWanderTime) {
			const newTarget = this.generateWanderTarget(context.position);
			context.blackboard.wanderTarget = newTarget;
			context.blackboard.wanderTime = 0;
		} else {
			context.blackboard.wanderTime = wanderTime + context.deltaTime;
		}

		// Move towards wander target
		const currentTarget = context.blackboard.wanderTarget as Vector3;
		if (currentTarget) {
			const distance = context.position.sub(currentTarget).Magnitude;

			if (distance > 2) {
				this.moveTowards(context, currentTarget);
			} else {
				// Reached target, generate new one
				context.blackboard.wanderTarget = undefined;
				context.blackboard.wanderTime = maxWanderTime;
			}
		}

		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		print(`🚶 ${context.entityId} is wandering around`);
		context.blackboard.wanderTarget = undefined;
		context.blackboard.wanderTime = 0;
	}

	private generateWanderTarget(currentPosition: Vector3): Vector3 {
		const wanderRadius = 15;
		const randomAngle = math.random() * math.pi * 2;
		const randomDistance = math.random() * wanderRadius;

		const offset = new Vector3(
			math.cos(randomAngle) * randomDistance,
			0,
			math.sin(randomAngle) * randomDistance
		);

		return currentPosition.add(offset);
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		const clearTarget = PositionHelper.findClearPath(context.position, targetPosition, [context.entity]);

		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				humanoid.MoveTo(clearTarget);
				humanoid.WalkSpeed = 6; // Slow wandering speed
			} else {
				this.smoothWanderMove(context.entity.PrimaryPart, clearTarget, context.deltaTime);
			}
		} else if (context.entity.IsA("BasePart")) {
			this.smoothWanderMove(context.entity as BasePart, clearTarget, context.deltaTime);
		}
	}

	private smoothWanderMove(part: BasePart, targetPosition: Vector3, deltaTime: number): void {
		const currentPosition = part.Position;
		const direction = targetPosition.sub(currentPosition);
		const distance = direction.Magnitude;

		if (distance > 0.1) {
			const moveSpeed = 6; // Slow wandering speed
			const maxMove = moveSpeed * deltaTime;
			const moveDistance = math.min(maxMove, distance);
			const newPosition = currentPosition.add(direction.Unit.mul(moveDistance));

			const lookDirection = direction.Unit;
			const newCFrame = CFrame.lookAt(newPosition, newPosition.add(lookDirection));
			part.CFrame = newCFrame;
		}
	}
}
