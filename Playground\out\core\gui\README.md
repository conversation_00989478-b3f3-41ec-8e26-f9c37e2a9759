# Core GUI Framework - Responsive Design Guide

This guide explains how to use the updated Core GUI framework with responsive design capabilities. The framework automatically adapts to different screen sizes and device types while preventing content overflow and positioning issues.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Responsive Manager](#responsive-manager)
3. [Dynamic Positioning](#dynamic-positioning)
4. [Preventing Overflow](#preventing-overflow)
5. [Responsive Design Tips](#responsive-design-tips)
6. [Component Examples](#component-examples)
7. [Best Practices](#best-practices)

## Quick Start

```tsx
import { ResponsiveManager, ContainerFrame, Button } from "@roboxgames/core";

// Get responsive manager instance
const responsiveManager = ResponsiveManager.getInstance();

// Create responsive components
<ContainerFrame responsive={true} responsiveMargin={true}>
  <Button text="Responsive Button" responsive={true} />
</ContainerFrame>
```

## Responsive Manager

The `ResponsiveManager` is a singleton class that handles all responsive calculations and device detection.

### Key Features

- **Screen Size Detection**: Automatically detects current viewport size
- **Device Type Classification**: Mobile, tablet, or desktop detection
- **Safe Area Insets**: Accounts for mobile control areas
- **Viewport Change Listeners**: Real-time responsiveness to screen changes

### Basic Usage

```tsx
const responsiveManager = ResponsiveManager.getInstance();

// Get current screen information
const screenSize = responsiveManager.getScreenSize();
const deviceType = responsiveManager.getDeviceType();
const isMobile = responsiveManager.isMobile();

// Listen for screen size changes
const unsubscribe = responsiveManager.onScreenSizeChange((newSize) => {
  print(`Screen changed to: ${newSize.width}x${newSize.height}`);
});
```

## Dynamic Positioning

### Using getRelativePosition()

Convert pixel positions to responsive scale values:

```tsx
const responsiveManager = ResponsiveManager.getInstance();

// Convert 100px from left, 50px from top to scale values
const position = responsiveManager.getRelativePosition({ x: 100, y: 50 });

<ContainerFrame
  position={position}
  anchorPoint={new Vector2(0, 0)}
/>
```

### Safe Area Aware Positioning

Account for mobile control areas:

```tsx
const safeAreaInsets = responsiveManager.getSafeAreaInsets();
const bottomMargin = 20 + safeAreaInsets.bottom;

<ContainerFrame
  position={new UDim2(0.5, 0, 1, -bottomMargin)}
  anchorPoint={new Vector2(0.5, 1)}
/>
```

### Responsive Margins

Use device-appropriate margins:

```tsx
const margin = responsiveManager.getResponsiveMargin(16);
// Returns: 12.8px on mobile, 16px on tablet, 19.2px on desktop

<ContainerFrame
  position={new UDim2(0, margin, 0, margin)}
/>
```

## Preventing Overflow

### AutomaticSize for Content

Always use AutomaticSize for dynamic content:

```tsx
<VerticalFrame fitContent={true}>
  <Label text="Dynamic text content" autoSize={true} />
  <Button text="Auto-sized button" autoSize={true} />
</VerticalFrame>
```

### ScrollingFrame with AutomaticCanvasSize

For scrollable content:

```tsx
<ScrollingFrame
  size={new UDim2(1, 0, 0, 300)}
  automaticCanvasSize={Enum.AutomaticSize.Y}
  scrollingDirection={Enum.ScrollingDirection.Y}
>
  <VerticalFrame fitContent={true}>
    {/* Content that may exceed frame height */}
  </VerticalFrame>
</ScrollingFrame>
```

### Size Constraints

Prevent components from becoming too large or small:

```tsx
<ContainerFrame
  responsive={true}
  minSize={new UDim2(0, 200, 0, 100)}
  maxSize={new UDim2(0, 600, 0, 400)}
>
  {/* Content with size limits */}
</ContainerFrame>
```

## Responsive Design Tips

### 1. Always Use Scale for Main Positioning

```tsx
// ✅ Good - Uses scale values
<ContainerFrame position={new UDim2(0.5, 0, 0.1, 0)} />

// ❌ Bad - Uses fixed pixel values
<ContainerFrame position={new UDim2(0, 400, 0, 100)} />
```

### 2. Use Offset for Fine-Tuning

```tsx
// ✅ Good - Scale for main position, offset for fine-tuning
<ContainerFrame position={new UDim2(0.5, -10, 1, -50)} />
```

### 3. Enable Responsive Properties

```tsx
<VerticalFrame
  responsive={true}
  responsiveMargin={true}
  spacing={8} // Will be adjusted based on device type
>
```

### 4. Listen for Screen Changes

```tsx
React.useEffect(() => {
  const unsubscribe = responsiveManager.onScreenSizeChange((screenSize) => {
    // Update component state based on new screen size
    setComponentSize(calculateNewSize(screenSize));
  });
  
  return unsubscribe; // Cleanup on unmount
}, []);
```

## Component Examples

### Responsive Bottom-Left Panel

```tsx
function ResponsiveBottomPanel() {
  const responsiveManager = ResponsiveManager.getInstance();
  const safeAreaInsets = responsiveManager.getSafeAreaInsets();
  
  const containerWidth = responsiveManager.isMobile() ? 100 : 120;
  const marginLeft = responsiveManager.getResponsiveMargin(16);
  const marginBottom = responsiveManager.getResponsiveMargin(20) + safeAreaInsets.bottom;

  return (
    <VerticalFrame
      size={new UDim2(0, containerWidth, 0, 200)}
      position={new UDim2(0, marginLeft, 1, -marginBottom)}
      anchorPoint={new Vector2(0, 1)}
      responsive={true}
      responsiveMargin={true}
    >
      <Button text="Action 1" responsive={true} />
      <Button text="Action 2" responsive={true} />
    </VerticalFrame>
  );
}
```

### Responsive Action Bar

```tsx
function ResponsiveActionBar() {
  const responsiveManager = ResponsiveManager.getInstance();
  const safeAreaInsets = responsiveManager.getSafeAreaInsets();
  
  const containerWidth = responsiveManager.isMobile() ? 320 : 400;
  const bottomMargin = responsiveManager.getResponsiveMargin(40) + safeAreaInsets.bottom;

  return (
    <ContainerFrame
      size={new UDim2(0, containerWidth, 0, 80)}
      position={new UDim2(0.5, 0, 1, -bottomMargin)}
      anchorPoint={new Vector2(0.5, 0)}
      responsive={true}
      minSize={new UDim2(0, 280, 0, 60)}
      maxSize={new UDim2(0, 500, 0, 100)}
    >
      {/* Action bar content */}
    </ContainerFrame>
  );
}
```

### Responsive Grid

```tsx
<Grid
  rows={3}
  cols={4}
  responsive={true}
  maxWidth={600}
  cellType="button"
>
  {/* Grid items */}
</Grid>
```

## Best Practices

### ✅ Do's

1. **Use ResponsiveManager** for all positioning calculations
2. **Enable responsive props** on components that need adaptation
3. **Use AutomaticSize** for dynamic content
4. **Account for safe areas** on mobile devices
5. **Test on multiple screen sizes** during development
6. **Use scale values** for main positioning
7. **Add size constraints** to prevent extreme scaling

### ❌ Don'ts

1. **Don't use fixed pixel positions** for main layout
2. **Don't ignore safe area insets** on mobile
3. **Don't forget to cleanup** screen change listeners
4. **Don't hardcode device-specific values**
5. **Don't use absolute positioning** without responsive calculations
6. **Don't forget ClipsDescendants** for overflow prevention

### Testing Your Responsive Design

Use the built-in verification tools:

```tsx
import { runResponsiveTests } from "@roboxgames/core/gui/test/ResponsiveVerification";

// Run automated tests
runResponsiveTests();

// Or use the interactive test panel
<ResponsiveTest isOpen={true} onClose={() => setTestOpen(false)} />
```

## Migration from Fixed Positioning

If you have existing components with fixed positioning, here's how to migrate:

### Before (Fixed)
```tsx
<ContainerFrame
  size={new UDim2(0, 400, 0, 200)}
  position={new UDim2(0, 50, 1, -250)}
/>
```

### After (Responsive)
```tsx
const responsiveManager = ResponsiveManager.getInstance();
const safeAreaInsets = responsiveManager.getSafeAreaInsets();
const width = responsiveManager.isMobile() ? 320 : 400;
const margin = responsiveManager.getResponsiveMargin(50);
const bottomOffset = responsiveManager.getResponsiveMargin(50) + safeAreaInsets.bottom;

<ContainerFrame
  size={new UDim2(0, width, 0, 200)}
  position={new UDim2(0, margin, 1, -bottomOffset)}
  responsive={true}
  responsiveMargin={true}
  minSize={new UDim2(0, 280, 0, 150)}
  maxSize={new UDim2(0, 500, 0, 300)}
/>
```

This ensures your GUI works perfectly across all devices and screen sizes!
