import { TweenService } from "@rbxts/services";
import { AnimationBuilder } from "./AnimationBuilder";
import { getJoint } from "./CharacterJointManager";

export class LimbAnimator {
    private character: Model;

    constructor(character: Model) {
        this.character = character;
    }

    static for<PERSON><PERSON>cter(character: Model): LimbAnimator {
        return new <PERSON>bAnimator(character);
    }

    moveLimb(limbName: string, relativeCFrame: CFrame, duration = 0.3, easingStyle = Enum.EasingStyle.Quad, easingDirection = Enum.EasingDirection.Out): void {
        const joint = getJoint(this.character, limbName);
        if (joint) {
            AnimationBuilder.forJoint(joint)
                .to(relativeCFrame)
                .duration(duration)
                .easing(easingStyle, easingDirection)
                .play();
        }
    }

    moveBodyPart(partName: string, relativeCFrame: CFrame, duration = 0.3, easingStyle = Enum.EasingStyle.Quad, easingDirection = Enum.EasingDirection.Out): void {
        const joint = getJoint(this.character, partName);
        if (joint) {
            AnimationBuilder.forJoint(joint)
                .to(relativeCFrame)
                .duration(duration)
                .easing(easingStyle, easingDirection)
                .play();
        }
    }

    lunge(directionVector: Vector3, studs = 5, duration = 0.3, easingStyle = Enum.EasingStyle.Back, easingDirection = Enum.EasingDirection.Out): void {
        const humanoidRootPart = this.character.FindFirstChild("HumanoidRootPart") as Part | undefined;
        if (humanoidRootPart) {
            const originalCFrame = humanoidRootPart.CFrame;
            const lungeVector = directionVector.Unit.mul(studs);
            TweenService.Create(
                humanoidRootPart,
                new TweenInfo(duration, easingStyle, easingDirection),
                { CFrame: originalCFrame.add(lungeVector) }
            ).Play();
        }
    }
}