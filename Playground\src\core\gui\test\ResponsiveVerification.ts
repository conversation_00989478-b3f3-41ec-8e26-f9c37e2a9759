import { ResponsiveManager } from "../layout/ResponsiveManager";

export class ResponsiveVerification {
    private responsiveManager: ResponsiveManager;
    
    constructor() {
        this.responsiveManager = ResponsiveManager.getInstance();
    }
    
    public runAllTests(): void {
        print("🧪 Starting Responsive GUI Verification Tests...");
        print("=" .rep(50));
        
        this.testScreenSizeDetection();
        this.testDeviceTypeDetection();
        this.testResponsivePositioning();
        this.testResponsiveSizing();
        this.testSafeAreaInsets();
        this.testViewportChangeHandling();
        this.testEdgeCases();
        
        print("=" .rep(50));
        print("✅ All Responsive GUI Tests Completed!");
    }
    
    private testScreenSizeDetection(): void {
        print("\n📏 Testing Screen Size Detection:");
        
        const screenSize = this.responsiveManager.getScreenSize();
        
        // Verify screen size is valid
        const isValidWidth = screenSize.width > 0 && screenSize.width <= 7680; // Max 8K width
        const isValidHeight = screenSize.height > 0 && screenSize.height <= 4320; // Max 8K height
        const isValidAspectRatio = screenSize.aspectRatio > 0.5 && screenSize.aspectRatio < 4.0;
        
        print(`  Screen Size: ${screenSize.width}x${screenSize.height}`);
        print(`  Aspect Ratio: ${screenSize.aspectRatio}`);
        print(`  Width Valid: ${isValidWidth ? "✅" : "❌"}`);
        print(`  Height Valid: ${isValidHeight ? "✅" : "❌"}`);
        print(`  Aspect Valid: ${isValidAspectRatio ? "✅" : "❌"}`);
    }
    
    private testDeviceTypeDetection(): void {
        print("\n📱 Testing Device Type Detection:");
        
        const deviceType = this.responsiveManager.getDeviceType();
        const isMobile = this.responsiveManager.isMobile();
        const isTablet = this.responsiveManager.isTablet();
        const isDesktop = this.responsiveManager.isDesktop();
        
        print(`  Device Type: ${deviceType}`);
        print(`  Is Mobile: ${isMobile}`);
        print(`  Is Tablet: ${isTablet}`);
        print(`  Is Desktop: ${isDesktop}`);
        
        // Verify only one device type is true
        const deviceCount = (isMobile ? 1 : 0) + (isTablet ? 1 : 0) + (isDesktop ? 1 : 0);
        print(`  Single Device Type: ${deviceCount === 1 ? "✅" : "❌"}`);
    }
    
    private testResponsivePositioning(): void {
        print("\n📍 Testing Responsive Positioning:");
        
        // Test various positions
        const testCases = [
            { x: 0, y: 0, name: "Top-Left" },
            { x: 100, y: 50, name: "Small Offset" },
            { x: 500, y: 300, name: "Medium Offset" },
            { x: 1000, y: 600, name: "Large Offset" }
        ];
        
        testCases.forEach(testCase => {
            const position = this.responsiveManager.getRelativePosition(testCase);
            const isValidScale = position.X.Scale >= 0 && position.X.Scale <= 1 && 
                               position.Y.Scale >= 0 && position.Y.Scale <= 1;
            
            print(`  ${testCase.name} (${testCase.x},${testCase.y}): Scale(${math.floor(position.X.Scale * 1000) / 1000}, ${math.floor(position.Y.Scale * 1000) / 1000}) ${isValidScale ? "✅" : "❌"}`);
        });
    }
    
    private testResponsiveSizing(): void {
        print("\n📐 Testing Responsive Sizing:");
        
        const testSizes = [
            { width: 100, height: 50, name: "Small" },
            { width: 300, height: 200, name: "Medium" },
            { width: 600, height: 400, name: "Large" }
        ];
        
        testSizes.forEach(testSize => {
            const size = this.responsiveManager.getResponsiveSize(testSize);
            const isValidScale = size.X.Scale >= 0 && size.X.Scale <= 1 && 
                               size.Y.Scale >= 0 && size.Y.Scale <= 1;
            
            print(`  ${testSize.name} (${testSize.width}x${testSize.height}): Scale(${math.floor(size.X.Scale * 1000) / 1000}, ${math.floor(size.Y.Scale * 1000) / 1000}) ${isValidScale ? "✅" : "❌"}`);
        });
        
        // Test with constraints
        const constrainedSize = this.responsiveManager.getResponsiveSize(
            { width: 200, height: 100 },
            { width: 150, height: 80 },
            { width: 300, height: 150 }
        );
        print(`  Constrained Size: ${constrainedSize.X.Scale > 0 && constrainedSize.Y.Scale > 0 ? "✅" : "❌"}`);
    }
    
    private testSafeAreaInsets(): void {
        print("\n🛡️ Testing Safe Area Insets:");
        
        const safeArea = this.responsiveManager.getSafeAreaInsets();
        const isMobile = this.responsiveManager.isMobile();
        
        print(`  Top: ${safeArea.top}px`);
        print(`  Bottom: ${safeArea.bottom}px`);
        print(`  Left: ${safeArea.left}px`);
        print(`  Right: ${safeArea.right}px`);
        
        // Mobile should have safe areas, desktop should not
        const hasSafeAreas = safeArea.bottom > 0 || safeArea.left > 0 || safeArea.right > 0;
        const safeAreaLogic = isMobile ? hasSafeAreas : !hasSafeAreas;
        print(`  Safe Area Logic: ${safeAreaLogic ? "✅" : "❌"}`);
    }
    
    private testViewportChangeHandling(): void {
        print("\n🔄 Testing Viewport Change Handling:");
        
        let callbackTriggered = false;
        
        // Register a callback
        const unsubscribe = this.responsiveManager.onScreenSizeChange((screenSize) => {
            callbackTriggered = true;
            print(`  Callback triggered with size: ${screenSize.width}x${screenSize.height}`);
        });
        
        print(`  Callback registered: ✅`);
        
        // Clean up
        unsubscribe();
        print(`  Callback unsubscribed: ✅`);
    }
    
    private testEdgeCases(): void {
        print("\n⚠️ Testing Edge Cases:");
        
        // Test responsive margins with different values
        const margins = [0, 5, 10, 20, 50];
        margins.forEach(margin => {
            const responsiveMargin = this.responsiveManager.getResponsiveMargin(margin);
            const isValid = responsiveMargin >= 0 && responsiveMargin <= margin * 2; // Should be within reasonable bounds
            print(`  Margin ${margin}px -> ${responsiveMargin}px: ${isValid ? "✅" : "❌"}`);
        });
        
        // Test extreme positions
        const extremePosition = this.responsiveManager.getRelativePosition({ x: 99999, y: 99999 });
        const handlesExtreme = extremePosition.X.Scale <= 1 && extremePosition.Y.Scale <= 1;
        print(`  Extreme Position Handling: ${handlesExtreme ? "✅" : "❌"}`);
        
        // Test zero size
        const zeroSize = this.responsiveManager.getResponsiveSize({ width: 0, height: 0 });
        const handlesZero = zeroSize.X.Scale >= 0 && zeroSize.Y.Scale >= 0;
        print(`  Zero Size Handling: ${handlesZero ? "✅" : "❌"}`);
    }
    
    public simulateScreenSizes(): void {
        print("\n🖥️ Simulating Different Screen Sizes:");
        
        const screenSizes = [
            { width: 375, height: 667, name: "iPhone SE" },
            { width: 414, height: 896, name: "iPhone 11" },
            { width: 768, height: 1024, name: "iPad" },
            { width: 1024, height: 768, name: "iPad Landscape" },
            { width: 1920, height: 1080, name: "Desktop FHD" },
            { width: 2560, height: 1440, name: "Desktop QHD" }
        ];
        
        screenSizes.forEach(screen => {
            // Simulate what would happen with this screen size
            const aspectRatio = screen.width / screen.height;
            const deviceType = screen.width < 768 ? "mobile" : screen.width < 1024 ? "tablet" : "desktop";
            
            print(`  ${screen.name} (${screen.width}x${screen.height}): ${deviceType}, AR: ${math.floor(aspectRatio * 100) / 100}`);
        });
    }
}

// Export test function for easy access
export function runResponsiveTests(): void {
    const verification = new ResponsiveVerification();
    verification.runAllTests();
    verification.simulateScreenSizes();
}
