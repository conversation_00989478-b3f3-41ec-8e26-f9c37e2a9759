import { TweenService } from "@rbxts/services";

export class EffectTweenBuilder {
    private instance: Instance;
    private tweenInfo: TweenInfo;
    private properties: { [key: string]: unknown } = {};
    private delayTime = 0;
    private completionCallback?: () => void;

    constructor(instance: Instance) {
        this.instance = instance;
        this.tweenInfo = new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out);
    }

    static for(instance: Instance): EffectTweenBuilder {
        return new EffectTweenBuilder(instance);
    }

    expand(size: Vector3): this {
        this.properties.Size = size;
        return this;
    }

    fade(trans: number): this {
        this.properties.Transparency = trans;
        return this;
    }

    move(pos: Vector3): this {
        this.properties.Position = pos;
        return this;
    }

    rotate(angles: CFrame): this {
        this.properties.CFrame = (this.instance as BasePart).CFrame.mul(angles);
        return this;
    }

    duration(time: number): this {
        this.tweenInfo = new TweenInfo(time, this.tweenInfo.EasingStyle, this.tweenInfo.EasingDirection);
        return this;
    }

    easing(style: Enum.EasingStyle, direction: Enum.EasingDirection): this {
        this.tweenInfo = new TweenInfo(this.tweenInfo.Time, style, direction);
        return this;
    }

    delay(delay: number): this {
        this.delayTime = delay;
        return this;
    }

    brightness(brightness: number): this {
        this.properties.Brightness = brightness;
        return this;
    }

    onComplete(callback: () => void): this {
        this.completionCallback = callback;
        return this;
    }

    play(): Tween {
        const tween = TweenService.Create(this.instance, this.tweenInfo, this.properties);
        if (this.delayTime > 0) {
            task.delay(this.delayTime, () => tween.Play());
        } else {
            tween.Play();
        }
        if (this.completionCallback) {
            tween.Completed.Connect(this.completionCallback);
        }
        return tween;
    }
}