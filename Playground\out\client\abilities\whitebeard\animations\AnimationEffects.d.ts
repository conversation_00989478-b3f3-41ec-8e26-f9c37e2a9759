export declare function findBodyJoints(character: Model): Map<string, Motor6D | undefined>;
export declare function animateFullBodyPunch(rightShoulder: Motor6D, bodyJoints: Map<string, Motor6D | undefined>): void;
export declare function createWindUpAnimation(rightShoulder: Motor6D, bodyJoints: Map<string, Motor6D | undefined>, humanoidRootPart?: Part): void;
export declare function createExplosivePunch(rightShoulder: Motor6D, bodyJoints: Map<string, Motor6D | undefined>, humanoidRootPart?: Part): void;
export declare function animateCrossPunch(rightShoulder: Motor6D, bodyJoints: Map<string, Motor6D | undefined>): void;
export declare function createCrossArmsWindUp(rightShoulder: Motor6D, leftShoulder: Motor6D, bodyJoints: Map<string, Motor6D | undefined>, humanoidRootPart?: Part): void;
export declare function createExplosiveDoublePunch(rightShoulder: Motor6D, leftShoulder: Motor6D, bodyJoints: Map<string, Motor6D | undefined>, humanoidRootPart?: Part): void;
export declare function restoreBodyJoints(rightShoulder: Motor6D, bodyJoints: Map<string, Motor6D | undefined>): void;
