import * as React from "@rbxts/react";
import { ContainerFrame, VerticalFrame, Button, Label, ScrollingFrame, IconButton, useZIndex } from "../../core";
import { DebugManager } from "../../core/debug";
import { COLORS, BORDER_RADIUS } from "../../core/design";

interface DebugPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export function DebugPanel(props: DebugPanelProps) {
  const [debugEnabled, setDebugEnabled] = React.useState(false);
  const [aiDebugEnabled, setAiDebugEnabled] = React.useState(false);
  const [playerDebugEnabled, setPlayerDebugEnabled] = React.useState(false);
  const [performanceEnabled, setPerformanceEnabled] = React.useState(false);
  const [pathsEnabled, setPathsEnabled] = React.useState(false);
  const [visionEnabled, setVisionEnabled] = React.useState(false);

  const debugManager = DebugManager.getInstance();

  // Automatic Z-Index management - this will appear above other panels
  const { zIndex } = useZIndex("debug-panel", true);

  const toggleMainDebug = () => {
    const newState = !debugEnabled;
    setDebugEnabled(newState);

    // Always toggle the debug manager to match our state
    if (newState && !debugManager.isEnabled()) {
      debugManager.toggle();
    } else if (!newState && debugManager.isEnabled()) {
      debugManager.toggle();
    }

    print(`🔧 Debug overlay ${newState ? "ENABLED" : "DISABLED"}`);
  };

  const toggleAIDebug = () => {
    const newState = !aiDebugEnabled;
    setAiDebugEnabled(newState);
    debugManager.setConfig({ showAI: newState });
  };

  const togglePlayerDebug = () => {
    const newState = !playerDebugEnabled;
    setPlayerDebugEnabled(newState);
    debugManager.setConfig({ showPlayers: newState });
  };

  const togglePerformance = () => {
    const newState = !performanceEnabled;
    setPerformanceEnabled(newState);
    debugManager.setConfig({ showPerformance: newState });
  };

  const togglePaths = () => {
    const newState = !pathsEnabled;
    setPathsEnabled(newState);
    debugManager.setConfig({ showPaths: newState });
  };

  const toggleVision = () => {
    const newState = !visionEnabled;
    setVisionEnabled(newState);
    debugManager.setConfig({ showVision: newState });
  };

  const runDebugDemo = () => {
    // Make sure debug overlay is enabled first
    if (!debugEnabled) {
      print("❌ Enable Debug Overlay first!");
      return;
    }

    const Players = game.GetService("Players");
    const localPlayer = Players.LocalPlayer;

    if (localPlayer && localPlayer.Character && localPlayer.Character.PrimaryPart) {
      const playerPos = localPlayer.Character.PrimaryPart.Position;

      print("🎨 Running debug demo...");

      // Draw coordinate axes
      debugManager.drawLine(playerPos, playerPos.add(new Vector3(10, 0, 0)), Color3.fromRGB(255, 0, 0), 8);
      debugManager.drawLine(playerPos, playerPos.add(new Vector3(0, 10, 0)), Color3.fromRGB(0, 255, 0), 8);
      debugManager.drawLine(playerPos, playerPos.add(new Vector3(0, 0, 10)), Color3.fromRGB(0, 0, 255), 8);

      // Draw some spheres
      debugManager.drawSphere(playerPos.add(new Vector3(5, 5, 5)), 2, Color3.fromRGB(255, 255, 0), 8);
      debugManager.drawSphere(playerPos.add(new Vector3(-5, 5, -5)), 1.5, Color3.fromRGB(255, 0, 255), 8);
      debugManager.drawSphere(playerPos.add(new Vector3(0, 0, 0)), 0.5, Color3.fromRGB(255, 255, 255), 8);

      // Draw some text
      debugManager.drawText(playerPos.add(new Vector3(0, 12, 0)), "Debug Demo Active!", Color3.fromRGB(255, 255, 255), 8);
      debugManager.drawText(playerPos.add(new Vector3(10, 2, 0)), "X-Axis", Color3.fromRGB(255, 0, 0), 8);
      debugManager.drawText(playerPos.add(new Vector3(0, 10, 2)), "Y-Axis", Color3.fromRGB(0, 255, 0), 8);
      debugManager.drawText(playerPos.add(new Vector3(2, 2, 10)), "Z-Axis", Color3.fromRGB(0, 0, 255), 8);

      // Log debug messages
      debugManager.logDebug("Demo", "Debug system demonstration started", { position: playerPos });
      debugManager.logDebug("Demo", "Drawing coordinate axes and markers");

      print(`🔍 Debug demo running! Visual elements will appear for 8 seconds`);
      print(`🎯 Look around your character to see the debug visuals`);
    } else {
      print("❌ Could not get player position for demo");
    }
  };

  // Update state when panel opens
  React.useEffect(() => {
    if (props.isOpen) {
      const config = debugManager.getConfig();
      setDebugEnabled(debugManager.isEnabled());
      setAiDebugEnabled(config.showAI);
      setPlayerDebugEnabled(config.showPlayers);
      setPerformanceEnabled(config.showPerformance);
      setPathsEnabled(config.showPaths);
      setVisionEnabled(config.showVision);
    }
  }, [props.isOpen]);

  if (!props.isOpen) return <></>;

  return (
    <>
      {/* Debug Panel using Core Components */}
      <ContainerFrame
        size={new UDim2(0, 600, 0, 500)}
        position={new UDim2(0.5, 0, 0.5, 0)}
        anchorPoint={new Vector2(0.5, 0.5)}
        backgroundColor={COLORS.bg.base}
        backgroundTransparency={0}
        borderThickness={1}
        borderColor={COLORS.border.l2}
        cornerRadius={BORDER_RADIUS.md}
        zIndex={zIndex}
      >
        {/* Header with Title and Close Button */}
        <ContainerFrame
          size={new UDim2(1, 0, 0, 60)}
          position={new UDim2(0, 0, 0, 0)}
          backgroundTransparency={0}
          borderThickness={0}
        >
          {/* Title on the left */}
          <Label
            text="🔧 Debug System Controls"
            fontSize={16}
            bold={true}
            position={new UDim2(0, 16, 0.5, 0)}
            anchorPoint={new Vector2(0, 0.5)}
            size={new UDim2(1, -60, 0, 20)} // Leave space for close button
          />

          {/* Close button on the right */}
          <IconButton
            icon="✕"
            onClick={props.onClose}
            size={new UDim2(0, 32, 0, 32)}
            position={new UDim2(1, -16, 0.5, 0)}
            anchorPoint={new Vector2(1, 0.5)}
          />
        </ContainerFrame>

        {/* Content Area with Scrolling */}
        <ScrollingFrame
          size={new UDim2(1, 0, 1, -60)}
          position={new UDim2(0, 0, 0, 60)}
          backgroundTransparency={1}
          borderThickness={0}
          scrollingDirection={Enum.ScrollingDirection.Y}
          automaticCanvasSize={Enum.AutomaticSize.Y}
        >
          <VerticalFrame spacing={16} padding={16}>

          {/* Main Debug Control Section */}
          <VerticalFrame spacing={8} padding={0}>
            <Label
              text="🔧 Main Debug Control"
              fontSize={16}
              bold={true}
            />

            <Label
              text="Enable the debug overlay to see visual debugging information in-game."
              fontSize={12}
              textWrapped={true}
              size={new UDim2(1, 0, 0, 0)} // Full width, auto height
              autoSize={true}
            />
          </VerticalFrame>

          {/* Main Debug Button */}
          <VerticalFrame spacing={8} padding={0}>
            <Button
              text={debugEnabled ? "🟢 Debug Overlay: ENABLED" : "🔴 Debug Overlay: DISABLED"}
              onClick={toggleMainDebug}
            />
          </VerticalFrame>

          {/* Debug Features Section */}
          <VerticalFrame spacing={8} padding={0}>
            <Label
              text="🎛️ Debug Features"
              fontSize={16}
              bold={true}
            />

            <Label
              text="Toggle individual debug features. Debug overlay must be enabled first."
              fontSize={12}
              textWrapped={true}
              size={new UDim2(1, 0, 0, 0)} // Full width, auto height
              autoSize={true}
            />
          </VerticalFrame>

          {/* Debug Feature Buttons */}
          <VerticalFrame spacing={8} padding={0}>
            <Button
              text={aiDebugEnabled ? "🤖 AI Debug: ON" : "🤖 AI Debug: OFF"}
              onClick={toggleAIDebug}
            />

            <Button
              text={playerDebugEnabled ? "👤 Player Debug: ON" : "👤 Player Debug: OFF"}
              onClick={togglePlayerDebug}
            />

            <Button
              text={performanceEnabled ? "⚡ Performance Monitor: ON" : "⚡ Performance Monitor: OFF"}
              onClick={togglePerformance}
            />

            <Button
              text={pathsEnabled ? "🛤️ Path Visualization: ON" : "🛤️ Path Visualization: OFF"}
              onClick={togglePaths}
            />

            <Button
              text={visionEnabled ? "👁️ Vision Cones: ON" : "👁️ Vision Cones: OFF"}
              onClick={toggleVision}
            />
          </VerticalFrame>

          {/* Debug Demo Section */}
          <VerticalFrame spacing={8} padding={0}>
            <Label
              text="🎨 Debug Demo"
              fontSize={16}
              bold={true}
            />

            <Label
              text="Run a visual demonstration of the debug system with colored lines, spheres, and text."
              fontSize={12}
              textWrapped={true}
              size={new UDim2(1, 0, 0, 0)} // Full width, auto height
              autoSize={true}
            />
          </VerticalFrame>

          {/* Demo Button */}
          <VerticalFrame spacing={8} padding={0}>
            <Button
              text="🎨 Run Visual Demo"
              onClick={runDebugDemo}
            />
          </VerticalFrame>

          {/* Instructions Section */}
          <VerticalFrame spacing={8} padding={0}>
            <Label
              text="📖 How to Use"
              fontSize={16}
              bold={true}
            />

            <Label
              text="1. Enable Debug Overlay first • 2. Toggle features • 3. Spawn AI NPCs in World panel"
              fontSize={12}
              textWrapped={true}
              size={new UDim2(1, 0, 0, 0)} // Full width, auto height
              autoSize={true}
            />
            <Label
              text="4. Run visual demo • 5. Performance monitor shows FPS and memory stats"
              fontSize={12}
              textWrapped={true}
              size={new UDim2(1, 0, 0, 0)} // Full width, auto height
              autoSize={true}
            />
          </VerticalFrame>

          </VerticalFrame>
        </ScrollingFrame>
      </ContainerFrame>
    </>
  );
}
