-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local ResponsiveVerification
do
	ResponsiveVerification = setmetatable({}, {
		__tostring = function()
			return "ResponsiveVerification"
		end,
	})
	ResponsiveVerification.__index = ResponsiveVerification
	function ResponsiveVerification.new(...)
		local self = setmetatable({}, ResponsiveVerification)
		return self:constructor(...) or self
	end
	function ResponsiveVerification:constructor()
		self.responsiveManager = ResponsiveManager:getInstance()
	end
	function ResponsiveVerification:runAllTests()
		print("🧪 Starting Responsive GUI Verification Tests...")
		print(string.rep("=", 50))
		self:testScreenSizeDetection()
		self:testDeviceTypeDetection()
		self:testResponsivePositioning()
		self:testResponsiveSizing()
		self:testSafeAreaInsets()
		self:testViewportChangeHandling()
		self:testEdgeCases()
		print(string.rep("=", 50))
		print("✅ All Responsive GUI Tests Completed!")
	end
	function ResponsiveVerification:testScreenSizeDetection()
		print("\n📏 Testing Screen Size Detection:")
		local screenSize = self.responsiveManager:getScreenSize()
		-- Verify screen size is valid
		local isValidWidth = screenSize.width > 0 and screenSize.width <= 7680
		local isValidHeight = screenSize.height > 0 and screenSize.height <= 4320
		local isValidAspectRatio = screenSize.aspectRatio > 0.5 and screenSize.aspectRatio < 4.0
		print(`  Screen Size: {screenSize.width}x{screenSize.height}`)
		print(`  Aspect Ratio: {screenSize.aspectRatio}`)
		print(`  Width Valid: {if isValidWidth then "✅" else "❌"}`)
		print(`  Height Valid: {if isValidHeight then "✅" else "❌"}`)
		print(`  Aspect Valid: {if isValidAspectRatio then "✅" else "❌"}`)
	end
	function ResponsiveVerification:testDeviceTypeDetection()
		print("\n📱 Testing Device Type Detection:")
		local deviceType = self.responsiveManager:getDeviceType()
		local isMobile = self.responsiveManager:isMobile()
		local isTablet = self.responsiveManager:isTablet()
		local isDesktop = self.responsiveManager:isDesktop()
		print(`  Device Type: {deviceType}`)
		print(`  Is Mobile: {isMobile}`)
		print(`  Is Tablet: {isTablet}`)
		print(`  Is Desktop: {isDesktop}`)
		-- Verify only one device type is true
		local deviceCount = (if isMobile then 1 else 0) + (if isTablet then 1 else 0) + (if isDesktop then 1 else 0)
		print(`  Single Device Type: {if deviceCount == 1 then "✅" else "❌"}`)
	end
	function ResponsiveVerification:testResponsivePositioning()
		print("\n📍 Testing Responsive Positioning:")
		-- Test various positions
		local testCases = { {
			x = 0,
			y = 0,
			name = "Top-Left",
		}, {
			x = 100,
			y = 50,
			name = "Small Offset",
		}, {
			x = 500,
			y = 300,
			name = "Medium Offset",
		}, {
			x = 1000,
			y = 600,
			name = "Large Offset",
		} }
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(testCase)
			local position = self.responsiveManager:getRelativePosition(testCase)
			local isValidScale = position.X.Scale >= 0 and position.X.Scale <= 1 and position.Y.Scale >= 0 and position.Y.Scale <= 1
			print(`  {testCase.name} ({testCase.x},{testCase.y}): Scale({math.floor(position.X.Scale * 1000) / 1000}, {math.floor(position.Y.Scale * 1000) / 1000}) {if isValidScale then "✅" else "❌"}`)
		end
		for _k, _v in testCases do
			_callback(_v, _k - 1, testCases)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	function ResponsiveVerification:testResponsiveSizing()
		print("\n📐 Testing Responsive Sizing:")
		local testSizes = { {
			width = 100,
			height = 50,
			name = "Small",
		}, {
			width = 300,
			height = 200,
			name = "Medium",
		}, {
			width = 600,
			height = 400,
			name = "Large",
		} }
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(testSize)
			local size = self.responsiveManager:getResponsiveSize(testSize)
			local isValidScale = size.X.Scale >= 0 and size.X.Scale <= 1 and size.Y.Scale >= 0 and size.Y.Scale <= 1
			print(`  {testSize.name} ({testSize.width}x{testSize.height}): Scale({math.floor(size.X.Scale * 1000) / 1000}, {math.floor(size.Y.Scale * 1000) / 1000}) {if isValidScale then "✅" else "❌"}`)
		end
		for _k, _v in testSizes do
			_callback(_v, _k - 1, testSizes)
		end
		-- ▲ ReadonlyArray.forEach ▲
		-- Test with constraints
		local constrainedSize = self.responsiveManager:getResponsiveSize({
			width = 200,
			height = 100,
		}, {
			width = 150,
			height = 80,
		}, {
			width = 300,
			height = 150,
		})
		print(`  Constrained Size: {if constrainedSize.X.Scale > 0 and constrainedSize.Y.Scale > 0 then "✅" else "❌"}`)
	end
	function ResponsiveVerification:testSafeAreaInsets()
		print("\n🛡️ Testing Safe Area Insets:")
		local safeArea = self.responsiveManager:getSafeAreaInsets()
		local isMobile = self.responsiveManager:isMobile()
		print(`  Top: {safeArea.top}px`)
		print(`  Bottom: {safeArea.bottom}px`)
		print(`  Left: {safeArea.left}px`)
		print(`  Right: {safeArea.right}px`)
		-- Mobile should have safe areas, desktop should not
		local hasSafeAreas = safeArea.bottom > 0 or safeArea.left > 0 or safeArea.right > 0
		local safeAreaLogic = if isMobile then hasSafeAreas else not hasSafeAreas
		print(`  Safe Area Logic: {if safeAreaLogic then "✅" else "❌"}`)
	end
	function ResponsiveVerification:testViewportChangeHandling()
		print("\n🔄 Testing Viewport Change Handling:")
		local callbackTriggered = false
		-- Register a callback
		local unsubscribe = self.responsiveManager:onScreenSizeChange(function(screenSize)
			callbackTriggered = true
			print(`  Callback triggered with size: {screenSize.width}x{screenSize.height}`)
		end)
		print(`  Callback registered: ✅`)
		-- Clean up
		unsubscribe()
		print(`  Callback unsubscribed: ✅`)
	end
	function ResponsiveVerification:testEdgeCases()
		print("\n⚠️ Testing Edge Cases:")
		-- Test responsive margins with different values
		local margins = { 0, 5, 10, 20, 50 }
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(margin)
			local responsiveMargin = self.responsiveManager:getResponsiveMargin(margin)
			local isValid = responsiveMargin >= 0 and responsiveMargin <= margin * 2
			print(`  Margin {margin}px -> {responsiveMargin}px: {if isValid then "✅" else "❌"}`)
		end
		for _k, _v in margins do
			_callback(_v, _k - 1, margins)
		end
		-- ▲ ReadonlyArray.forEach ▲
		-- Test extreme positions
		local extremePosition = self.responsiveManager:getRelativePosition({
			x = 99999,
			y = 99999,
		})
		local handlesExtreme = extremePosition.X.Scale <= 1 and extremePosition.Y.Scale <= 1
		print(`  Extreme Position Handling: {if handlesExtreme then "✅" else "❌"}`)
		-- Test zero size
		local zeroSize = self.responsiveManager:getResponsiveSize({
			width = 0,
			height = 0,
		})
		local handlesZero = zeroSize.X.Scale >= 0 and zeroSize.Y.Scale >= 0
		print(`  Zero Size Handling: {if handlesZero then "✅" else "❌"}`)
	end
	function ResponsiveVerification:simulateScreenSizes()
		print("\n🖥️ Simulating Different Screen Sizes:")
		local screenSizes = { {
			width = 375,
			height = 667,
			name = "iPhone SE",
		}, {
			width = 414,
			height = 896,
			name = "iPhone 11",
		}, {
			width = 768,
			height = 1024,
			name = "iPad",
		}, {
			width = 1024,
			height = 768,
			name = "iPad Landscape",
		}, {
			width = 1920,
			height = 1080,
			name = "Desktop FHD",
		}, {
			width = 2560,
			height = 1440,
			name = "Desktop QHD",
		} }
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(screen)
			-- Simulate what would happen with this screen size
			local aspectRatio = screen.width / screen.height
			local deviceType = if screen.width < 768 then "mobile" elseif screen.width < 1024 then "tablet" else "desktop"
			print(`  {screen.name} ({screen.width}x{screen.height}): {deviceType}, AR: {math.floor(aspectRatio * 100) / 100}`)
		end
		for _k, _v in screenSizes do
			_callback(_v, _k - 1, screenSizes)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
end
-- Export test function for easy access
local function runResponsiveTests()
	local verification = ResponsiveVerification.new()
	verification:runAllTests()
	verification:simulateScreenSizes()
end
return {
	runResponsiveTests = runResponsiveTests,
	ResponsiveVerification = ResponsiveVerification,
}
