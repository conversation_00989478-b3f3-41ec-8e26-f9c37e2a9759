-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local CameraShakeHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core").CameraShakeHelper
local function createCameraShake(intensity, duration)
	if intensity == nil then
		intensity = 5
	end
	if duration == nil then
		duration = 0.5
	end
	-- Use Core framework CameraShakeHelper - exact same behavior
	CameraShakeHelper:shake(intensity, duration, `whitebeard_{tick()}`)
	print(`🔥 Camera shake started: intensity={intensity}, duration={duration}`)
end
return {
	createCameraShake = createCameraShake,
}
