-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local createDustCloudShockwave = TS.import(script, script.Parent, "DustCloud").createDustCloudShockwave
local createGroundCracks = TS.import(script, script.Parent, "GroundCracks").createGroundCracks
local function createMapShockwave(centerPosition, maxRadius, duration)
	if maxRadius == nil then
		maxRadius = 200
	end
	if duration == nil then
		duration = 2
	end
	-- Create beautiful dust cloud shockwave effect
	createDustCloudShockwave(centerPosition, maxRadius, duration)
	-- Create ground crack pattern
	createGroundCracks(centerPosition, maxRadius * 0.7)
	print(`🌊 Enhanced shockwave system created: radius={maxRadius}, duration={duration}`)
end
return {
	createMapShockwave = createMapShockwave,
}
