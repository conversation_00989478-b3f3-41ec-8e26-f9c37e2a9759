// Example integration of Core World systems with Whitebeard abilities
// This shows how to use the World system for epic environmental effects

import { TweenService } from "@rbxts/services";
import {
    DestructibleManager,
    PhysicsImpactHelper,
    WorldEventBroadcaster,
    type DestructionOptions,
    type GravityZoneOptions,
    type BarrierZoneOptions,
    type EarthquakeEvent,
    type ObjectType
} from "../../../core/world/index";

export class WhitebeardWorldIntegration {
    
    /**
     * Enhanced Whitebeard punch with full environmental destruction
     */
    public static executeQuakePunch(
        player: Player, 
        punchPosition: Vector3, 
        punchType: "single" | "double"
    ): void {
        const intensity = punchType === "double" ? 1.0 : 0.7;
        const radius = punchType === "double" ? 100 : 60;

        // 1. Create destructible zone for building damage
        const destructionOptions: DestructionOptions = {
            center: punchPosition,
            radius: radius,
            destructionType: "earthquake",
            intensity: intensity,
            duration: 30, // Effects last 30 seconds
            effects: [
                { type: "cracks", intensity: intensity, duration: 60 },
                { type: "debris", intensity: intensity * 0.8, duration: 45 },
                { type: "dust", intensity: intensity * 0.6, duration: 20 }
            ],
            createDebris: true,
            soundEffect: "whitebeard_quake"
        };

        const destructionZoneId = DestructibleManager.createDestructibleZone(destructionOptions);

        // 2. Create gravity distortion zone
        const gravityOptions: GravityZoneOptions = {
            center: punchPosition,
            radius: radius * 0.7,
            zoneType: "gravity",
            intensity: intensity,
            duration: 15,
            affectedObjects: ["players", "parts", "debris"],
            gravityMultiplier: 0.3, // Reduced gravity in quake zone
            visualEffect: true
        };

        const gravityZoneId = PhysicsImpactHelper.createGravityZone(gravityOptions);

        // 3. Trigger world-wide earthquake event
        const earthquakeOptions: EarthquakeEvent = {
            eventType: "earthquake",
            center: punchPosition,
            radius: radius * 2, // Earthquake felt much further
            duration: 25,
            intensity: intensity,
            magnitude: punchType === "double" ? 8.5 : 7.0,
            epicenter: punchPosition,
            shockwaveSpeed: 50,
            aftershockCount: 3,
            affectedPlayers: "all", // Everyone feels the earthquake
            visualEffects: true,
            soundEffects: true,
            environmentalChanges: true
        };

        const earthquakeEventId = WorldEventBroadcaster.triggerEarthquake(earthquakeOptions);

        // 4. Create expanding shockwave effect
        this.createShockwaveEffect(punchPosition, radius, intensity);

        // 5. Apply force to nearby objects
        this.applyQuakeForces(punchPosition, radius, intensity);

        print(`🥊 Whitebeard ${punchType} punch executed with full world effects!`);
        print(`   - Destruction Zone: ${destructionZoneId}`);
        print(`   - Gravity Zone: ${gravityZoneId}`);
        print(`   - Earthquake Event: ${earthquakeEventId}`);
    }

    /**
     * Room ability with spatial manipulation
     */
    public static executeRoomAbility(
        player: Player,
        roomCenter: Vector3,
        roomRadius: number
    ): void {
        // 1. Create gravity manipulation zone
        const gravityOptions: GravityZoneOptions = {
            center: roomCenter,
            radius: roomRadius,
            zoneType: "gravity",
            intensity: 1.0,
            duration: 60, // Room lasts 1 minute
            affectedObjects: ["players", "parts", "debris"],
            gravityMultiplier: 0.1, // Very low gravity
            gravityDirection: new Vector3(0, 0, 0), // Zero gravity
            visualEffect: true
        };

        const roomZoneId = PhysicsImpactHelper.createGravityZone(gravityOptions);

        // 2. Create barrier around the room
        const barrierOptions: BarrierZoneOptions = {
            center: roomCenter,
            radius: roomRadius,
            zoneType: "barrier",
            intensity: 1.0,
            duration: 60,
            affectedObjects: ["players", "parts"] as ObjectType[],
            barrierType: "dome",
            visualEffect: true
        };

        const barrierZoneId = PhysicsImpactHelper.createBarrierZone(barrierOptions);

        print(`🏠 Room ability activated!`);
        print(`   - Gravity Zone: ${roomZoneId}`);
        print(`   - Barrier Zone: ${barrierZoneId}`);
    }

    /**
     * Ice Age ability with environmental freezing
     */
    public static executeIceAge(
        player: Player,
        freezeCenter: Vector3,
        freezeRadius: number
    ): void {
        // 1. Create freeze destruction zone
        const freezeOptions: DestructionOptions = {
            center: freezeCenter,
            radius: freezeRadius,
            destructionType: "freeze",
            intensity: 0.9,
            duration: 120, // Ice lasts 2 minutes
            effects: [
                { type: "ice", intensity: 1.0, duration: 120, color: new Color3(0.7, 0.9, 1) }
            ],
            affectedMaterials: [Enum.Material.Water, Enum.Material.Concrete],
            createDebris: false
        };

        const freezeZoneId = DestructibleManager.createDestructibleZone(freezeOptions);

        // 2. Trigger blizzard weather event
        const blizzardOptions = {
            eventType: "blizzard" as const,
            center: freezeCenter,
            radius: freezeRadius * 1.5,
            duration: 90,
            intensity: 0.8,
            affectedPlayers: "nearby" as const,
            visualEffects: true,
            soundEffects: true,
            environmentalChanges: true
        };

        // Note: Would need to implement blizzard in WorldEventBroadcaster
        print(`❄️ Ice Age ability activated!`);
        print(`   - Freeze Zone: ${freezeZoneId}`);
    }

    /**
     * Create expanding shockwave visual effect
     */
    private static createShockwaveEffect(
        center: Vector3, 
        maxRadius: number, 
        intensity: number
    ): void {
        // Create expanding ring effect
        const shockwave = new Instance("Part");
        shockwave.Name = "QuakeShockwave";
        shockwave.Size = new Vector3(1, 1, 1);
        shockwave.Position = center;
        shockwave.Shape = Enum.PartType.Cylinder;
        shockwave.Material = Enum.Material.ForceField;
        shockwave.Transparency = 0.5;
        shockwave.Color = new Color3(0.8, 0.6, 0.2); // Earthquake color
        shockwave.CanCollide = false;
        shockwave.Anchored = true;
        shockwave.Parent = game.Workspace;

        // Animate expansion
        const expandTween = TweenService.Create(shockwave,
            new TweenInfo(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            {
                Size: new Vector3(maxRadius * 2, 5, maxRadius * 2),
                Transparency: 1
            }
        );

        expandTween.Play();
        expandTween.Completed.Connect(() => shockwave.Destroy());
    }

    /**
     * Apply forces to objects from quake
     */
    private static applyQuakeForces(
        center: Vector3,
        radius: number,
        intensity: number
    ): void {
        // Find all parts in radius and apply upward force
        for (const descendant of game.Workspace.GetDescendants()) {
            if (descendant.IsA("Part") && !descendant.Anchored) {
                const distance = center.sub(descendant.Position).Magnitude;
                if (distance <= radius) {
                    const forceStrength = intensity * (1 - distance / radius) * 50;
                    
                    const bodyVelocity = new Instance("BodyVelocity");
                    bodyVelocity.MaxForce = new Vector3(4000, 4000, 4000);
                    bodyVelocity.Velocity = new Vector3(
                        (math.random() - 0.5) * forceStrength,
                        forceStrength * 0.7, // Upward force
                        (math.random() - 0.5) * forceStrength
                    );
                    bodyVelocity.Parent = descendant;

                    // Remove force after short time
                    task.delay(1, () => {
                        if (bodyVelocity.Parent) {
                            bodyVelocity.Destroy();
                        }
                    });
                }
            }
        }
    }

    /**
     * Combo ability: Earthquake + Tsunami
     */
    public static executeEarthquakeTsunami(
        player: Player,
        epicenter: Vector3,
        magnitude: number
    ): void {
        // 1. First trigger earthquake
        const earthquakeOptions: EarthquakeEvent = {
            eventType: "earthquake",
            center: epicenter,
            radius: magnitude * 20,
            duration: 15,
            intensity: magnitude / 10,
            magnitude: magnitude,
            epicenter: epicenter,
            shockwaveSpeed: 60,
            aftershockCount: 2,
            affectedPlayers: "all",
            visualEffects: true,
            soundEffects: true,
            environmentalChanges: true
        };

        const earthquakeId = WorldEventBroadcaster.triggerEarthquake(earthquakeOptions);

        // 2. After 10 seconds, trigger tsunami
        task.delay(10, () => {
            const tsunamiOptions = {
                eventType: "tsunami" as const,
                center: epicenter,
                radius: magnitude * 30,
                duration: 45,
                intensity: magnitude / 10,
                waveHeight: magnitude * 2,
                waveSpeed: 40,
                origin: epicenter,
                direction: new Vector3(1, 0, 0), // East direction
                affectedPlayers: "all" as const,
                visualEffects: true,
                soundEffects: true,
                environmentalChanges: true
            };

            const tsunamiId = WorldEventBroadcaster.triggerTsunami(tsunamiOptions);
            
            print(`🌊 Combo attack: Earthquake → Tsunami!`);
            print(`   - Earthquake: ${earthquakeId}`);
            print(`   - Tsunami: ${tsunamiId}`);
        });
    }
}
