export interface DestructionOptions {
    center: Vector3;
    radius: number;
    destructionType: "earthquake" | "explosion" | "slash" | "impact" | "freeze" | "burn";
    intensity: number; // 0.1 to 1.0
    duration?: number; // seconds, undefined = permanent
    effects?: DestructionEffect[];
    affectedMaterials?: Enum.Material[];
    excludeAnchored?: boolean;
    createDebris?: boolean;
    soundEffect?: string;
}

export interface DestructionEffect {
    type: "cracks" | "debris" | "dust" | "smoke" | "sparks" | "ice" | "fire";
    intensity: number;
    duration: number;
    color?: Color3;
    size?: number;
}

export interface DestructibleZone {
    id: string;
    center: Vector3;
    radius: number;
    createdAt: number;
    expiresAt?: number;
    destructionType: string;
    affectedParts: Part[];
    originalStates: Map<Part, PartState>;
}

export interface PartState {
    position: Vector3;
    rotation: Vector3;
    size: Vector3;
    transparency: number;
    material: Enum.Material;
    color: Color3;
    anchored: boolean;
}
