// Core World System - Environmental interactions and world state management
// Universal utilities for destructible environments, physics zones, world events, and state management

// Environment Management
export { DestructibleManager } from "./environment/DestructibleManager";
export {
    type DestructionOptions,
    type DestructionEffect,
    type DestructibleZone,
    type PartState
} from "./environment/interfaces/DestructionOptions";

// Physics Management
import { PhysicsImpactHelper } from "./physics/PhysicsImpactHelper";
export { PhysicsImpactHelper };
export {
    type PhysicsZoneOptions,
    type GravityZoneOptions,
    type ForceZoneOptions,
    type BarrierZoneOptions,
    type PhysicsZone,
    type ForceApplication,
    type ObjectType
} from "./physics/interfaces/PhysicsZoneOptions";

// World Events
import { WorldEventBroadcaster } from "./events/WorldEventBroadcaster";
export { WorldEventBroadcaster };
export {
    type WorldEventOptions,
    type WorldEventType,
    type EarthquakeEvent,
    type TsunamiEvent,
    type MeteorEvent,
    type LightningStormEvent,
    type WorldEvent,
    type EventPhase,
    type EventEffect
} from "./events/interfaces/WorldEventOptions";

// World State Management
import { WorldStateManager } from "./state/WorldStateManager";
export { WorldStateManager };
export { WeatherController } from "./state/WeatherController";
export { TimeController } from "./state/TimeController";
export { GravityController } from "./state/GravityController";
export {
    type WorldStateOptions,
    type WeatherOptions,
    type TimeOptions,
    type GravityOptions,
    type AtmosphereOptions,
    type WorldStateEvent,
    type WeatherType,
    type TimeOfDay,
    type GravityLevel
} from "./state/interfaces/WorldStateOptions";

/**
 * Initialize all Core World systems
 * Call this once in your main server script
 */
export function initializeCoreWorld(): void {
    PhysicsImpactHelper.initialize();
    WorldEventBroadcaster.initialize();
    WorldStateManager.initialize();

    print("🌍 Core World systems initialized successfully!");
}

/**
 * Shutdown all Core World systems
 * Call this when shutting down the server
 */
export function shutdownCoreWorld(): void {
    PhysicsImpactHelper.shutdown();
    WorldEventBroadcaster.shutdown();
    WorldStateManager.getInstance().cleanup();

    print("🌍 Core World systems shutdown complete!");
}
