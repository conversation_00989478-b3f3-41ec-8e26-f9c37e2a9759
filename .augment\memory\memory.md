# Development Environment
- User has a Roblox game development setup using Roblox TS (TypeScript) and <PERSON><PERSON>jo for direct sync into Roblox with watch mode enabled.
- User doesn't want me to build or run code during development - they handle that themselves with their Roblox TS and Rojo setup.
- User wants to work on both RoboxGames/Playground and RoboxGames/Core projects simultaneously in the same VSCode workspace with structure: Roblox/Playground/src and Roblox/Core/src.
- User wants to work only in Core/Playground workspace, not separate Core project, and needs to create core\server folder for better server-client synchronization in the current setup.
- User prefers to use only the master branch in their GitHub repository and wants to remove the main branch.
- Rojo requires restart when making changes to the Core framework project to sync updates properly to sync updates properly to Roblox Studio.
- I am roblox-ai working with TypeScript/Roblox-TS on Core framework and Playground projects, using file separation for classes/namespaces, modern clean code.
- Roblox-TS does not support null values and requires using undefined instead of null throughout the codebase.
- Roblox-TS latest version is 3.0.0 (released September 12, 2024) with key updates: TypeScript 5.5.3 support, Generic JSX for any React-like library, optimized loops enabled by default, .luau file extension support, breaking changes requiring @rbxts/roact 3.0.1+ for JSX, roblox-lua-promise upgraded to 4.0.0, and files now emit as .luau by default.
- Roblox-TS 2025 updates: Active development continues with recent commits including SharedTable iteration support (June 23), bitwise operations optimization (June 23), $tuple() type assertion fixes (June 24), rest support for spread destructuring (June 16), optional chaining tuples fixes (June 17), Jest testing framework migration replacing Mocha (June 17), Codecov integration replacing coveralls (June 17), 95% PR coverage requirement added (July 1), and regular dependency updates throughout 2025.
- NEVER create any Readme.md files. NEVER create any examples files or folders. ALWAYS ask the user if he wants to see the new implementations in existing code.
- Always separate classes into different files - never multiple classes in one file. When interfaces are needed for a class, organize as: PlayerDataSyncHelper.ts goes to core\server\player\PlayerDataSync.ts, interfaces go to core\server\player\interfaces\PlayerDataUpdate.ts, etc.

# Core Library Design and Publishing
- User has Playground repository for testing and Core repository as a reusable framework/library designed to make game development easier for future projects.
- Core library should be designed as a comprehensive helper system for all games (not just GUI), serving as the main development acceleration tool for RoboxGames organization.
- User wants to expand Core framework with additional components to accelerate future game layout development.
- User wants to implement a GuiBuilder class in Core/src framework with fluent API pattern using method chaining for easier GUI creation.
- User's GitHub organization is RoboxGames with Playground repo at https://github.com/RoboxGames/Playground and prefers naming the shared GUI library 'CORE'.
- User wants to publish Core framework as private npm package @roboxgames/core for use across multiple games in their organization.
- User prefers local file path imports instead of NPM package imports for the Core framework during local development.
- Core framework design system should be internal-only and not accessible to Playground or external users, and imports should not use @roboxgames/core/src pattern.
- In Playground project, imports from Core framework must use @roboxgames/core package syntax, not relative paths like ../core/.
- Client code should never directly import design system values (COLORS, SIZES, TYPOGRAPHY, BORDER_RADIUS) from the core - components should have proper defaults built-in and client should only use the components themselves.
- Core framework should only contain helper utilities (effects, animations, sounds helpers) to make code cleaner, never pre-made effects like sphereeffects, and when refactoring existing effects to use Core helpers, the animations must remain 100% identical in behavior.
- User wants Core framework to provide better server-client synchronization for abilities to avoid duplicating code between server and client implementations.
- Core framework should never contain pre-made code like specific abilities (whitebeard, etc.) - it's only for universal helpers and utilities that work across all future projects, so ability-specific managers are not appropriate for Core.
- ALWAYS use reusable Core framework helpers like effects folder (EffectPartBuilder, EffectTweenBuilder, SoundHelper, ParticleHelper, etc.) to create effects in state management systems instead of raw Roblox instances.
- ALWAYS separate interfaces into individual files rather than having multiple interfaces in one file - follow the established pattern of one interface per file.

# UI Components and Styling
- User wants vertical and horizontal frame components in Core framework for stacking elements with consistent sizing, useful for layouts like ActionBar.
- User prefers to organize Frame components (ContainerFrame, HorizontalFrame, VerticalFrame, ScrollingFrame) into separate files within the frame folder rather than having them all in one Frame.tsx file or a separate scrolling folder.
- User wants a new DropdownButton component in Core framework with dropdown menu functionality to toggle layout examples on/off.
- User prefers to always use existing Core framework components instead of creating raw Roblox instances to maintain consistency and reuse established patterns.
- Always ensure GUI components reuse existing components instead of raw Roblox elements and follow the design system consistently when making any changes - use ContainerFrame, HorizontalFrame, VerticalFrame, Button, IconButton, ListItemButton, Label, Image, Overlay components instead of raw frame, textbutton, textlabel, imagelabel elements.
- User prefers to reuse existing components in every GUI component and ensure all components use the established design system.
- User prefers dropdown components to have clean backgrounds without weird styling and wants dropdown z-index to default to highest value to render above all windows.
- User prefers dropdown components to close after item selection, only show one layout example at a time (unless 'Show All' selected), and wants QuickActionDropdown pattern.
- User wants a 'hide all' option in dropdown components to close all open examples at once.
- User prefers Roblox native UI style without colored borders/accents for menus and GUI elements.
- User prefers dark mode as the default theme for GUI components in the Core framework.
- User prefers buttons in vertical layouts to have consistent visual size appearance.
- User prefers that when UI components use transparent backgrounds, all visual elements including outlines should also be removed for clean styling.
- User prefers UI components to always use autosize to fit all content, utilize full width and height available, while maintaining proper padding rules from the design system.
- User prefers using standard/default colors from the design system instead of hardcoded colors in UI components, and expects high-quality button appearance.
- User prefers to always use default values from Core framework for all colors and styling - no custom color choices, always use the default values provided by the Core system.
- User prefers button hover effects to work consistently whether buttons are standalone or inside layouts/frames, avoiding transparency issues on standalone buttons.
- User wants component testing UI with left sidebar listing all components and right panel showing examples when selected, all in one unified window.
- User wants a centralized design system for colors, borders, and styling that can be reused across components and easily modified when needed.
- User prefers design system to be organized as separate files (colors, paddings, etc.) in the design folder rather than a single DesignSystem.ts file.
- User prefers removing Component namespace from spacing (wants default same spacing for all components), questions need for re-exports in design system, and wants to remove DesignSystem.ts file.
- Core framework should have all new properties as defaults (transparency support, icon support, improved hover colors, auto-resize) to prevent future compatibility issues.
- Modal components should only close when clicking the backdrop or close button, not when clicking on the modal content itself, and icon display issues need to be addressed for proper UI feedback.
- User wants component testing lab improvements for better display and prefers Input components to be more customizable with additional configuration options.
- User prefers that UI components (like ActionBar slots) always fit properly inside their frames without layout issues.
- User prefers that every component should have a default corner radius as part of the design system.
- Modal component should use Button component instead of raw textbutton, and create a reusable ScrollingFrame component to use in ListView instead of raw scrollingframe.

# Game Development Focus
- User wants to develop One Piece-inspired ability systems with server-side validation and client-server networking.
- The ability system should feature blue energy visual effects with proper cooldown mechanics.
- User prefers One Piece ability effects to have a charging/loading animation that grows larger and then expands dramatically.
- User is interested in adding player animations to the ability system, specifically charging animations over simple sitting poses.
- User wants to implement server-side orbs that fly around in their Roblox game.
- User prefers RGB sparkle effects over square-shaped particles for better visual appeal in ability effects.
- User wants the action bar GUI improved with better slot management and layouts that fit properly without empty space.
- User wants action bar GUI with numbered ability slots (1-5) that show countdown timers and progress bars for ability cooldowns when activated.
- User wants sound effects added to the ability system for audio feedback.
- User wants Room ability to work with specific sequence: right hand moves straight up, circle appears in hand first, then shoots and expands sphere, with stronger gravity effects that make objects fly around, larger sphere size, and should affect spawnpoint and rig objects in the map.
- User prefers ability spheres to persist during punch animations and disappear after, and wants air cracks to appear in mid-air rather than on the ground for better visual impact.
- User prefers punch animations to have more forward movement and wants sphere transparency to be even lower (more opaque) for better visibility.
- User prefers crack effects to look like realistic glass breaking with expanding cracks that simulate punching through glass air, not just simple crack lines.
- User prefers reduced lightning effects in abilities and wants hand spheres to be more visible during ability charging.
- User prefers crack effects to be positioned accurately based on punch direction - single punch cracks should appear in front of the player, cross punch cracks should appear to the left and right sides (not behind the character).
- User prefers refactoring abilities to use Core framework helpers (LimbAnimator, EffectPartBuilder, EffectTweenBuilder, SoundHelper) instead of raw Motor6D/TweenService/Roblox instances while maintaining 100% identical behavior and organizing code into separate effects/ and animations/ folders.

# Animation Creation
- User prefers creating custom animations programmatically using Roblox's animation editor with Motor6D/KeyframeSequence.
- User prefers Motor6D.Transform with TweenService for custom Roblox animations without asset IDs, with detailed logging for character part detection.
- User prefers creating interactive animation GUIs in the client for controlling character animations (arms, legs, etc.).
- User prefers visual drag-and-drop animation interfaces with 3D character models over slider-based controls for creating animations.
- User prefers simplified animation GUIs with only a 3D character model and direct limb manipulation for faster animation creation.
- Always use Core framework elements (LimbAnimator, EffectPartBuilder, EffectTweenBuilder, SoundHelper, etc.) to create new animations and effects instead of raw Roblox instances - maintain consistency with the established Core system.