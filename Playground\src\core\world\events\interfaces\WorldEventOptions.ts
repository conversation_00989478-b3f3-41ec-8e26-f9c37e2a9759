export interface WorldEventOptions {
    eventType: WorldEventType;
    center?: Vector3;
    radius?: number;
    duration?: number;
    intensity: number;
    affectedPlayers?: "all" | "nearby" | "specific";
    specificPlayers?: Player[];
    visualEffects?: boolean;
    soundEffects?: boolean;
    environmentalChanges?: boolean;
}

export type WorldEventType = 
    | "earthquake" 
    | "tsunami" 
    | "meteor" 
    | "lightning_storm" 
    | "blizzard" 
    | "volcanic_eruption"
    | "gravity_shift"
    | "time_dilation"
    | "dimensional_rift"
    | "energy_surge";

export interface EarthquakeEvent extends WorldEventOptions {
    eventType: "earthquake";
    magnitude: number; // 1-10 scale
    epicenter: Vector3;
    shockwaveSpeed: number;
    aftershockCount?: number;
}

export interface TsunamiEvent extends WorldEventOptions {
    eventType: "tsunami";
    waveHeight: number;
    waveSpeed: number;
    origin: Vector3;
    direction: Vector3;
}

export interface MeteorEvent extends WorldEventOptions {
    eventType: "meteor";
    impactPoint: Vector3;
    meteorSize: number;
    fallSpeed: number;
    explosionRadius: number;
}

export interface LightningStormEvent extends WorldEventOptions {
    eventType: "lightning_storm";
    strikeCount: number;
    strikeInterval: number;
    stormCenter: Vector3;
    stormRadius: number;
}

export interface WorldEvent {
    id: string;
    options: WorldEventOptions;
    startTime: number;
    endTime?: number;
    status: "pending" | "active" | "completed" | "cancelled";
    affectedRegions: Region3[];
    participants: Set<Player>;
}

export interface EventPhase {
    name: string;
    startTime: number;
    duration: number;
    effects: EventEffect[];
}

export interface EventEffect {
    type: "visual" | "audio" | "physics" | "environmental";
    data: unknown;
    delay?: number;
    duration?: number;
}
