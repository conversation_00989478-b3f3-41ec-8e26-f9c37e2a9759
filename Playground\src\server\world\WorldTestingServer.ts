import { Workspace, TweenService, Lighting } from "@rbxts/services";
import { NetworkSyncHelper } from "../../core/server";
import { WorldEventBroadcaster, WorldStateManager } from "../../core/world";

interface WorldTestRequest {
    testType: string;
    position: Vector3;
    playerId: number;
}

export class WorldTestingServer {
    private static readonly NORMAL_GRAVITY = 196.2; // Roblox default gravity
    private currentGravityEffect?: Part;
    private currentWeatherEvent?: string;
    private worldStateManager: WorldStateManager;
    private originalLighting?: {
        Brightness: number;
        Ambient: Color3;
        ColorShift_Top: Color3;
        ColorShift_Bottom: Color3;
    };

    constructor() {
        this.worldStateManager = WorldStateManager.getInstance();
        this.setupWorldTesting();
        this.storeOriginalLighting();
        print("🌍 World Testing Server initialized");
    }

    private setupWorldTesting(): void {
        // Create network handler for world testing (gravity + weather)
        NetworkSyncHelper.createSyncedAction<WorldTestRequest, void>(
            "WORLD_TESTING",
            (player, request) => this.handleWorldTest(player, request),
            {
                validateCooldown: true,
                cooldownTime: 1, // 1 second cooldown
                replicateToAll: false
            }
        );
    }

    private storeOriginalLighting(): void {
        this.originalLighting = {
            Brightness: Lighting.Brightness,
            Ambient: Lighting.Ambient,
            ColorShift_Top: Lighting.ColorShift_Top,
            ColorShift_Bottom: Lighting.ColorShift_Bottom
        };
    }

    private handleWorldTest(player: Player, request: WorldTestRequest): void {
        print(`🌍 ${player.Name} testing world: ${request.testType}`);

        // Handle gravity tests
        if (string.find(request.testType, "gravity")[0] !== undefined) {
            this.handleGravityTest(request, player.UserId);
        }
        // Handle weather tests
        else if (string.find(request.testType, "weather")[0] !== undefined) {
            this.handleWeatherTest(request, player.UserId);
        }
        // Handle time tests
        else if (string.find(request.testType, "time")[0] !== undefined) {
            this.handleTimeTest(request, player.UserId);
        }
        else {
            print(`❌ Unknown world test: ${request.testType}`);
        }
    }

    private handleGravityTest(request: WorldTestRequest, playerId: number): void {
        switch (request.testType) {
            case "low_gravity_world":
                this.worldStateManager.applyWorldState({
                    gravity: { level: "low" }
                }, playerId);
                break;
            case "high_gravity_world":
                this.worldStateManager.applyWorldState({
                    gravity: { level: "high" }
                }, playerId);
                break;
            case "zero_gravity_world":
                this.worldStateManager.applyWorldState({
                    gravity: { level: "zero" }
                }, playerId);
                break;
            case "normal_gravity_world":
                this.worldStateManager.applyWorldState({
                    gravity: { level: "normal" }
                }, playerId);
                break;
            default:
                print(`❌ Unknown gravity test: ${request.testType}`);
        }
    }

    private handleWeatherTest(request: WorldTestRequest, playerId: number): void {
        switch (request.testType) {
            case "clear_weather":
                this.worldStateManager.applyWorldState({
                    weather: { type: "clear" }
                }, playerId);
                break;
            case "rain_weather":
                this.worldStateManager.applyWorldState({
                    weather: { type: "rain", intensity: 0.5 }
                }, playerId);
                break;
            case "heavy_rain_weather":
                this.worldStateManager.applyWorldState({
                    weather: { type: "heavy_rain", intensity: 0.8 }
                }, playerId);
                break;
            case "snow_weather":
                this.worldStateManager.applyWorldState({
                    weather: { type: "snow", intensity: 0.5 }
                }, playerId);
                break;
            case "blizzard_weather":
                this.worldStateManager.applyWorldState({
                    weather: { type: "blizzard", intensity: 0.9 }
                }, playerId);
                break;
            case "storm_weather":
                this.worldStateManager.applyWorldState({
                    weather: { type: "storm", intensity: 0.7 }
                }, playerId);
                break;
            case "thunderstorm_weather":
                this.worldStateManager.applyWorldState({
                    weather: { type: "thunderstorm", intensity: 0.8 }
                }, playerId);
                break;
            case "fog_weather":
                this.worldStateManager.applyWorldState({
                    weather: { type: "fog", intensity: 0.6 }
                }, playerId);
                break;
            case "sandstorm_weather":
                this.worldStateManager.applyWorldState({
                    weather: { type: "sandstorm", intensity: 0.7 }
                }, playerId);
                break;
            default:
                print(`❌ Unknown weather test: ${request.testType}`);
        }
    }

    private handleTimeTest(request: WorldTestRequest, playerId: number): void {
        switch (request.testType) {
            case "dawn_time":
                this.worldStateManager.applyWorldState({
                    time: { timeOfDay: "dawn", transitionDuration: 3 }
                }, playerId);
                break;
            case "noon_time":
                this.worldStateManager.applyWorldState({
                    time: { timeOfDay: "noon", transitionDuration: 3 }
                }, playerId);
                break;
            case "dusk_time":
                this.worldStateManager.applyWorldState({
                    time: { timeOfDay: "dusk", transitionDuration: 3 }
                }, playerId);
                break;
            case "night_time":
                this.worldStateManager.applyWorldState({
                    time: { timeOfDay: "night", transitionDuration: 3 }
                }, playerId);
                break;
            default:
                print(`❌ Unknown time test: ${request.testType}`);
        }
    }

    /**
     * Cleanup method
     */
    public cleanup(): void {
        this.worldStateManager.cleanup();
        
        if (this.currentGravityEffect) {
            this.currentGravityEffect.Destroy();
        }
        
        print("🌍 World Testing Server cleaned up");
    }
}