import { Workspace } from "@rbxts/services";

export class CharacterBuilder {
    private model: Model;
    private humanoid?: Humanoid;
    private parts: Map<string, Part> = new Map();

    constructor() {
        this.model = new Instance("Model");
        this.model.Parent = Workspace;
    }

    static create(): CharacterBuilder {
        return new CharacterBuilder();
    }

    name(name: string): this {
        this.model.Name = name;
        return this;
    }

    position(position: Vector3): this {
        if (this.model.PrimaryPart) {
            this.model.PrimaryPart.Position = position;
        }
        return this;
    }

    parent(parent: Instance): this {
        this.model.Parent = parent;
        return this;
    }

    addHumanoid(maxHealth = 100): this {
        this.humanoid = new Instance("Humanoid");
        this.humanoid.MaxHealth = maxHealth;
        this.humanoid.Health = maxHealth;
        this.humanoid.Parent = this.model;
        return this;
    }

    addPart(partName: string, size: Vector3, color?: Color3, shape?: Enum.PartType): this {
        const part = new Instance("Part");
        part.Name = partName;
        part.Size = size;
        
        if (color) {
            part.Color = color;
        }
        
        if (shape) {
            part.Shape = shape;
        }
        
        part.Parent = this.model;
        this.parts.set(partName, part);
        
        return this;
    }

    addHead(color = Color3.fromRGB(255, 184, 148)): this {
        return this.addPart("Head", new Vector3(2, 1, 1), color, Enum.PartType.Ball);
    }

    addTorso(color = Color3.fromRGB(13, 105, 172)): this {
        const torso = this.addPart("Torso", new Vector3(2, 2, 1), color);
        this.model.PrimaryPart = this.parts.get("Torso");
        return torso;
    }

    addArm(side: "Left" | "Right", color = Color3.fromRGB(255, 184, 148)): this {
        const armName = `${side} Arm`;
        return this.addPart(armName, new Vector3(1, 2, 1), color);
    }

    addLeg(side: "Left" | "Right", color = Color3.fromRGB(13, 105, 172)): this {
        const legName = `${side} Leg`;
        return this.addPart(legName, new Vector3(1, 2, 1), color);
    }

    positionPart(partName: string, relativePosition: Vector3): this {
        const part = this.parts.get(partName);
        const primaryPart = this.model.PrimaryPart;
        
        if (part && primaryPart) {
            part.Position = primaryPart.Position.add(relativePosition);
        }
        
        return this;
    }

    addJoint(jointName: string, part0Name: string, part1Name: string, c0: CFrame, c1: CFrame): this {
        const part0 = this.parts.get(part0Name);
        const part1 = this.parts.get(part1Name);
        
        if (part0 && part1) {
            const joint = new Instance("Motor6D");
            joint.Name = jointName;
            joint.Part0 = part0;
            joint.Part1 = part1;
            joint.C0 = c0;
            joint.C1 = c1;
            joint.Parent = part0;
        }
        
        return this;
    }

    addNeckJoint(): this {
        return this.addJoint(
            "Neck",
            "Torso",
            "Head",
            new CFrame(0, 1, 0),
            new CFrame(0, -0.5, 0)
        );
    }

    addShoulderJoint(side: "Left" | "Right"): this {
        const jointName = `${side} Shoulder`;
        const armName = `${side} Arm`;
        const xOffset = side === "Left" ? -1.5 : 1.5;
        
        return this.addJoint(
            jointName,
            "Torso",
            armName,
            new CFrame(xOffset, 0.5, 0),
            new CFrame(0, 1, 0)
        );
    }

    addHipJoint(side: "Left" | "Right"): this {
        const jointName = `${side} Hip`;
        const legName = `${side} Leg`;
        const xOffset = side === "Left" ? -0.5 : 0.5;
        
        return this.addJoint(
            jointName,
            "Torso",
            legName,
            new CFrame(xOffset, -1, 0),
            new CFrame(0, 1, 0)
        );
    }

    autoPositionParts(): this {
        this.positionPart("Head", new Vector3(0, 1.5, 0));
        this.positionPart("Left Arm", new Vector3(-1.5, 0.5, 0));
        this.positionPart("Right Arm", new Vector3(1.5, 0.5, 0));
        this.positionPart("Left Leg", new Vector3(-0.5, -2, 0));
        this.positionPart("Right Leg", new Vector3(0.5, -2, 0));
        return this;
    }

    autoAddJoints(): this {
        if (this.parts.has("Head")) {
            this.addNeckJoint();
        }
        if (this.parts.has("Left Arm")) {
            this.addShoulderJoint("Left");
        }
        if (this.parts.has("Right Arm")) {
            this.addShoulderJoint("Right");
        }
        if (this.parts.has("Left Leg")) {
            this.addHipJoint("Left");
        }
        if (this.parts.has("Right Leg")) {
            this.addHipJoint("Right");
        }
        return this;
    }

    spawn(): Model {
        return this.model;
    }

    // Preset builders for common character types
    static createBasicNPC(name = "NPC", position = new Vector3(0, 0, 0)): Model {
        return CharacterBuilder.create()
            .name(name)
            .addHumanoid(100)
            .addTorso()
            .addHead()
            .position(position)
            .positionPart("Head", new Vector3(0, 1.5, 0))
            .addNeckJoint()
            .spawn();
    }

    static createBasicPlayer(name = "PlayerEntity", position = new Vector3(0, 0, 0)): Model {
        return CharacterBuilder.create()
            .name(name)
            .addHumanoid(100)
            .addTorso()
            .position(position)
            .spawn();
    }

    static createFullCharacter(name = "Character", position = new Vector3(0, 0, 0)): Model {
        return CharacterBuilder.create()
            .name(name)
            .addHumanoid(100)
            .addTorso()
            .addHead()
            .addArm("Left")
            .addArm("Right")
            .addLeg("Left")
            .addLeg("Right")
            .position(position)
            .autoPositionParts()
            .autoAddJoints()
            .spawn();
    }
}
