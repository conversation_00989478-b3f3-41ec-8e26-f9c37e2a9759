-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local TweenService = _services.TweenService
local Workspace = _services.Workspace
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local coreJointFinder = _core.findBodyJoints
local coreRestoreJoints = _core.restoreBodyJoints
local function findBodyJoints(character)
	-- Use Core framework CharacterJointManager - exact same behavior
	return coreJointFinder(character)
end
local createWindUpAnimation, createExplosivePunch
local function animateFullBodyPunch(rightShoulder, bodyJoints)
	-- Get character for movement
	local _result = rightShoulder.Parent
	if _result ~= nil then
		_result = _result.Parent
	end
	local character = _result
	local _result_1 = character
	if _result_1 ~= nil then
		_result_1 = _result_1:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("HumanoidRootPart")
	end
	local humanoidRootPart = _result_1
	-- PHASE 1: Wind-up (0.2s) - Pull back for power
	createWindUpAnimation(rightShoulder, bodyJoints, humanoidRootPart)
	-- PHASE 2: Explosive punch (0.3s) - After wind-up
	task.delay(0.2, function()
		createExplosivePunch(rightShoulder, bodyJoints, humanoidRootPart)
	end)
	print("✅ Epic punch animation sequence initiated")
end
function createWindUpAnimation(rightShoulder, bodyJoints, humanoidRootPart)
	local originalC0 = rightShoulder.C0
	-- Wind-up: Pull right arm back dramatically
	local _exp = rightShoulder
	local _exp_1 = TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In)
	local _object = {}
	local _left = "C0"
	local _arg0 = CFrame.Angles(math.rad(-30), math.rad(-20), math.rad(-10))
	_object[_left] = originalC0 * _arg0
	local windUpTween = TweenService:Create(_exp, _exp_1, _object)
	windUpTween:Play()
	-- Left arm forward for balance during wind-up
	local leftShoulder = bodyJoints.LeftShoulder
	if leftShoulder then
		local leftOriginal = leftShoulder.C0
		local _exp_2 = TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.rad(20), math.rad(15), math.rad(10))
		_object_1[_left_1] = leftOriginal * _arg0_1
		local leftWindUp = TweenService:Create(leftShoulder, _exp_2, _object_1)
		leftWindUp:Play()
	end
	-- Torso twist back for wind-up
	local waist = bodyJoints.Waist or bodyJoints.RootJoint
	if waist then
		local waistOriginal = waist.C0
		local _exp_2 = TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.rad(10), math.rad(-25), 0)
		_object_1[_left_1] = waistOriginal * _arg0_1
		local waistWindUp = TweenService:Create(waist, _exp_2, _object_1)
		waistWindUp:Play()
	end
	-- Character step back slightly
	if humanoidRootPart then
		local originalCFrame = humanoidRootPart.CFrame
		local backStep = originalCFrame.LookVector * (-1)
		local stepBackTween = TweenService:Create(humanoidRootPart, TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In), {
			CFrame = originalCFrame + backStep,
		})
		stepBackTween:Play()
	end
	print("✅ Wind-up animation started")
end
function createExplosivePunch(rightShoulder, bodyJoints, humanoidRootPart)
	local originalC0 = rightShoulder.C0
	-- EXPLOSIVE punch forward with overshoot
	local _exp = rightShoulder
	local _exp_1 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
	local _object = {}
	local _left = "C0"
	local _arg0 = CFrame.Angles(math.rad(130), math.rad(15), math.rad(5))
	_object[_left] = originalC0 * _arg0
	local punchTween = TweenService:Create(_exp, _exp_1, _object)
	punchTween:Play()
	-- Left arm swing back dramatically
	local leftShoulder = bodyJoints.LeftShoulder
	if leftShoulder then
		local leftOriginal = leftShoulder.C0
		local _exp_2 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.rad(-80), math.rad(-50), math.rad(-30))
		_object_1[_left_1] = leftOriginal * _arg0_1
		local leftSwing = TweenService:Create(leftShoulder, _exp_2, _object_1)
		leftSwing:Play()
	end
	-- Explosive torso rotation and lean
	local waist = bodyJoints.Waist or bodyJoints.RootJoint
	if waist then
		local waistOriginal = waist.C0
		local _exp_2 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.rad(-25), math.rad(50), 0)
		_object_1[_left_1] = waistOriginal * _arg0_1
		local explosiveTorso = TweenService:Create(waist, _exp_2, _object_1)
		explosiveTorso:Play()
	end
	-- Head snap forward with punch
	local neck = bodyJoints.Neck
	if neck then
		local neckOriginal = neck.C0
		local _exp_2 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.rad(-30), math.rad(25), 0)
		_object_1[_left_1] = neckOriginal * _arg0_1
		local headSnap = TweenService:Create(neck, _exp_2, _object_1)
		headSnap:Play()
	end
	-- Explosive forward lunge
	if humanoidRootPart then
		local originalCFrame = humanoidRootPart.CFrame
		local explosiveLunge = originalCFrame.LookVector * 5
		local lungeTween = TweenService:Create(humanoidRootPart, TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
			CFrame = originalCFrame + explosiveLunge,
		})
		lungeTween:Play()
		-- Don't return to original position - let player move freely after punch
	end
	print("✅ EXPLOSIVE punch animation executed!")
end
local createCrossArmsWindUp, createExplosiveDoublePunch
local function animateCrossPunch(rightShoulder, bodyJoints)
	-- Get character for movement
	local _result = rightShoulder.Parent
	if _result ~= nil then
		_result = _result.Parent
	end
	local character = _result
	local _result_1 = character
	if _result_1 ~= nil then
		_result_1 = _result_1:FindFirstChild("HumanoidRootPart")
	end
	local humanoidRootPart = _result_1
	local leftShoulder = bodyJoints.LeftShoulder
	if not leftShoulder then
		print("❌ Left shoulder not found for cross punch, falling back to single punch")
		animateFullBodyPunch(rightShoulder, bodyJoints)
		return nil
	end
	-- PHASE 1: Cross arms (0.3s) - Whitebeard style
	createCrossArmsWindUp(rightShoulder, leftShoulder, bodyJoints, humanoidRootPart)
	-- PHASE 2: Explosive double punch (0.4s) - After cross
	task.delay(0.3, function()
		createExplosiveDoublePunch(rightShoulder, leftShoulder, bodyJoints, humanoidRootPart)
	end)
	print("✅ One Piece cross punch animation sequence initiated")
end
function createCrossArmsWindUp(rightShoulder, leftShoulder, bodyJoints, humanoidRootPart)
	local rightOriginal = rightShoulder.C0
	local leftOriginal = leftShoulder.C0
	-- Cross arms horizontally in front - Whitebeard style
	local _exp = rightShoulder
	local _exp_1 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In)
	local _object = {}
	local _left = "C0"
	local _arg0 = CFrame.Angles(math.rad(0), math.rad(-45), math.rad(-45))
	_object[_left] = rightOriginal * _arg0
	local rightCrossTween = TweenService:Create(_exp, _exp_1, _object)
	local _exp_2 = leftShoulder
	local _exp_3 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In)
	local _object_1 = {}
	local _left_1 = "C0"
	local _arg0_1 = CFrame.Angles(math.rad(0), math.rad(45), math.rad(45))
	_object_1[_left_1] = leftOriginal * _arg0_1
	local leftCrossTween = TweenService:Create(_exp_2, _exp_3, _object_1)
	rightCrossTween:Play()
	leftCrossTween:Play()
	-- Torso preparation
	local waist = bodyJoints.Waist or bodyJoints.RootJoint
	if waist then
		local waistOriginal = waist.C0
		local _exp_4 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In)
		local _object_2 = {}
		local _left_2 = "C0"
		local _arg0_2 = CFrame.Angles(math.rad(5), 0, 0)
		_object_2[_left_2] = waistOriginal * _arg0_2
		local waistPrep = TweenService:Create(waist, _exp_4, _object_2)
		waistPrep:Play()
	end
	-- Head preparation
	local neck = bodyJoints.Neck
	if neck then
		local neckOriginal = neck.C0
		local _exp_4 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In)
		local _object_2 = {}
		local _left_2 = "C0"
		local _arg0_2 = CFrame.Angles(math.rad(-10), 0, 0)
		_object_2[_left_2] = neckOriginal * _arg0_2
		local headPrep = TweenService:Create(neck, _exp_4, _object_2)
		headPrep:Play()
	end
	print("✅ Cross arms wind-up started")
end
function createExplosiveDoublePunch(rightShoulder, leftShoulder, bodyJoints, humanoidRootPart)
	local rightOriginal = rightShoulder.C0
	local leftOriginal = leftShoulder.C0
	-- LEFT punch first (0.3s) - Horizontal punch to the left
	local _exp = leftShoulder
	local _exp_1 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
	local _object = {}
	local _left = "C0"
	local _arg0 = CFrame.Angles(math.rad(0), math.rad(-90), math.rad(-90))
	_object[_left] = leftOriginal * _arg0
	local leftPunchTween = TweenService:Create(_exp, _exp_1, _object)
	leftPunchTween:Play()
	-- RIGHT punch with delay (0.5s delay, then 0.3s animation)
	task.delay(0.5, function()
		local _exp_2 = rightShoulder
		local _exp_3 = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.rad(0), math.rad(90), math.rad(90))
		_object_1[_left_1] = rightOriginal * _arg0_1
		local rightPunchTween = TweenService:Create(_exp_2, _exp_3, _object_1)
		rightPunchTween:Play()
	end)
	-- Play double punch sounds with proper timing
	local _result = rightShoulder.Parent
	if _result ~= nil then
		_result = _result.Parent
	end
	local character = _result
	if character then
		-- Left punch sound (immediate)
		local leftPunchSound = Instance.new("Sound")
		leftPunchSound.SoundId = "rbxassetid://84539241826189"
		leftPunchSound.Volume = 0.8
		leftPunchSound.PlaybackSpeed = 0.9
		leftPunchSound.Parent = character:FindFirstChild("HumanoidRootPart") or Workspace
		leftPunchSound:Play()
		-- Right punch sound with 0.5s delay to match animation
		task.delay(0.5, function()
			local rightPunchSound = Instance.new("Sound")
			rightPunchSound.SoundId = "rbxassetid://84539241826189"
			rightPunchSound.Volume = 0.8
			rightPunchSound.PlaybackSpeed = 1.1
			rightPunchSound.Parent = character:FindFirstChild("HumanoidRootPart") or Workspace
			rightPunchSound:Play()
			-- Clean up sounds
			leftPunchSound.Ended:Connect(function()
				return leftPunchSound:Destroy()
			end)
			rightPunchSound.Ended:Connect(function()
				return rightPunchSound:Destroy()
			end)
		end)
	end
	-- Explosive torso movement
	local waist = bodyJoints.Waist or bodyJoints.RootJoint
	if waist then
		local waistOriginal = waist.C0
		local _exp_2 = TweenInfo.new(0.4, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.rad(-20), 0, 0)
		_object_1[_left_1] = waistOriginal * _arg0_1
		local explosiveTorso = TweenService:Create(waist, _exp_2, _object_1)
		explosiveTorso:Play()
	end
	-- Head follow the punches
	local neck = bodyJoints.Neck
	if neck then
		local neckOriginal = neck.C0
		local _exp_2 = TweenInfo.new(0.4, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.rad(-25), 0, 0)
		_object_1[_left_1] = neckOriginal * _arg0_1
		local headFollow = TweenService:Create(neck, _exp_2, _object_1)
		headFollow:Play()
	end
	-- Forward lunge
	if humanoidRootPart then
		local originalCFrame = humanoidRootPart.CFrame
		local doubleLunge = originalCFrame.LookVector * 4
		local lungeTween = TweenService:Create(humanoidRootPart, TweenInfo.new(0.4, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
			CFrame = originalCFrame + doubleLunge,
		})
		lungeTween:Play()
		-- Don't return to original position - let player move freely after cross punch
	end
	print("✅ EXPLOSIVE double cross punch executed!")
end
local function restoreBodyJoints(rightShoulder, bodyJoints)
	-- Use Core framework restoreBodyJoints - exact same behavior
	coreRestoreJoints(bodyJoints)
	print("✅ All body joints restored")
end
return {
	findBodyJoints = findBodyJoints,
	animateFullBodyPunch = animateFullBodyPunch,
	createWindUpAnimation = createWindUpAnimation,
	createExplosivePunch = createExplosivePunch,
	animateCrossPunch = animateCrossPunch,
	createCrossArmsWindUp = createCrossArmsWindUp,
	createExplosiveDoublePunch = createExplosiveDoublePunch,
	restoreBodyJoints = restoreBodyJoints,
}
