{"name": "roblox-ts-game", "globIgnorePaths": ["**/package.json", "**/tsconfig.json"], "tree": {"$className": "DataModel", "ServerScriptService": {"$className": "ServerScriptService", "TS": {"$path": "out/server"}}, "ReplicatedStorage": {"$className": "ReplicatedStorage", "rbxts_include": {"$path": "include", "node_modules": {"$className": "Folder", "@rbxts": {"$path": "node_modules/@rbxts"}}}, "TS": {"$path": "out/shared"}, "core": {"$path": "out/core"}}, "StarterPlayer": {"$className": "StarterPlayer", "StarterPlayerScripts": {"$className": "StarterPlayerScripts", "TS": {"$path": "out/client"}}}, "Workspace": {"$className": "Workspace", "$properties": {"FilteringEnabled": true}}, "HttpService": {"$className": "HttpService", "$properties": {"HttpEnabled": true}}, "SoundService": {"$className": "SoundService", "$properties": {"RespectFilteringEnabled": true}}}}