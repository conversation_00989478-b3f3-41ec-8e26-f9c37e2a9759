-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local RunService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").RunService
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local BORDER_RADIUS = _design.BORDER_RADIUS
local Label = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "label", "Label").Label
local function AbilitySlot(props)
	local currentTime, setCurrentTime = React.useState(tick())
	local hovered, setHovered = React.useState(false)
	-- Update current time when on cooldown for smooth countdown
	React.useEffect(function()
		local _condition = not props.abilityData.isOnCooldown
		if not _condition then
			local _value = props.abilityData.cooldownEndTime
			_condition = not (_value ~= 0 and _value == _value and _value)
		end
		if _condition then
			return nil
		end
		local connection
		connection = RunService.Heartbeat:Connect(function()
			local now = tick()
			local _condition_1 = props.abilityData.cooldownEndTime
			if _condition_1 == nil then
				_condition_1 = 0
			end
			if now >= _condition_1 then
				-- Cooldown finished, stop updating
				connection:Disconnect()
				return nil
			end
			setCurrentTime(now)
		end)
		return function()
			connection:Disconnect()
		end
	end, { props.abilityData.isOnCooldown, props.abilityData.cooldownEndTime })
	local size = props.size or UDim2.new(0, 60, 0, 60)
	local _condition = props.abilityData.isOnCooldown
	if _condition then
		_condition = props.abilityData.cooldownEndTime
		if _condition ~= 0 and _condition == _condition and _condition then
			_condition = currentTime < props.abilityData.cooldownEndTime
		end
	end
	local isOnCooldown = _condition
	local _condition_1 = isOnCooldown
	if _condition_1 ~= 0 and _condition_1 == _condition_1 and _condition_1 then
		_condition_1 = props.abilityData.cooldownEndTime
	end
	local remainingTime = if _condition_1 ~= 0 and _condition_1 == _condition_1 and _condition_1 then math.max(0, props.abilityData.cooldownEndTime - currentTime) else 0
	-- Calculate cooldown progress (0 to 1, where 1 is fully on cooldown)
	local _condition_2 = isOnCooldown
	if _condition_2 ~= 0 and _condition_2 == _condition_2 and _condition_2 then
		_condition_2 = props.abilityData.cooldownEndTime
		if _condition_2 ~= 0 and _condition_2 == _condition_2 and _condition_2 then
			_condition_2 = remainingTime > 0
		end
	end
	local cooldownProgress = if _condition_2 ~= 0 and _condition_2 == _condition_2 and _condition_2 then (function()
		local _condition_3 = props.abilityData.totalCooldownDuration
		if _condition_3 == nil then
			_condition_3 = 30
		end
		local totalDuration = _condition_3
		return math.max(0, math.min(1, remainingTime / totalDuration))
	end)() else 0
	-- Get background color based on state
	local backgroundColor = if isOnCooldown ~= 0 and isOnCooldown == isOnCooldown and isOnCooldown then COLORS.bg.secondary elseif hovered then COLORS.bg["surface-hover"] else COLORS.bg.surface
	local _exp = React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	})
	local _exp_1 = React.createElement("uistroke", {
		Color = Color3.fromHex(if isOnCooldown ~= 0 and isOnCooldown == isOnCooldown and isOnCooldown then COLORS.border.l1 else COLORS.border.l2),
		Thickness = 2,
		Transparency = if isOnCooldown ~= 0 and isOnCooldown == isOnCooldown and isOnCooldown then 0.3 else 0,
	})
	local _exp_2 = React.createElement(Label, {
		text = tostring(props.slotNumber),
		fontSize = 10,
		textColor = COLORS.text.secondary,
		position = UDim2.new(0, 4, 0, 2),
		size = UDim2.new(0, 15, 0, 12),
		alignment = Enum.TextXAlignment.Left,
	})
	local _attributes = {}
	local _condition_3 = props.abilityData.icon
	if _condition_3 == nil then
		_condition_3 = string.sub(props.abilityData.name, 1, 1)
	end
	_attributes.text = _condition_3
	_attributes.fontSize = if isOnCooldown ~= 0 and isOnCooldown == isOnCooldown and isOnCooldown then 12 else 16
	_attributes.textColor = if isOnCooldown ~= 0 and isOnCooldown == isOnCooldown and isOnCooldown then COLORS.text.secondary else COLORS.text.main
	_attributes.position = UDim2.new(0.5, 0, 0.5, 0)
	_attributes.anchorPoint = Vector2.new(0.5, 0.5)
	_attributes.size = UDim2.new(0.8, 0, 0.6, 0)
	_attributes.alignment = Enum.TextXAlignment.Center
	local _exp_3 = React.createElement(Label, _attributes)
	local _condition_4 = isOnCooldown
	if _condition_4 ~= 0 and _condition_4 == _condition_4 and _condition_4 then
		_condition_4 = remainingTime > 0 and (React.createElement(Label, {
			text = tostring(math.ceil(remainingTime)),
			fontSize = 14,
			textColor = COLORS.warning,
			position = UDim2.new(0.5, 0, 0.8, 0),
			anchorPoint = Vector2.new(0.5, 0.5),
			size = UDim2.new(1, 0, 0, 16),
			alignment = Enum.TextXAlignment.Center,
			bold = true,
		}))
	end
	local _condition_5 = isOnCooldown
	if _condition_5 ~= 0 and _condition_5 == _condition_5 and _condition_5 then
		_condition_5 = (React.createElement("frame", {
			BackgroundColor3 = Color3.fromHex(COLORS.bg.base),
			BackgroundTransparency = 0.7,
			Size = UDim2.new(1, 0, cooldownProgress, 0),
			Position = UDim2.new(0, 0, 0, 0),
			BorderSizePixel = 0,
			ZIndex = 2,
		}, React.createElement("uicorner", {
			CornerRadius = UDim.new(0, BORDER_RADIUS.md),
		})))
	end
	return React.createElement("textbutton", {
		Text = "",
		BackgroundColor3 = Color3.fromHex(backgroundColor),
		Size = size,
		LayoutOrder = props.layoutOrder,
		AutoButtonColor = false,
		BorderSizePixel = 0,
		Event = {
			Activated = function()
				if not (isOnCooldown ~= 0 and isOnCooldown == isOnCooldown and isOnCooldown) then
					props.onClick()
				end
			end,
			MouseEnter = function()
				return not (isOnCooldown ~= 0 and isOnCooldown == isOnCooldown and isOnCooldown) and setHovered(true)
			end,
			MouseLeave = function()
				return setHovered(false)
			end,
		},
	}, _exp, _exp_1, _exp_2, _exp_3, _condition_4, _condition_5)
end
return {
	AbilitySlot = AbilitySlot,
}
