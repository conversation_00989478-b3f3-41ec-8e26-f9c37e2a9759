{"name": "roblo<PERSON>", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "rbxtsc", "watch": "rbxtsc -w"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@rbxts/compiler-types": "^3.0.0-types.0", "@rbxts/types": "^1.0.861", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-roblox-ts": "^0.0.36", "prettier": "^3.6.2", "roblox-ts": "^3.0.0", "typescript": "^5.8.3"}, "dependencies": {"@rbxts/react": "^17.2.3", "@rbxts/react-roblox": "^17.2.3", "@rbxts/roact": "^3.0.1", "@rbxts/roact-hooked": "^2.6.2", "@rbxts/services": "^1.5.5", "@rbxts/string-utils": "^1.0.3", "@roboxgames/core": "file:../Core"}}