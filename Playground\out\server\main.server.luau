-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local initializeCoreServer = TS.import(script, game:GetService("ReplicatedStorage"), "core", "server").initializeCoreServer
local WhitebeardAbilityServer = TS.import(script, game:GetService("ServerScriptService"), "TS", "abilities", "WhitebeardAbilityServer").WhitebeardAbilityServer
local WorldTestingServer = TS.import(script, game:GetService("ServerScriptService"), "TS", "world", "WorldTestingServer").WorldTestingServer
local ServerDataStoreService = TS.import(script, game:GetService("ServerScriptService"), "TS", "data", "DataStoreService").ServerDataStoreService
-- Initialize Core server helpers
-- This sets up universal networking, validation, and replication helpers
initializeCoreServer()
-- Initialize game-specific ability servers using Core helpers
local whitebeardServer = WhitebeardAbilityServer.new()
-- Initialize gravity testing server
local worldTestingServer = WorldTestingServer.new()
-- Initialize DataStore service
local dataStoreService = ServerDataStoreService:getInstance()
print("🔥 Playground server loaded with Core helpers, Gravity testing, and DataStore service!")
