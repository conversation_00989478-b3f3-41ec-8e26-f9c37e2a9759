# Create a single readme file with all project files for Grok
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
$projectRoot = Join-Path $scriptDir "Playground"
$outputFile = Join-Path $scriptDir "grok.readme"

# Initialize the output file
Set-Content -Path $outputFile -Value "# Associated Project files.`n`n"

# Function to add a file to the readme
function Add-FileToReadme {
    param (
        [string]$filePath,
        [string]$relativePath
    )
    
    if (Test-Path $filePath -PathType Leaf) {
        $content = Get-Content -Path $filePath -Raw
        $fileExtension = [System.IO.Path]::GetExtension($filePath).TrimStart('.')
        if ($fileExtension -eq '') { $fileExtension = 'txt' }
        

        Add-Content -Path $outputFile -Value "===================="
        Add-Content -Path $outputFile -Value "## $relativePath`n"
        Add-Content -Path $outputFile -Value $content
        Add-Content -Path $outputFile -Value "\n"
    }


}

# Add configuration files
Write-Host "Adding configuration files..."
Add-FileToReadme -filePath (Join-Path $projectRoot "tsconfig.json") -relativePath "tsconfig.json"
Add-FileToReadme -filePath (Join-Path $projectRoot "default.project.json") -relativePath "default.project.json"

# Recursively add all files from src directory
Write-Host "Adding source files..."
$srcPath = Join-Path $projectRoot "src"
$files = Get-ChildItem -Path $srcPath -Recurse -File

foreach ($file in $files) {
    $relativePath = $file.FullName.Substring($projectRoot.Length + 1).Replace("\", "/")
    Add-FileToReadme -filePath $file.FullName -relativePath $relativePath
}

Write-Host "Grok readme created successfully at $outputFile"








