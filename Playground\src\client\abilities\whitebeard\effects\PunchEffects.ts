import { createParticleExplosion, EffectPart<PERSON><PERSON>er, EffectTweenBuilder } from "../../../../core";

export function createPunchParticleExplosion(position: Vector3, direction: Vector3): void {
    // Use Core framework createParticleExplosion - exact same behavior
    createParticleExplosion(
        position,
        40, // numParticles
        Color3.fromRGB(255, 200, 100), // Orange energy color
        [25, 50], // velocityRange
        [0.2, 0.6] // sizeRange
    );
}

export function createEnergyTrails(startPosition: Vector3, endPosition: Vector3): void {
    // Create energy trails that follow the punch path using Core framework
    const numTrails = 8;

    for (let i = 0; i < numTrails; i++) {
        // Position along punch path with some randomness
        const t = i / numTrails;
        const basePosition = startPosition.Lerp(endPosition, t);
        const offset = new Vector3(
            math.random(-2, 2),
            math.random(-1, 1),
            math.random(-2, 2)
        );
        const trailPosition = basePosition.add(offset);

        // Orient towards punch direction
        const direction = endPosition.sub(startPosition).Unit;
        const trailCFrame = new CFrame(trailPosition, trailPosition.add(direction));

        // Create trail using EffectPartBuilder
        const trail = EffectPartBuilder.create()
            .shape(Enum.PartType.Block)
            .size(new Vector3(0.3, 0.3, 2))
            .color(Color3.fromRGB(100, 200, 255))
            .material(Enum.Material.Neon)
            .transparency(0.3)
            .cframe(trailCFrame)
            .spawn();

        trail.Name = `EnergyTrail_${i}`;

        // Delayed appearance and fade using EffectTweenBuilder
        task.delay(i * 0.05, () => {
            EffectTweenBuilder.for(trail)
                .fade(0.1)
                .duration(0.1)
                .play();

            task.delay(1, () => {
                EffectTweenBuilder.for(trail)
                    .fade(1)
                    .duration(1.5)
                    .onComplete(() => trail.Destroy())
                    .play();
            });
        });
    }
}