import { TweenService, RunService, Workspace, Players } from "@rbxts/services";

import type { QuakeAbility } from "../QuakeAbility";

import { createPunchParticleExplosion, createEnergyTrails } from "../effects/PunchEffects";
import { createAirCracks, createEnhancedAirCracks } from "../effects/CrackEffects";
import { createCameraShake } from "../effects/CameraShake";
import { createMapShockwave } from "../effects/ShockwaveEffects";

import { findBodyJoints, animateFullBodyPunch, animateCrossPunch, restoreBodyJoints } from "./AnimationEffects";

export function executeQuakePunch(ability: QuakeAbility, character: Model, rightHand: Part, selectedPunchType: string): void {
    print(`🔥 Starting executeQuakePunch with ${selectedPunchType} punch`);

    // Keep sphere(s) for a few more seconds during punch, clean up later
    // Don't destroy sphere(s) immediately

    // Find the right shoulder Motor6D - comprehensive search
    let rightShoulder: Motor6D | undefined;

    // First check if it's an R6 character (has Torso)
    const torso = character.FindFirstChild("Torso") as Part;
    if (torso) {
        // R6 character - Motor6D is in Torso
        rightShoulder = torso.FindFirstChild("Right Shoulder") as Motor6D;
        print("🔍 Checking R6 character for Right Shoulder in Torso");
    } else {
        // R15 character - search everywhere for right arm connection
        print("🔍 R15 character detected, searching for right arm Motor6D...");

        const rightUpperArm = character.FindFirstChild("RightUpperArm") as Part;
        const rightLowerArm = character.FindFirstChild("RightLowerArm") as Part;
        const rightHand = character.FindFirstChild("RightHand") as Part;

        // Search through all parts in the character for Motor6D joints
        const partsToCheck = [
            character.FindFirstChild("UpperTorso"),
            rightUpperArm,
            rightLowerArm,
            rightHand
        ];

        for (const part of partsToCheck) {
            if (part && part.IsA("BasePart")) {
                for (const [name, child] of pairs(part.GetChildren())) {
                    if (child.IsA("Motor6D")) {
                        const motor = child as Motor6D;
                        // Check if this Motor6D connects UpperTorso to RightUpperArm
                        if ((motor.Part0?.Name === "UpperTorso" && motor.Part1?.Name === "RightUpperArm") ||
                            (motor.Part1?.Name === "UpperTorso" && motor.Part0?.Name === "RightUpperArm")) {
                            rightShoulder = motor;
                            print(`✅ Found right shoulder Motor6D: ${name} in ${part.Name} (connects ${motor.Part0?.Name} to ${motor.Part1?.Name})`);
                            break;
                        }
                    }
                }
                if (rightShoulder) break;
            }
        }
    }

    // Debug: Comprehensive search for all Motor6D joints
    if (!rightShoulder) {
        print("🔍 Debugging - Comprehensive Motor6D search:");

        // Check all parts in character for Motor6D joints
        const allParts = character.GetDescendants().filter(obj => obj.IsA("BasePart")) as Part[];

        for (const part of allParts) {
            const motors = part.GetChildren().filter(child => child.IsA("Motor6D")) as Motor6D[];
            if (motors.size() > 0) {
                print(`🔍 Motor6D joints in ${part.Name}:`);
                for (const motor of motors) {
                    print(`  - ${motor.Name}: (Part0: ${motor.Part0?.Name}, Part1: ${motor.Part1?.Name})`);
                }
            }
        }

        // Also list all right arm parts
        print("🔍 Right arm parts found:");
        const rightParts = ["RightUpperArm", "RightLowerArm", "RightHand"];
        for (const partName of rightParts) {
            const part = character.FindFirstChild(partName);
            print(`  - ${partName}: ${part ? "✅ Found" : "❌ Missing"}`);
        }
    }

    if (!rightShoulder) {
        print("❌ Right Shoulder not found! Proceeding without arm animation...");
        // Still create the cracks even if we can't animate the arm - use current position
        task.delay(0.2, () => {
            print("🔥 Creating air cracks (no arm animation)");
            const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
            if (humanoidRootPart) {
                const lookDirection = humanoidRootPart.CFrame.LookVector;
                const horizontalLookDirection = new Vector3(lookDirection.X, 0, lookDirection.Z).Unit;
                const crackPosition = humanoidRootPart.Position.add(horizontalLookDirection.mul(8));
                print(`✅ Crack position (horizontal): ${crackPosition}`);
                createAirCracks(crackPosition);
            } else {
                print("❌ HumanoidRootPart not found for cracks!");
            }
        });
        return;
    }

    print("✅ Right Shoulder found, starting full body punch animation");

    // Store original position using attributes
    const originalC0 = rightShoulder.C0;
    rightShoulder.SetAttribute("OriginalC0", originalC0);

    // Find additional joints for full body animation
    const bodyJoints = findBodyJoints(character);

    // Store original positions for all joints
    for (const [jointName, joint] of pairs(bodyJoints)) {
        if (joint) {
            joint.SetAttribute(`Original_${tostring(jointName)}`, joint.C0);
            print(`✅ Stored original position for ${tostring(jointName)}`);
        } else {
            print(`❌ Joint ${tostring(jointName)} not found`);
        }
    }

    // Use the punch type passed from executeQuakePunch
    if (selectedPunchType === "single") {
        // Original single forward punch
        animateFullBodyPunch(rightShoulder, bodyJoints);
    } else {
        // One Piece style cross punch
        animateCrossPunch(rightShoulder, bodyJoints);
    }

    // Play punch sound effect
    const punchSound = new Instance("Sound");
    punchSound.SoundId = "rbxassetid://84539241826189";
    punchSound.Volume = 0.8;
    punchSound.PlaybackSpeed = 1;
    punchSound.Parent = character.FindFirstChild("HumanoidRootPart") || Workspace;
    punchSound.Play();

    // Clean up sound after it finishes
    punchSound.Ended.Connect(() => {
        punchSound.Destroy();
    });

    // Create enhanced air cracks during punch (slight delay for impact)
    task.delay(0.3, () => {
        print("🔥 Creating enhanced air cracks");

        // Use current player position at punch time for accurate crack placement
        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (humanoidRootPart) {
            const currentPosition = humanoidRootPart.Position;
            const currentLookDirection = humanoidRootPart.CFrame.LookVector;
            const currentRightVector = humanoidRootPart.CFrame.RightVector;

            // Add dramatic punch particles
            createPunchParticleExplosion(currentPosition, currentLookDirection);
            
            // Create energy trails from fist to impact point
            const rightHand = character.FindFirstChild("RightHand") as Part;
            if (rightHand) {
                const impactPoint = currentPosition.add(currentLookDirection.mul(10));
                createEnergyTrails(rightHand.Position, impactPoint);
            }

            if (selectedPunchType === "single") {
                // Single punch: crack appears directly in front of player where the punch hits
                const horizontalLookDirection = new Vector3(currentLookDirection.X, 0, currentLookDirection.Z).Unit;
                const crackPosition = currentPosition.add(horizontalLookDirection.mul(6)); // In front of player
                print(`✅ Single punch crack position (front): ${crackPosition}`);
                createEnhancedAirCracks(crackPosition, horizontalLookDirection);
                // Single punch camera shake
                createCameraShake(8, 0.6);
                // Single punch map shockwave
                createMapShockwave(currentPosition, 250, 2.5);
            } else {
                // Cross punch: cracks appear to the LEFT and RIGHT sides where each punch hits

                // Left punch hits to the LEFT side of the player
                const leftDirection = currentRightVector.mul(-1); // Left direction
                const leftCrackPos = currentPosition.add(leftDirection.mul(8)); // 8 studs to the left

                // Right punch hits to the RIGHT side of the player
                const rightDirection = currentRightVector; // Right direction
                const rightCrackPos = currentPosition.add(rightDirection.mul(8)); // 8 studs to the right

                print(`✅ Cross punch crack positions: LEFT=${leftCrackPos}, RIGHT=${rightCrackPos}`);

                // Create LEFT crack immediately (for left punch)
                createEnhancedAirCracks(leftCrackPos, leftDirection);
                // Left punch camera shake
                createCameraShake(6, 0.4);
                // Left punch shockwave (moderate)
                createMapShockwave(currentPosition, 180, 1.8);

                // Create RIGHT crack with 0.5s delay (to match right punch timing)
                task.delay(0.5, () => {
                    // Get current position again for the delayed right crack
                    const humanoidRootPartDelayed = character.FindFirstChild("HumanoidRootPart") as Part;
                    if (humanoidRootPartDelayed) {
                        const delayedPosition = humanoidRootPartDelayed.Position;
                        const delayedRightVector = humanoidRootPartDelayed.CFrame.RightVector;
                        const delayedRightDirection = delayedRightVector; // Right direction
                        const delayedRightCrackPos = delayedPosition.add(delayedRightDirection.mul(8)); // 8 studs to the right

                        createEnhancedAirCracks(delayedRightCrackPos, delayedRightDirection);
                        // Right punch camera shake (stronger for final impact)
                        createCameraShake(10, 0.7);
                        // Right punch shockwave (massive final impact)
                        createMapShockwave(delayedPosition, 300, 3);
                        print(`✅ Right crack created with delay at position: ${delayedRightCrackPos}`);
                    }
                });
            }
        } else {
            print("❌ HumanoidRootPart not found for cracks!");
        }
    });

    // Hold punch position for 3 seconds, then restore all joints
    task.delay(3.5, () => {
        restoreBodyJoints(rightShoulder, bodyJoints);
    });

    // Clean up sphere effect(s) after punch is done (5 seconds total)
    task.delay(5, () => {
        if (ability.quakeEffect) {
            // Fade out right sphere
            const fadeOut = TweenService.Create(
                ability.quakeEffect,
                new TweenInfo(1, Enum.EasingStyle.Quad),
                { Transparency: 1 }
            );
            fadeOut.Play();
            fadeOut.Completed.Connect(() => {
                if (ability.quakeEffect) {
                    ability.quakeEffect.Destroy();
                    ability.quakeEffect = undefined;
                }
            });
        }
        if (ability.leftQuakeEffect) {
            // Fade out left sphere
            const leftFadeOut = TweenService.Create(
                ability.leftQuakeEffect,
                new TweenInfo(1, Enum.EasingStyle.Quad),
                { Transparency: 1 }
            );
            leftFadeOut.Play();
            leftFadeOut.Completed.Connect(() => {
                if (ability.leftQuakeEffect) {
                    ability.leftQuakeEffect.Destroy();
                    ability.leftQuakeEffect = undefined;
                }
            });
        }
        if (ability.quakeConnection) {
            ability.quakeConnection.Disconnect();
            ability.quakeConnection = undefined;
        }
    });
}