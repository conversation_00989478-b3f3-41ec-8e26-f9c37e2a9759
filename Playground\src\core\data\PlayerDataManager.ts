import { Players } from "@rbxts/services";
import { DataStoreHelper } from "./DataStoreHelper";
import { BasePlayerData, PlayerSettings } from "./interfaces/PlayerData";
import { DataStoreResult } from "./interfaces/DataStoreConfig";

export class PlayerDataManager<TPlayerData extends BasePlayerData = BasePlayerData> {
	private static instance: PlayerDataManager;
	private dataStore: DataStoreHelper<TPlayerData>;
	private loadedPlayers = new Set<number>();

	private constructor() {
		this.dataStore = DataStoreHelper.getInstance() as DataStoreHelper<TPlayerData>;
		this.setupPlayerEvents();
	}

	public static getInstance<T extends BasePlayerData = BasePlayerData>(): PlayerDataManager<T> {
		if (!PlayerDataManager.instance) {
			PlayerDataManager.instance = new PlayerDataManager();
		}
		return PlayerDataManager.instance as PlayerDataManager<T>;
	}

	// Player Lifecycle Management
	public async onPlayerJoin(player: Player): Promise<DataStoreResult<TPlayerData>> {
		print(`Loading data for player: ${player.Name} (${player.UserId})`);

		const result = await this.dataStore.loadPlayerData(player.UserId);

		if (result.success && result.data) {
			// Update last login time and session count
			const updatedData = {
				...result.data,
				lastLogin: tick(),
				metadata: {
					...result.data.metadata,
					totalSessions: result.data.metadata.totalSessions + 1
				}
			} as TPlayerData;

			await this.dataStore.savePlayerData(updatedData);
			this.loadedPlayers.add(player.UserId);

			print(`✅ Successfully loaded data for ${player.Name}`);
			return { success: true, data: updatedData };
		} else {
			print(`❌ Failed to load data for ${player.Name}: ${result.error}`);
			return result;
		}
	}

	public async onPlayerLeave(player: Player): Promise<void> {
		if (this.loadedPlayers.has(player.UserId)) {
			print(`Saving data for leaving player: ${player.Name}`);
			
			const result = await this.dataStore.loadPlayerData(player.UserId);
			if (result.success && result.data) {
				// Update playtime before saving
				const sessionTime = tick() - result.data.lastLogin;
				const updatedData = {
					...result.data,
					playtime: result.data.playtime + sessionTime
				};
				
				const saveResult = await this.dataStore.savePlayerData(updatedData);
				if (saveResult.success) {
					print(`✅ Successfully saved data for ${player.Name}`);
				} else {
					print(`❌ Failed to save data for ${player.Name}: ${saveResult.error}`);
				}
			}
			
			this.loadedPlayers.delete(player.UserId);
		}
	}

	// Generic game data management
	public async updatePlayerGameData(userId: number, gameDataUpdates: Record<string, unknown>): Promise<DataStoreResult<TPlayerData>> {
		return this.dataStore.updatePlayerGameData(userId, gameDataUpdates);
	}

	// Settings Management
	public async updatePlayerSettings(userId: number, settings: Partial<PlayerSettings>): Promise<DataStoreResult<PlayerSettings>> {
		const result = await this.dataStore.loadPlayerData(userId);
		if (!result.success || !result.data) {
			return { success: false, error: "Failed to load player data" };
		}

		const newSettings = { ...result.data.settings, ...settings };
		const updateResult = await this.dataStore.updatePlayerData(userId, {
			settings: newSettings
		} as Partial<TPlayerData>);

		if (updateResult.success) {
			print(`⚙️ Updated settings for player ${userId}`);
			return { success: true, data: newSettings };
		} else {
			return { success: false, error: updateResult.error };
		}
	}

	// Utility Methods
	public isPlayerLoaded(userId: number): boolean {
		return this.loadedPlayers.has(userId);
	}

	public getLoadedPlayerCount(): number {
		return this.loadedPlayers.size();
	}



	private setupPlayerEvents(): void {
		Players.PlayerAdded.Connect((player) => {
			this.onPlayerJoin(player);
		});

		Players.PlayerRemoving.Connect((player) => {
			this.onPlayerLeave(player);
		});
	}

	public cleanup(): void {
		this.loadedPlayers.clear();
		this.dataStore.cleanup();
	}
}
