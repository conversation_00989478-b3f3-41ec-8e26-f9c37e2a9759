import * as React from "@rbxts/react";
import { COLORS, BORDER_RADIUS } from "../../design";
import { ScrollingFrameProps } from "./types";

export function ScrollingFrame(props: ScrollingFrameProps): React.ReactElement {
  const size = props.size ?? new UDim2(1, 0, 1, 0);
  const canvasSize = props.canvasSize ?? new UDim2(0, 0, 0, 0); // Default to no scrolling
  const scrollBarThickness = props.scrollBarThickness ?? 6;
  const backgroundColor = props.backgroundColor ?? COLORS.bg.base;
  const backgroundTransparency = props.backgroundTransparency ?? 0;
  const borderColor = props.borderColor ?? COLORS.border.l2;
  const borderThickness = props.borderThickness ?? 1;
  const cornerRadius = props.cornerRadius ?? BORDER_RADIUS.md;
  const scrollingDirection = props.scrollingDirection ?? Enum.ScrollingDirection.XY;
  const elasticBehavior = props.elasticBehavior ?? Enum.ElasticBehavior.WhenScrollable;
  const automaticCanvasSize = props.automaticCanvasSize ?? Enum.AutomaticSize.Y;

  return (
    <scrollingframe
      Size={size}
      Position={props.position}
      AnchorPoint={props.anchorPoint}
      LayoutOrder={props.layoutOrder}
      CanvasSize={canvasSize}
      ScrollBarThickness={scrollBarThickness}
      BackgroundColor3={Color3.fromHex(backgroundColor)}
      BackgroundTransparency={backgroundTransparency}
      ScrollBarImageColor3={Color3.fromHex(COLORS.border.l3)}
      BorderSizePixel={0}
      ScrollingDirection={scrollingDirection}
      ElasticBehavior={elasticBehavior}
      AutomaticCanvasSize={automaticCanvasSize}
      ScrollingEnabled={true}
    >
      <uicorner CornerRadius={new UDim(0, cornerRadius)} />
      
      {borderThickness > 0 && (
        <uistroke
          Color={Color3.fromHex(borderColor)}
          Thickness={borderThickness}
        />
      )}
      
      {props.children}
    </scrollingframe>
  );
}
