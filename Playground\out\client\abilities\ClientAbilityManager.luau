-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local RoomAbility = TS.import(script, script.Parent, "RoomAbility").RoomAbility
local QuakeAbility = TS.import(script, script.Parent, "whitebeard", "QuakeAbility").QuakeAbility
local HakiDominanceAbility = TS.import(script, script.Parent, "HakiDominanceAbility").HakiDominanceAbility
local IceAgeAbility = TS.import(script, script.Parent, "IceAgeAbility").IceAgeAbility
local FireFistAbility = TS.import(script, script.Parent, "FireFistAbility").FireFistAbility
local ThreeSwordStyleAbility = TS.import(script, script.Parent, "ThreeSwordStyleAbility").ThreeSwordStyleAbility
local ClientAbilityManager
do
	ClientAbilityManager = setmetatable({}, {
		__tostring = function()
			return "ClientAbilityManager"
		end,
	})
	ClientAbilityManager.__index = ClientAbilityManager
	function ClientAbilityManager.new(...)
		local self = setmetatable({}, ClientAbilityManager)
		return self:constructor(...) or self
	end
	function ClientAbilityManager:constructor()
		self.abilities = {}
		self:initializeAbilities()
	end
	function ClientAbilityManager:initializeAbilities()
		self:registerAbility(RoomAbility.new())
		self:registerAbility(QuakeAbility.new())
		self:registerAbility(HakiDominanceAbility.new())
		self:registerAbility(IceAgeAbility.new())
		self:registerAbility(FireFistAbility.new())
		self:registerAbility(ThreeSwordStyleAbility.new())
	end
	function ClientAbilityManager:registerAbility(ability)
		local _abilities = self.abilities
		local _arg0 = ability:getName()
		local _ability = ability
		_abilities[_arg0] = _ability
	end
	function ClientAbilityManager:activateAbility(name)
		local _abilities = self.abilities
		local _name = name
		local ability = _abilities[_name]
		if ability then
			ability:activate()
		end
	end
end
return {
	ClientAbilityManager = ClientAbilityManager,
}
