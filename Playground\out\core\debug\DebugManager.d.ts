export interface DebugConfig {
    showAI: boolean;
    showPlayers: boolean;
    showPerformance: boolean;
    showPositions: boolean;
    showPaths: boolean;
    showVision: boolean;
    showCollisions: boolean;
    showFPS: boolean;
    showMemory: boolean;
    enabled: boolean;
}
export declare class DebugManager {
    private static instance;
    private config;
    private renderer;
    private aiDebugger;
    private playerDebugger;
    private performanceMonitor;
    private heartbeatConnection?;
    private inputConnection?;
    private isInitialized;
    private constructor();
    static getInstance(): DebugManager;
    initialize(): void;
    toggle(): void;
    setConfig(newConfig: Partial<DebugConfig>): void;
    getConfig(): DebugConfig;
    isEnabled(): boolean;
    toggleAI(): void;
    togglePlayers(): void;
    togglePerformance(): void;
    togglePaths(): void;
    toggleVision(): void;
    private setupInputHandling;
    private startDebugLoop;
    cleanup(): void;
    drawLine(from: Vector3, to: Vector3, color?: Color3, duration?: number): void;
    drawSphere(position: Vector3, radius?: number, color?: Color3, duration?: number): void;
    drawText(position: Vector3, text: string, color?: Color3, duration?: number): void;
    logDebug(category: string, message: string, data?: unknown): void;
}
