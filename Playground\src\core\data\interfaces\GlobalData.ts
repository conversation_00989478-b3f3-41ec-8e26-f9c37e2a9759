// Core global data that every game needs
export interface CoreGlobalData {
	serverVersion: string;
	maintenanceMode: boolean;
	lastUpdated: number;
}

// Generic global data structure that games can extend
export interface BaseGlobalData extends CoreGlobalData {
	gameData: Record<string, unknown>; // Game-specific global data
	serverConfig: ServerConfig;
}

export interface ServerConfig {
	maxPlayers: number;
	autoSaveInterval: number;
	debugMode: boolean;
	[key: string]: unknown; // Allow games to add custom config
}

// Helper type for games to define their own global data
export type GameGlobalData<T = Record<string, unknown>> = BaseGlobalData & {
	gameData: T;
};
