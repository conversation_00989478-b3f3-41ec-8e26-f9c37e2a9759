import { make<PERSON><PERSON> } from "../shared/module";
import { initializeCoreServer } from "../core/server";
import { WhitebeardAbilityServer } from "./abilities/WhitebeardAbilityServer";
import { WorldTestingServer } from "./world/WorldTestingServer";
import { ServerDataStoreService } from "./data/DataStoreService";

// Initialize Core server helpers
// This sets up universal networking, validation, and replication helpers
initializeCoreServer();

// Initialize game-specific ability servers using Core helpers
const whitebeardServer = new WhitebeardAbilityServer();

// Initialize gravity testing server
const worldTestingServer = new WorldTestingServer();

// Initialize DataStore service
const dataStoreService = ServerDataStoreService.getInstance();

print("🔥 Playground server loaded with Core helpers, Gravity testing, and DataStore service!");

