export interface ScreenSize {
    width: number;
    height: number;
    aspectRatio: number;
}
export interface ResponsiveBreakpoints {
    mobile: number;
    tablet: number;
    desktop: number;
}
export type DeviceType = "mobile" | "tablet" | "desktop";
export declare class ResponsiveManager {
    private static instance;
    private currentScreenSize;
    private callbacks;
    private breakpoints;
    private constructor();
    static getInstance(): ResponsiveManager;
    private calculateScreenSize;
    private setupViewportListener;
    private notifyCallbacks;
    getScreenSize(): ScreenSize;
    getDeviceType(): DeviceType;
    isMobile(): boolean;
    isTablet(): boolean;
    isDesktop(): boolean;
    onScreenSizeChange(callback: (screenSize: ScreenSize) => void): () => void;
    getRelativePosition(desiredPosition: {
        x: number;
        y: number;
    }): UDim2;
    getResponsiveSize(desiredSize: {
        width: number;
        height: number;
    }, minSize?: {
        width: number;
        height: number;
    }, maxSize?: {
        width: number;
        height: number;
    }): UDim2;
    getResponsiveMargin(pixelMargin: number): number;
    getSafeAreaInsets(): {
        top: number;
        bottom: number;
        left: number;
        right: number;
    };
}
