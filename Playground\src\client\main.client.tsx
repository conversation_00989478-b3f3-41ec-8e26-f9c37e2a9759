import * as React from "@rbxts/react";
import { createRoot } from "@rbxts/react-roblox";
import { Players } from "@rbxts/services";
import { BottomLeftGrid } from "./gui/BottomLeftGrid";
import { ActionBarDemo } from "./gui/ActionBarDemo";
import { MovementExample } from "./movement/MovementExample";
import { initializeDebugSystem } from "../core/debug";

const version = "v1.3.5";
const player = Players.LocalPlayer;
const playerGui = player.WaitForChild("PlayerGui") as PlayerGui;

// Create a ScreenGui with properties to make it visible
const screenGui = new Instance("ScreenGui", playerGui);
screenGui.ResetOnSpawn = false;
screenGui.IgnoreGuiInset = true;
screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling;

const root = createRoot(screenGui);

// Render the GUI components
root.render(
  <>
    <BottomLeftGrid
      onTestClick={() => print("Test button clicked!")}
      onHelloClick={() => print("Hello button clicked!")}
    />
    <ActionBarDemo />
  </>
);

// Initialize movement example for testing
new MovementExample();

// Initialize debug system for development
initializeDebugSystem();

print(`🔥 Playground client loaded! [${version}]`);
print(`🎙️ Voice system available! Use voiceDemo methods in console`);