import { Players, ReplicatedStorage, RunService } from "@rbxts/services";
import { SyncOptions } from "./interfaces/SyncOptions";
import { SyncedAction } from "./interfaces/SyncedAction";

interface SyncState {
    sequenceNumber: number;
    timestamp: number;
    playerId: number;
    actionId: string;
    data: unknown;
}

interface PendingAction {
    player: Player;
    request: unknown;
    timestamp: number;
    sequenceNumber: number;
    resolve: (response: unknown) => void;
    reject: (reason: string) => void;
}

export class NetworkSync {
    private static remoteEvents: Map<string, RemoteEvent> = new Map();
    private static playerCooldowns: Map<Player, Map<string, number>> = new Map();
    private static playerSequenceNumbers: Map<Player, number> = new Map();
    private static pendingActions: Map<string, PendingAction[]> = new Map();
    private static serverStartTime = tick();
    private static actionBatches: Map<string, SyncState[]> = new Map();
    private static batchTimers: Map<string, thread> = new Map();

    /**
     * Creates a synced action that handles client-server communication with enhanced synchronization
     * Features: sequence validation, timestamp sync, batching, and conflict resolution
     */
    public static createSyncedAction<TRequest, TResponse>(
        eventName: string,
        serverHandler: (player: Player, request: TRequest) => TResponse | undefined,
        options: SyncOptions = {}
    ): SyncedAction<TRequest, TResponse> {

        // Create or get the RemoteEvent
        const remoteEvent = this.getOrCreateRemoteEvent(eventName);

        // Set up server-side handler with enhanced synchronization
        remoteEvent.OnServerEvent.Connect((player: Player, ...args: unknown[]) => {
            const requestData = args[0] as {
                request: TRequest;
                clientTimestamp?: number;
                sequenceNumber?: number;
            };

            const request = requestData.request;
            const clientTimestamp = requestData.clientTimestamp || tick();
            const sequenceNumber = requestData.sequenceNumber || 0;

            // Enhanced validation with timing
            const validationResult = this.validateSyncedRequest(
                player,
                eventName,
                request,
                clientTimestamp,
                sequenceNumber,
                options
            );

            if (!validationResult.valid) {
                // Send error response back to client
                remoteEvent.FireClient(player, {
                    success: false,
                    error: validationResult.reason,
                    serverTimestamp: tick()
                });
                return;
            }

            // Execute the server handler with timing context
            const startTime = tick();
            const response = serverHandler(player, request);
            const processingTime = tick() - startTime;

            // Set cooldown after successful execution
            if (options.validateCooldown && options.cooldownTime && response !== undefined) {
                this.setCooldown(player, eventName);
                this.updatePlayerSequence(player, sequenceNumber);
            }

            // Enhanced replication with synchronization data
            if (response !== undefined) {
                const syncedResponse = {
                    data: response,
                    serverTimestamp: tick(),
                    sequenceNumber: sequenceNumber,
                    processingTime: processingTime,
                    playerId: player.UserId
                };

                this.handleSyncedReplication(player, remoteEvent, syncedResponse, options);
            }
        });

        // Return the synced action interface
        return {
            fireToClient: (player: Player, response: TResponse) => {
                remoteEvent.FireClient(player, response);
            },
            fireToAllClients: (response: TResponse) => {
                remoteEvent.FireAllClients(response);
            },
            getRemoteEvent: () => remoteEvent
        };
    }

    /**
     * Create a simple RemoteEvent for custom handling
     */
    public static createRemoteEvent(
        eventName: string, 
        serverHandler: (player: Player, ...args: any[]) => void
    ): RemoteEvent {
        const remoteEvent = this.getOrCreateRemoteEvent(eventName);
        
        remoteEvent.OnServerEvent.Connect(serverHandler);
        
        return remoteEvent;
    }

    /**
     * Get or create a RemoteEvent
     */
    private static getOrCreateRemoteEvent(eventName: string): RemoteEvent {
        let remoteEvent = this.remoteEvents.get(eventName);
        
        if (!remoteEvent) {
            // Create or get RemoteEvents folder
            let remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
            if (!remoteEventsFolder) {
                remoteEventsFolder = new Instance("Folder");
                remoteEventsFolder.Name = "RemoteEvents";
                remoteEventsFolder.Parent = ReplicatedStorage;
            }

            // Create the RemoteEvent
            remoteEvent = new Instance("RemoteEvent");
            remoteEvent.Name = eventName;
            remoteEvent.Parent = remoteEventsFolder;
            
            this.remoteEvents.set(eventName, remoteEvent);
            
            print(`Created RemoteEvent: ${eventName}`);
        }
        
        return remoteEvent;
    }

    /**
     * Enhanced validation for synced requests with timing and sequence validation
     */
    private static validateSyncedRequest(
        player: Player,
        actionName: string,
        request: unknown,
        clientTimestamp: number,
        sequenceNumber: number,
        options: SyncOptions
    ): { valid: boolean; reason?: string } {
        // Validate sequence number to prevent replay attacks
        const lastSequence = this.playerSequenceNumbers.get(player) || 0;
        if (sequenceNumber <= lastSequence) {
            return { valid: false, reason: `Invalid sequence number: ${sequenceNumber} <= ${lastSequence}` };
        }

        // Validate timestamp to prevent old requests
        const timeDiff = tick() - clientTimestamp;
        if (timeDiff > 5) { // Reject requests older than 5 seconds
            return { valid: false, reason: `Request too old: ${math.floor(timeDiff)} seconds` };
        }

        // Cooldown validation
        if (options.validateCooldown && options.cooldownTime) {
            if (!this.validateCooldown(player, actionName, options.cooldownTime)) {
                return { valid: false, reason: "Action on cooldown" };
            }
        }

        // Range validation
        if (options.validateRange && options.maxRange) {
            if (!this.validateRange(player, request, options.maxRange)) {
                return { valid: false, reason: "Target out of range" };
            }
        }

        return { valid: true };
    }

    /**
     * Update player sequence number for replay attack prevention
     */
    private static updatePlayerSequence(player: Player, sequenceNumber: number): void {
        this.playerSequenceNumbers.set(player, sequenceNumber);
    }

    /**
     * Handle synced replication with batching and spatial optimization
     */
    private static handleSyncedReplication(
        originPlayer: Player,
        remoteEvent: RemoteEvent,
        response: unknown,
        options: SyncOptions
    ): void {
        if (options.replicateToAll) {
            // Batch replication for performance
            this.batchReplication(remoteEvent, response, "all");
        } else if (options.replicateToRadius && options.replicateToRadius > 0) {
            // Spatial replication with optimization
            this.replicateToRadiusOptimized(originPlayer, remoteEvent, response, options.replicateToRadius);
        } else {
            // Default: send back to originating player
            remoteEvent.FireClient(originPlayer, response);
        }
    }

    /**
     * Optimized radius-based replication with spatial partitioning
     */
    private static replicateToRadiusOptimized(
        originPlayer: Player,
        remoteEvent: RemoteEvent,
        response: unknown,
        radius: number
    ): void {
        const originCharacter = originPlayer.Character;
        if (!originCharacter) return;

        const originRootPart = originCharacter.FindFirstChild("HumanoidRootPart") as Part;
        if (!originRootPart) return;

        const originPosition = originRootPart.Position;
        const playersInRange: Player[] = [];

        // Optimized distance checking
        for (const player of Players.GetPlayers()) {
            if (player === originPlayer) continue;

            const character = player.Character;
            if (!character) continue;

            const rootPart = character.FindFirstChild("HumanoidRootPart") as Part;
            if (!rootPart) continue;

            // Quick distance check using squared distance (faster than magnitude)
            const deltaX = originPosition.X - rootPart.Position.X;
            const deltaZ = originPosition.Z - rootPart.Position.Z;
            const deltaY = originPosition.Y - rootPart.Position.Y;
            const distanceSquared = deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ;
            const radiusSquared = radius * radius;

            if (distanceSquared <= radiusSquared) {
                playersInRange.push(player);
            }
        }

        // Batch send to players in range
        if (playersInRange.size() > 0) {
            this.batchReplicationToPlayers(remoteEvent, response, playersInRange);
        }
    }

    /**
     * Batch replication for better performance
     */
    private static batchReplication(remoteEvent: RemoteEvent, response: unknown, target: "all"): void {
        const eventName = remoteEvent.Name;

        // Add to batch
        if (!this.actionBatches.has(eventName)) {
            this.actionBatches.set(eventName, []);
        }

        const batch = this.actionBatches.get(eventName)!;
        batch.push({
            sequenceNumber: 0,
            timestamp: tick(),
            playerId: 0,
            actionId: eventName,
            data: response
        });

        // Clear existing timer
        const existingTimer = this.batchTimers.get(eventName);
        if (existingTimer) {
            task.cancel(existingTimer);
        }

        // Set new timer to flush batch
        const timer = task.delay(0.016, () => { // ~60fps batching
            this.flushBatch(remoteEvent, eventName, target);
        });

        this.batchTimers.set(eventName, timer);
    }

    /**
     * Batch replication to specific players
     */
    private static batchReplicationToPlayers(
        remoteEvent: RemoteEvent,
        response: unknown,
        players: Player[]
    ): void {
        // For small groups, send immediately
        if (players.size() <= 3) {
            for (const player of players) {
                remoteEvent.FireClient(player, response);
            }
            return;
        }

        // For larger groups, use batching
        // Implementation would depend on specific needs
        for (const player of players) {
            remoteEvent.FireClient(player, response);
        }
    }

    /**
     * Flush batched actions
     */
    private static flushBatch(remoteEvent: RemoteEvent, eventName: string, target: "all"): void {
        const batch = this.actionBatches.get(eventName);
        if (!batch || batch.size() === 0) return;

        // Send batched data
        if (target === "all") {
            remoteEvent.FireAllClients({
                batchedActions: batch,
                batchTimestamp: tick()
            });
        }

        // Clear batch
        this.actionBatches.set(eventName, []);
        this.batchTimers.delete(eventName);
    }

    /**
     * Validate cooldown for a player action
     */
    private static validateCooldown(player: Player, actionName: string, cooldownTime: number): boolean {
        let playerCooldowns = this.playerCooldowns.get(player);
        if (!playerCooldowns) {
            playerCooldowns = new Map();
            this.playerCooldowns.set(player, playerCooldowns);
        }

        const lastUsed = playerCooldowns.get(actionName);
        if (lastUsed !== undefined) {
            const timeSinceLastUse = tick() - lastUsed;
            if (timeSinceLastUse < cooldownTime) {
                return false; // Still on cooldown
            }
        }

        return true; // Not on cooldown
    }

    /**
     * Set cooldown for a player action
     */
    private static setCooldown(player: Player, actionName: string): void {
        let playerCooldowns = this.playerCooldowns.get(player);
        if (!playerCooldowns) {
            playerCooldowns = new Map();
            this.playerCooldowns.set(player, playerCooldowns);
        }

        playerCooldowns.set(actionName, tick());
    }

    /**
     * Validate range for a player action
     */
    private static validateRange(player: Player, request: unknown, maxRange: number): boolean {
        const character = player.Character;
        if (!character) return false;

        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (!humanoidRootPart) return false;

        // Try to extract position from request
        let targetPosition: Vector3 | undefined;
        if (request && typeIs(request, "table")) {
            const requestObj = request as Record<string, unknown>;
            if (requestObj.position && typeIs(requestObj.position, "Vector3")) {
                targetPosition = requestObj.position;
            } else if (requestObj.targetPosition && typeIs(requestObj.targetPosition, "Vector3")) {
                targetPosition = requestObj.targetPosition;
            }
        }

        if (!targetPosition) {
            return true; // No position to validate, allow the action
        }

        const distance = humanoidRootPart.Position.sub(targetPosition).Magnitude;
        return distance <= maxRange;
    }

    /**
     * Replicate to players within radius
     */
    private static replicateToRadius(
        originPlayer: Player, 
        remoteEvent: RemoteEvent, 
        response: any, 
        radius: number
    ): void {
        const originCharacter = originPlayer.Character;
        if (!originCharacter) return;

        const originRootPart = originCharacter.FindFirstChild("HumanoidRootPart") as Part;
        if (!originRootPart) return;

        for (const player of Players.GetPlayers()) {
            if (player === originPlayer) continue; // Skip origin player

            const character = player.Character;
            if (!character) continue;

            const rootPart = character.FindFirstChild("HumanoidRootPart") as Part;
            if (!rootPart) continue;

            const distance = originRootPart.Position.sub(rootPart.Position).Magnitude;
            if (distance <= radius) {
                remoteEvent.FireClient(player, response);
            }
        }
    }

    /**
     * Initialize cleanup for when players leave with enhanced cleanup
     */
    public static initializeCleanup(): void {
        Players.PlayerRemoving.Connect((player) => {
            // Clean up all player-related data
            this.playerCooldowns.delete(player);
            this.playerSequenceNumbers.delete(player);

            // Clean up any pending actions for this player
            for (const [eventName, pendingList] of this.pendingActions) {
                const filteredPending = pendingList.filter(action => action.player !== player);
                this.pendingActions.set(eventName, filteredPending);
            }

            print(`🧹 Cleaned up sync data for ${player.Name}`);
        });

        Players.PlayerAdded.Connect((player) => {
            // Initialize player sync data
            this.playerSequenceNumbers.set(player, 0);
            print(`🔄 Initialized sync data for ${player.Name}`);
        });
    }

    /**
     * Get server synchronization statistics for monitoring
     */
    public static getSyncStats(): {
        connectedPlayers: number;
        activeEvents: number;
        pendingActions: number;
        averageLatency: number;
    } {
        let totalPending = 0;
        for (const [, pendingList] of this.pendingActions) {
            totalPending += pendingList.size();
        }

        return {
            connectedPlayers: Players.GetPlayers().size(),
            activeEvents: this.remoteEvents.size(),
            pendingActions: totalPending,
            averageLatency: 0 // Would need to implement latency tracking
        };
    }

    /**
     * Force synchronization of all players (emergency sync)
     */
    public static forceSyncAll(eventName: string, syncData: unknown): void {
        const remoteEvent = this.remoteEvents.get(eventName);
        if (!remoteEvent) {
            warn(`Cannot force sync - event ${eventName} not found`);
            return;
        }

        const forceSyncPacket = {
            type: "FORCE_SYNC",
            data: syncData,
            serverTimestamp: tick(),
            reason: "Server initiated sync"
        };

        remoteEvent.FireAllClients(forceSyncPacket);
        print(`🔄 Force synced all players for event: ${eventName}`);
    }

    /**
     * Get all created RemoteEvents
     */
    public static getRemoteEvents(): Map<string, RemoteEvent> {
        return this.remoteEvents;
    }

    /**
     * Get a specific RemoteEvent by name
     */
    public static getRemoteEvent(eventName: string): RemoteEvent | undefined {
        return this.remoteEvents.get(eventName);
    }
}
