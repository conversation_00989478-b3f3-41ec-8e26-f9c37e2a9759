-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local createParticleExplosion = _core.createParticleExplosion
local EffectPartBuilder = _core.EffectPartBuilder
local EffectTweenBuilder = _core.EffectTweenBuilder
local function createPunchParticleExplosion(position, direction)
	-- Use Core framework createParticleExplosion - exact same behavior
	createParticleExplosion(position, 40, Color3.fromRGB(255, 200, 100), { 25, 50 }, { 0.2, 0.6 })
end
local function createEnergyTrails(startPosition, endPosition)
	-- Create energy trails that follow the punch path using Core framework
	local numTrails = 8
	for i = 0, numTrails - 1 do
		-- Position along punch path with some randomness
		local t = i / numTrails
		local basePosition = startPosition:Lerp(endPosition, t)
		local offset = Vector3.new(math.random(-2, 2), math.random(-1, 1), math.random(-2, 2))
		local trailPosition = basePosition + offset
		-- Orient towards punch direction
		local _endPosition = endPosition
		local _startPosition = startPosition
		local direction = (_endPosition - _startPosition).Unit
		local trailCFrame = CFrame.new(trailPosition, trailPosition + direction)
		-- Create trail using EffectPartBuilder
		local trail = EffectPartBuilder:create():shape(Enum.PartType.Block):size(Vector3.new(0.3, 0.3, 2)):color(Color3.fromRGB(100, 200, 255)):material(Enum.Material.Neon):transparency(0.3):cframe(trailCFrame):spawn()
		trail.Name = `EnergyTrail_{i}`
		-- Delayed appearance and fade using EffectTweenBuilder
		task.delay(i * 0.05, function()
			EffectTweenBuilder["for"](EffectTweenBuilder, trail):fade(0.1):duration(0.1):play()
			task.delay(1, function()
				EffectTweenBuilder["for"](EffectTweenBuilder, trail):fade(1):duration(1.5):onComplete(function()
					return trail:Destroy()
				end):play()
			end)
		end)
	end
end
return {
	createPunchParticleExplosion = createPunchParticleExplosion,
	createEnergyTrails = createEnergyTrails,
}
