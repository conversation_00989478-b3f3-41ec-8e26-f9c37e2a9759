-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local TweenService = _services.TweenService
local Workspace = _services.Workspace
local Players = _services.Players
local CrackEffectHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core").CrackEffectHelper
local function createAirCracks(position)
	print(`🔥 Creating {8} air cracks at position: {position}`)
	-- Use Core framework CrackEffectHelper - exact same behavior
	CrackEffectHelper:createAirCracks(position, 8)
end
local createEnhancedGlassCracks, createEnergyLightning, createFlyingObjects, createCrackSparklingEffects
local function createEnhancedAirCracks(position, direction)
	print(`🔥 Creating ENHANCED Gura Gura air cracks at position: {position}, direction: {direction}`)
	-- Create massive impact flash first (temporarily disabled to debug black square)
	-- this.createMassiveImpactFlash(position);
	-- Create enhanced glass crack system (main feature)
	createEnhancedGlassCracks(position, direction)
	-- Create atmospheric distortion waves (temporarily disabled to debug black square)
	-- this.createAtmosphericDistortion(position, direction);
	-- Create reduced energy lightning around cracks
	createEnergyLightning(position)
	-- Create floating energy particles (temporarily disabled to debug black square)
	-- this.createEnergyParticleStorm(position);
	-- Create reality shatter effect (disabled - was causing black box)
	-- this.createRealityShatter(position, direction);
	-- Create flying objects effect (was missing!)
	createFlyingObjects(position)
	-- ✨ NEW: Create spectacular sparkling effects for the cracks
	createCrackSparklingEffects(position, direction)
	print("✅ ENHANCED Gura Gura air cracks created with MASSIVE effects including flying objects and sparkling")
end
local createGlassFragments
function createEnhancedGlassCracks(position, direction)
	-- Create stunning realistic glass crack pattern like punching through air
	print(`💎 Creating enhanced glass crack system at {position}`)
	-- Use Core framework CrackEffectHelper for glass cracks - exact same behavior
	CrackEffectHelper:createGlassCracks(position, direction, 8, 12)
	-- Create floating glass fragments (game-specific effect)
	createGlassFragments(position, direction)
	print("✅ Enhanced glass crack system created")
end
local function createGlassImpactCenter(position)
	-- Create bright impact center like hitting glass
	local impact = Instance.new("Part")
	impact.Name = "GlassImpactCenter"
	impact.Shape = Enum.PartType.Ball
	impact.Size = Vector3.new(2, 2, 2)
	impact.Color = Color3.fromRGB(255, 255, 255)
	impact.Material = Enum.Material.Neon
	impact.Transparency = 0
	impact.CanCollide = false
	impact.Anchored = true
	impact.Position = position
	impact.Parent = Workspace
	-- Bright white light
	local light = Instance.new("PointLight")
	light.Color = Color3.fromRGB(255, 255, 255)
	light.Range = 30
	light.Brightness = 20
	light.Parent = impact
	-- Expand and fade quickly
	local expandTween = TweenService:Create(impact, TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
		Size = Vector3.new(8, 8, 8),
		Transparency = 1,
	})
	expandTween:Play()
	expandTween.Completed:Connect(function()
		return impact:Destroy()
	end)
end
local createEnhancedGlassCrackLine
local function createMainGlassCracks(position, direction)
	-- Create main radiating glass cracks
	local numMainCracks = 8
	for i = 0, numMainCracks - 1 do
		local angle = (i / numMainCracks) * math.pi * 2
		local delay = i * 0.02
		task.delay(delay, function()
			createEnhancedGlassCrackLine(position, angle, 15, true)
		end)
	end
end
function createEnhancedGlassCrackLine(startPoint, angle, maxLength, isMainCrack)
	local segments = if isMainCrack then 12 else 6
	local segmentLength = maxLength / segments
	for seg = 0, segments - 1 do
		local segmentDelay = seg * 0.01
		task.delay(segmentDelay, function()
			local crack = Instance.new("Part")
			crack.Name = `EnhancedGlassCrack_{if isMainCrack then "Main" else "Secondary"}_{seg}`
			crack.Shape = Enum.PartType.Block
			-- Realistic glass crack appearance
			local thickness = if isMainCrack then 0.05 else 0.03
			local length = segmentLength + math.random(-0.2, 0.2)
			local width = if isMainCrack then 0.08 else 0.05
			crack.Size = Vector3.new(thickness, width, length)
			-- Bright glass-like appearance
			crack.Color = Color3.fromRGB(240, 250, 255)
			crack.Material = Enum.Material.Neon
			crack.Transparency = 0.1
			crack.CanCollide = false
			crack.Anchored = true
			crack.Parent = Workspace
			-- Position along crack line with natural variation
			local deviation = math.random(-0.2, 0.2)
			local heightVariation = math.random(-1, 1)
			local distance = seg * segmentLength + segmentLength * 0.5
			local _startPoint = startPoint
			local _vector3 = Vector3.new(math.cos(angle + deviation) * distance, math.sin(angle + deviation) * distance + heightVariation, math.random(-0.3, 0.3))
			local crackPos = _startPoint + _vector3
			crack.Position = crackPos
			local _vector3_1 = Vector3.new(math.cos(angle), math.sin(angle), 0)
			crack.CFrame = CFrame.lookAt(crackPos, crackPos + _vector3_1)
			-- Bright glow for main cracks
			if isMainCrack then
				local light = Instance.new("PointLight")
				light.Color = Color3.fromRGB(200, 230, 255)
				light.Range = 2
				light.Brightness = 3
				light.Parent = crack
			end
			-- Flash into existence
			crack.Transparency = 1
			local flashTween = TweenService:Create(crack, TweenInfo.new(0.02, Enum.EasingStyle.Quad), {
				Transparency = 0.1,
			})
			flashTween:Play()
			-- Fade out after time
			task.delay(5 + math.random(2, 4), function()
				local fadeOut = TweenService:Create(crack, TweenInfo.new(2, Enum.EasingStyle.Quad), {
					Transparency = 1,
				})
				fadeOut:Play()
				fadeOut.Completed:Connect(function()
					return crack:Destroy()
				end)
			end)
		end)
	end
end
local function createEnhancedGlassWebPattern(position)
	-- Create secondary connecting cracks for web pattern
	local numWebCracks = 12
	for i = 0, numWebCracks - 1 do
		local delay = 0.2 + (i * 0.03)
		task.delay(delay, function()
			local angle = math.random(0, math.pi * 2)
			local startRadius = math.random(3, 8)
			local _position = position
			local _vector3 = Vector3.new(math.cos(angle) * startRadius, math.sin(angle) * startRadius, math.random(-1, 1))
			local startPoint = _position + _vector3
			createEnhancedGlassCrackLine(startPoint, angle + math.random(-0.5, 0.5), 8, false)
		end)
	end
end
function createGlassFragments(position, direction)
	-- Create floating glass fragments
	local numFragments = 15
	for i = 0, numFragments - 1 do
		local fragment = Instance.new("Part")
		fragment.Name = `GlassFragment_{i}`
		fragment.Shape = Enum.PartType.Block
		fragment.Size = Vector3.new(math.random(0.2, 0.6), math.random(0.2, 0.6), math.random(0.05, 0.1))
		fragment.Color = Color3.fromRGB(240, 248, 255)
		fragment.Material = Enum.Material.Glass
		fragment.Transparency = 0.2
		fragment.CanCollide = false
		fragment.Anchored = false
		fragment.Parent = Workspace
		-- Position around impact point
		local angle = (i / numFragments) * math.pi * 2
		local radius = math.random(2, 8)
		local _position = position
		local _vector3 = Vector3.new(math.cos(angle) * radius, math.sin(angle) * radius + math.random(1, 4), math.random(-1, 1))
		fragment.Position = _position + _vector3
		-- Apply gentle floating motion
		local bodyVelocity = Instance.new("BodyVelocity")
		bodyVelocity.MaxForce = Vector3.new(2000, 2000, 2000)
		bodyVelocity.Velocity = Vector3.new(math.random(-5, 5), math.random(-3, 8), math.random(-5, 5))
		bodyVelocity.Parent = fragment
		-- Gentle rotation
		local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
		bodyAngularVelocity.MaxTorque = Vector3.new(500, 500, 500)
		bodyAngularVelocity.AngularVelocity = Vector3.new(math.random(-2, 2), math.random(-2, 2), math.random(-2, 2))
		bodyAngularVelocity.Parent = fragment
		-- Remove forces and fade out
		task.delay(2, function()
			if bodyVelocity.Parent then
				bodyVelocity:Destroy()
			end
			if bodyAngularVelocity.Parent then
				bodyAngularVelocity:Destroy()
			end
		end)
		task.delay(4 + math.random(2, 4), function()
			local fadeOut = TweenService:Create(fragment, TweenInfo.new(2, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				return fragment:Destroy()
			end)
		end)
	end
end
local createIceImpactCrater, createExpandingIceCracks, createIceShards
local function createRealisticGlassBreaking(position)
	-- Create a stunning ice breaking effect like punching through frozen air
	local impactPoint = position
	-- Create impact point with bright flash
	createIceImpactCrater(impactPoint)
	-- Create expanding ice crack pattern that spreads from impact
	createExpandingIceCracks(impactPoint)
	-- Create ice shards that fall from the cracks
	createIceShards(impactPoint)
end
function createIceImpactCrater(position)
	-- Create a bright ice impact point with expanding frost rings
	local crater = Instance.new("Part")
	crater.Name = "IceImpactCrater"
	crater.Shape = Enum.PartType.Ball
	crater.Size = Vector3.new(1.5, 1.5, 1.5)
	crater.Color = Color3.fromRGB(200, 230, 255)
	crater.Material = Enum.Material.Neon
	crater.Transparency = 0
	crater.CanCollide = false
	crater.Anchored = true
	crater.Position = position
	crater.Parent = Workspace
	-- Bright ice-blue light
	local light = Instance.new("PointLight")
	light.Color = Color3.fromRGB(200, 230, 255)
	light.Range = 30
	light.Brightness = 25
	light.Parent = crater
	-- Expand and fade with ice effect
	local expandTween = TweenService:Create(crater, TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
		Size = Vector3.new(8, 8, 8),
		Transparency = 1,
	})
	local lightFade = TweenService:Create(light, TweenInfo.new(0.5, Enum.EasingStyle.Quad), {
		Brightness = 0,
	})
	expandTween:Play()
	lightFade:Play()
	expandTween.Completed:Connect(function()
		crater:Destroy()
	end)
end
local createIceCrackLine, createIceCrystals
function createExpandingIceCracks(impactPoint)
	-- Create realistic ice crack pattern that expands from impact point
	local numMainCracks = 8
	-- Create main radial ice cracks - faster timing
	for i = 0, numMainCracks - 1 do
		local angle = (i / numMainCracks) * math.pi * 2
		local delay = i * 0.03
		task.delay(delay, function()
			createIceCrackLine(impactPoint, angle, 15, true)
		end)
	end
	-- Create secondary branching ice cracks - faster timing
	task.delay(0.15, function()
		for i = 0, 15 do
			local angle = math.random(0, math.pi * 2)
			local delay = i * 0.02
			task.delay(delay, function()
				-- Start from points around the impact area
				local _impactPoint = impactPoint
				local _vector3 = Vector3.new(math.cos(angle) * math.random(2, 6), math.sin(angle) * math.random(2, 6), math.random(-2, 2))
				local startPoint = _impactPoint + _vector3
				createIceCrackLine(startPoint, angle + math.random(-0.5, 0.5), 8, false)
			end)
		end
	end)
	-- Create ice crystal formations - faster timing
	task.delay(0.3, function()
		createIceCrystals(impactPoint)
	end)
end
local function createVerticalIceCrack(startPoint, endPoint, isMainCrack)
	-- Create a vertical ice crack line from start to end point
	local _endPoint = endPoint
	local _startPoint = startPoint
	local direction = _endPoint - _startPoint
	local totalLength = direction.Magnitude
	local segments = if isMainCrack then 10 else 6
	local segmentLength = totalLength / segments
	for seg = 0, segments - 1 do
		local segmentDelay = seg * 0.015
		task.delay(segmentDelay, function()
			local crack = Instance.new("Part")
			crack.Name = `VerticalIceCrack_{if isMainCrack then "Main" else "Branch"}_{seg}`
			crack.Shape = Enum.PartType.Block
			-- Vertical ice crack appearance
			local thickness = if isMainCrack then 0.1 else 0.06
			local width = if isMainCrack then 0.15 else 0.1
			crack.Size = Vector3.new(thickness, segmentLength, width)
			-- Ice-like appearance with blue tint
			crack.Color = Color3.fromRGB(200, 230, 255)
			crack.Material = Enum.Material.Ice
			crack.Transparency = 0.1
			crack.CanCollide = false
			crack.Anchored = true
			crack.Parent = Workspace
			-- Position along the vertical crack line
			local progress = (seg + 0.5) / segments
			local _startPoint_1 = startPoint
			local _arg0 = direction * progress
			local segmentPos = _startPoint_1 + _arg0
			crack.Position = segmentPos
			-- Orient vertically
			local _unit = direction.Unit
			crack.CFrame = CFrame.new(segmentPos, segmentPos + _unit)
			-- Ice-blue glow effect
			local light = Instance.new("PointLight")
			light.Color = Color3.fromRGB(150, 200, 255)
			light.Range = if isMainCrack then 4 else 2
			light.Brightness = if isMainCrack then 5 else 3
			light.Parent = crack
			-- Start invisible and flash into existence
			crack.Transparency = 1
			local flashTween = TweenService:Create(crack, TweenInfo.new(0.05, Enum.EasingStyle.Quad), {
				Transparency = 0.1,
			})
			flashTween:Play()
			-- Gradually fade out
			task.delay(6 + math.random(2, 4), function()
				local fadeOut = TweenService:Create(crack, TweenInfo.new(3, Enum.EasingStyle.Quad), {
					Transparency = 1,
				})
				fadeOut:Play()
				fadeOut.Completed:Connect(function()
					crack:Destroy()
				end)
			end)
		end)
	end
end
function createIceCrackLine(startPoint, angle, maxLength, isMainCrack)
	local segments = if isMainCrack then 12 else 8
	local segmentLength = maxLength / segments
	for seg = 0, segments - 1 do
		local segmentDelay = seg * 0.015
		task.delay(segmentDelay, function()
			local crack = Instance.new("Part")
			crack.Name = `IceCrack_{if isMainCrack then "Main" else "Branch"}_{seg}`
			crack.Shape = Enum.PartType.Block
			-- Ice crack appearance - thicker than glass
			local thickness = if isMainCrack then 0.08 else 0.05
			local length = segmentLength + math.random(-0.3, 0.3)
			local width = if isMainCrack then 0.12 else 0.08
			crack.Size = Vector3.new(thickness, width, length)
			-- Ice-like appearance with blue tint
			crack.Color = Color3.fromRGB(200, 230, 255)
			crack.Material = Enum.Material.Ice
			crack.Transparency = 0.1
			crack.CanCollide = false
			crack.Anchored = true
			crack.Parent = Workspace
			-- Position along the crack line with natural variation
			local deviation = math.random(-0.4, 0.4)
			local heightVariation = math.random(-1, 1)
			local distance = seg * segmentLength + segmentLength * 0.5
			local _startPoint = startPoint
			local _vector3 = Vector3.new(math.cos(angle + deviation) * distance, math.sin(angle + deviation) * distance + heightVariation, math.random(-0.5, 0.5))
			local crackPos = _startPoint + _vector3
			crack.Position = crackPos
			local _vector3_1 = Vector3.new(math.cos(angle), math.sin(angle), 0)
			crack.CFrame = CFrame.lookAt(crackPos, crackPos + _vector3_1)
			-- Ice-blue glow effect
			local light = Instance.new("PointLight")
			light.Color = Color3.fromRGB(150, 200, 255)
			light.Range = if isMainCrack then 3 else 2
			light.Brightness = if isMainCrack then 4 else 2
			light.Parent = crack
			-- Start invisible and flash into existence
			crack.Transparency = 1
			local flashTween = TweenService:Create(crack, TweenInfo.new(0.05, Enum.EasingStyle.Quad), {
				Transparency = 0.1,
			})
			flashTween:Play()
			-- Gradually fade out
			task.delay(6 + math.random(2, 4), function()
				local fadeOut = TweenService:Create(crack, TweenInfo.new(3, Enum.EasingStyle.Quad), {
					Transparency = 1,
				})
				fadeOut:Play()
				fadeOut.Completed:Connect(function()
					crack:Destroy()
				end)
			end)
		end)
	end
end
function createIceCrystals(centerPosition)
	-- Create ice crystal formations around the impact point
	local numCrystals = 12
	for i = 0, numCrystals - 1 do
		local crystal = Instance.new("Part")
		crystal.Name = `IceCrystal_{i}`
		crystal.Shape = Enum.PartType.Block
		crystal.Size = Vector3.new(math.random(0.3, 0.8), math.random(1, 2.5), math.random(0.3, 0.8))
		crystal.Color = Color3.fromRGB(220, 240, 255)
		crystal.Material = Enum.Material.Ice
		crystal.Transparency = 0.2
		crystal.CanCollide = false
		crystal.Anchored = true
		crystal.Parent = Workspace
		-- Position crystals around the impact point
		local angle = (i / numCrystals) * math.pi * 2
		local radius = math.random(4, 10)
		local _centerPosition = centerPosition
		local _vector3 = Vector3.new(math.cos(angle) * radius, math.sin(angle) * radius, math.random(-2, 2))
		crystal.Position = _centerPosition + _vector3
		-- Random rotation for natural look
		local _cFrame = crystal.CFrame
		local _arg0 = CFrame.Angles(math.random(0, math.pi), math.random(0, math.pi), math.random(0, math.pi))
		crystal.CFrame = _cFrame * _arg0
		-- Ice-blue glow
		local light = Instance.new("PointLight")
		light.Color = Color3.fromRGB(150, 200, 255)
		light.Range = 2
		light.Brightness = 3
		light.Parent = crystal
		-- Delayed appearance
		crystal.Transparency = 1
		local delay = i * 0.08
		task.delay(delay, function()
			local appearTween = TweenService:Create(crystal, TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
				Transparency = 0.2,
			})
			appearTween:Play()
		end)
		-- Fade out after some time
		task.delay(5 + math.random(2, 4), function()
			local fadeOut = TweenService:Create(crystal, TweenInfo.new(2, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				crystal:Destroy()
			end)
		end)
	end
end
function createIceShards(centerPosition)
	-- Create ice shards that fall from the broken ice
	local numShards = 20
	for i = 0, numShards - 1 do
		local shard = Instance.new("Part")
		shard.Name = `IceShard_{i}`
		shard.Shape = Enum.PartType.Block
		shard.Size = Vector3.new(math.random(0.2, 0.6), math.random(0.4, 1.2), math.random(0.1, 0.3))
		shard.Color = Color3.fromRGB(220, 240, 255)
		shard.Material = Enum.Material.Ice
		shard.Transparency = 0.15
		shard.CanCollide = false
		shard.Anchored = false
		shard.Parent = Workspace
		-- Position around the impact point
		local angle = (i / numShards) * math.pi * 2
		local radius = math.random(3, 8)
		local _centerPosition = centerPosition
		local _vector3 = Vector3.new(math.cos(angle) * radius, math.sin(angle) * radius + math.random(3, 6), math.random(-1, 1))
		shard.Position = _centerPosition + _vector3
		-- Ice-blue glow
		local light = Instance.new("PointLight")
		light.Color = Color3.fromRGB(150, 200, 255)
		light.Range = 1
		light.Brightness = 1.5
		light.Parent = shard
		-- Apply gentle falling motion
		local bodyVelocity = Instance.new("BodyVelocity")
		bodyVelocity.MaxForce = Vector3.new(1500, 1500, 1500)
		bodyVelocity.Velocity = Vector3.new(math.random(-8, 8), math.random(-15, -8), math.random(-8, 8))
		bodyVelocity.Parent = shard
		-- Add gentle rotation
		local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
		bodyAngularVelocity.MaxTorque = Vector3.new(300, 300, 300)
		bodyAngularVelocity.AngularVelocity = Vector3.new(math.random(-3, 3), math.random(-3, 3), math.random(-3, 3))
		bodyAngularVelocity.Parent = shard
		-- Remove forces after some time
		task.delay(2, function()
			if bodyVelocity.Parent then
				bodyVelocity:Destroy()
			end
			if bodyAngularVelocity.Parent then
				bodyAngularVelocity:Destroy()
			end
		end)
		-- Fade out and destroy
		task.delay(4 + math.random(2, 4), function()
			local fadeOut = TweenService:Create(shard, TweenInfo.new(2, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				shard:Destroy()
			end)
		end)
	end
	print(`❄️ Created {numShards} falling ice shards`)
end
local function createGlassCrackLine(startPoint, angle, maxLength, isMainCrack, crackLines)
	local segments = if isMainCrack then 15 else 8
	local segmentLength = maxLength / segments
	for seg = 0, segments - 1 do
		local segmentDelay = seg * 0.02
		task.delay(segmentDelay, function()
			local crack = Instance.new("Part")
			crack.Name = `GlassCrack_{if isMainCrack then "Main" else "Branch"}_{seg}`
			crack.Shape = Enum.PartType.Block
			-- Very thin crack line
			local thickness = if isMainCrack then 0.03 else 0.02
			local length = segmentLength + math.random(-0.2, 0.2)
			crack.Size = Vector3.new(thickness, thickness, length)
			-- Bright glass-like appearance
			crack.Color = Color3.fromRGB(255, 255, 255)
			crack.Material = Enum.Material.Neon
			crack.Transparency = 0
			crack.CanCollide = false
			crack.Anchored = true
			crack.Parent = Workspace
			-- Position along the crack line with slight randomness
			local deviation = math.random(-0.3, 0.3)
			local distance = seg * segmentLength + segmentLength * 0.5
			local _startPoint = startPoint
			local _vector3 = Vector3.new(math.cos(angle + deviation) * distance, math.sin(angle + deviation) * distance, math.random(-0.1, 0.1))
			local crackPos = _startPoint + _vector3
			crack.Position = crackPos
			local _vector3_1 = Vector3.new(math.cos(angle), math.sin(angle), 0)
			crack.CFrame = CFrame.lookAt(crackPos, crackPos + _vector3_1)
			-- Add to crack lines array for web connections
			table.insert(crackLines, crack)
			-- Bright glow effect
			local light = Instance.new("PointLight")
			light.Color = Color3.fromRGB(200, 230, 255)
			light.Range = if isMainCrack then 2 else 1
			light.Brightness = if isMainCrack then 3 else 2
			light.Parent = crack
			-- Start invisible and flash into existence
			crack.Transparency = 1
			local flashTween = TweenService:Create(crack, TweenInfo.new(0.02, Enum.EasingStyle.Quad), {
				Transparency = 0,
			})
			flashTween:Play()
			-- Gradually fade out
			task.delay(4 + math.random(2, 4), function()
				local fadeOut = TweenService:Create(crack, TweenInfo.new(2, Enum.EasingStyle.Quad), {
					Transparency = 1,
				})
				fadeOut:Play()
				fadeOut.Completed:Connect(function()
					crack:Destroy()
				end)
			end)
		end)
	end
end
local createGlassWebSegment
local function createGlassWebConnections(impactPoint, crackLines)
	-- Create web-like connections between crack segments
	local numConnections = math.min(15, #crackLines)
	do
		local i = 0
		local _shouldIncrement = false
		while true do
			if _shouldIncrement then
				i += 1
			else
				_shouldIncrement = true
			end
			if not (i < numConnections) then
				break
			end
			if #crackLines < 2 then
				break
			end
			local delay = i * 0.04
			task.delay(delay, function()
				-- Connect random crack segments
				local crack1 = crackLines[math.floor(math.random() * #crackLines) + 1]
				local crack2 = crackLines[math.floor(math.random() * #crackLines) + 1]
				if crack1 ~= crack2 and crack1.Parent and crack2.Parent then
					local _position = crack1.Position
					local _position_1 = crack2.Position
					local distance = (_position - _position_1).Magnitude
					if distance > 1 and distance < 8 then
						createGlassWebSegment(crack1.Position, crack2.Position)
					end
				end
			end)
		end
	end
end
function createGlassWebSegment(pos1, pos2)
	local webSegment = Instance.new("Part")
	webSegment.Name = "GlassWebConnection"
	webSegment.Shape = Enum.PartType.Block
	local _pos2 = pos2
	local _pos1 = pos1
	local direction = _pos2 - _pos1
	local length = direction.Magnitude
	local _pos1_1 = pos1
	local _arg0 = direction * 0.5
	local midPoint = _pos1_1 + _arg0
	webSegment.Size = Vector3.new(0.015, 0.015, length)
	webSegment.Color = Color3.fromRGB(255, 255, 255)
	webSegment.Material = Enum.Material.Neon
	webSegment.Transparency = 0.2
	webSegment.CanCollide = false
	webSegment.Anchored = true
	webSegment.Parent = Workspace
	webSegment.CFrame = CFrame.lookAt(midPoint, pos2)
	-- Subtle glow
	local light = Instance.new("PointLight")
	light.Color = Color3.fromRGB(200, 230, 255)
	light.Range = 0.5
	light.Brightness = 1
	light.Parent = webSegment
	-- Flash into existence
	webSegment.Transparency = 1
	local appearTween = TweenService:Create(webSegment, TweenInfo.new(0.03, Enum.EasingStyle.Quad), {
		Transparency = 0.2,
	})
	appearTween:Play()
	-- Fade out
	task.delay(3 + math.random(1, 3), function()
		local fadeOut = TweenService:Create(webSegment, TweenInfo.new(2, Enum.EasingStyle.Quad), {
			Transparency = 1,
		})
		fadeOut:Play()
		fadeOut.Completed:Connect(function()
			webSegment:Destroy()
		end)
	end)
end
local function createGlassImpactCrater(position)
	-- Create a bright impact point with expanding rings
	local crater = Instance.new("Part")
	crater.Name = "GlassImpactCrater"
	crater.Shape = Enum.PartType.Ball
	crater.Size = Vector3.new(1, 1, 1)
	crater.Color = Color3.fromRGB(255, 255, 255)
	crater.Material = Enum.Material.Neon
	crater.Transparency = 0
	crater.CanCollide = false
	crater.Anchored = true
	crater.Position = position
	crater.Parent = Workspace
	-- Bright light
	local light = Instance.new("PointLight")
	light.Color = Color3.fromRGB(255, 255, 255)
	light.Range = 25
	light.Brightness = 20
	light.Parent = crater
	-- Expand and fade
	local expandTween = TweenService:Create(crater, TweenInfo.new(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
		Size = Vector3.new(6, 6, 6),
		Transparency = 1,
	})
	local lightFade = TweenService:Create(light, TweenInfo.new(0.4, Enum.EasingStyle.Quad), {
		Brightness = 0,
	})
	expandTween:Play()
	lightFade:Play()
	expandTween.Completed:Connect(function()
		crater:Destroy()
	end)
end
local createCrackSegment
local function animateCrackLine(nodes, delay, isMainCrack)
	for i = 0, #nodes - 2 do
		local startNode = nodes[i + 1]
		local endNode = nodes[i + 2]
		local segmentDelay = delay + (i * 0.03)
		task.delay(segmentDelay, function()
			createCrackSegment(startNode, endNode, isMainCrack)
		end)
	end
end
function createCrackSegment(startPos, endPos, isMainCrack)
	local segment = Instance.new("Part")
	segment.Name = `GlassCrack_{if isMainCrack then "Main" else "Secondary"}`
	segment.Shape = Enum.PartType.Block
	-- Calculate segment properties
	local _endPos = endPos
	local _startPos = startPos
	local direction = _endPos - _startPos
	local length = direction.Magnitude
	local _startPos_1 = startPos
	local _arg0 = direction * 0.5
	local midPoint = _startPos_1 + _arg0
	-- Thin crack appearance
	local thickness = if isMainCrack then 0.05 else 0.03
	segment.Size = Vector3.new(thickness, thickness, length)
	-- Realistic glass crack appearance
	segment.Color = Color3.fromRGB(245, 250, 255)
	segment.Material = Enum.Material.Glass
	segment.Transparency = if isMainCrack then 0.1 else 0.2
	segment.CanCollide = false
	segment.Anchored = true
	segment.Parent = Workspace
	-- Orient segment along the crack direction
	segment.CFrame = CFrame.lookAt(midPoint, endPos)
	-- Add subtle glow for main cracks
	if isMainCrack then
		local light = Instance.new("PointLight")
		light.Color = Color3.fromRGB(200, 220, 255)
		light.Range = 1.5
		light.Brightness = 0.8
		light.Parent = segment
	end
	-- Start invisible and appear quickly
	segment.Transparency = 1
	local appearTween = TweenService:Create(segment, TweenInfo.new(0.05, Enum.EasingStyle.Quad), {
		Transparency = if isMainCrack then 0.1 else 0.2,
	})
	appearTween:Play()
	-- Fade out after some time
	task.delay(4 + math.random(1, 3), function()
		local fadeOut = TweenService:Create(segment, TweenInfo.new(2, Enum.EasingStyle.Quad), {
			Transparency = 1,
		})
		fadeOut:Play()
		fadeOut.Completed:Connect(function()
			segment:Destroy()
		end)
	end)
end
local function createGlassWebPattern(centerPoint, existingNodes)
	-- Create interconnecting web cracks between existing crack nodes
	local numWebCracks = 8
	for i = 0, numWebCracks - 1 do
		if #existingNodes < 2 then
			break
		end
		-- Connect random nodes to create web pattern
		local node1 = existingNodes[math.floor(math.random() * #existingNodes) + 1]
		local node2 = existingNodes[math.floor(math.random() * #existingNodes) + 1]
		if node1 ~= node2 then
			local delay = i * 0.1
			task.delay(delay, function()
				createCrackSegment(node1, node2, false)
			end)
		end
	end
end
local function createImpactFlash(position)
	-- Create bright flash at impact point
	local flash = Instance.new("Part")
	flash.Name = "ImpactFlash"
	flash.Shape = Enum.PartType.Ball
	flash.Size = Vector3.new(0.5, 0.5, 0.5)
	flash.Color = Color3.fromRGB(255, 255, 255)
	flash.Material = Enum.Material.Neon
	flash.Transparency = 0
	flash.CanCollide = false
	flash.Anchored = true
	flash.Position = position
	flash.Parent = Workspace
	-- Bright light
	local light = Instance.new("PointLight")
	light.Color = Color3.fromRGB(255, 255, 255)
	light.Range = 20
	light.Brightness = 15
	light.Parent = flash
	-- Quick expand and fade
	local expandTween = TweenService:Create(flash, TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
		Size = Vector3.new(4, 4, 4),
		Transparency = 1,
	})
	local lightFade = TweenService:Create(light, TweenInfo.new(0.3, Enum.EasingStyle.Quad), {
		Brightness = 0,
	})
	expandTween:Play()
	lightFade:Play()
	expandTween.Completed:Connect(function()
		flash:Destroy()
	end)
end
local createDebrisObjects
function createFlyingObjects(centerPosition)
	-- Always create debris objects for guaranteed flying effect
	createDebrisObjects(centerPosition)
	-- Also try to find and affect existing objects
	local objectsInRange = {}
	-- Search for parts in workspace recursively
	local searchForParts
	searchForParts = function(parent)
		for _, obj in parent:GetChildren() do
			if obj:IsA("Part") and obj.Name ~= "Baseplate" and obj.Parent ~= Players.LocalPlayer.Character then
				local _position = obj.Position
				local _centerPosition = centerPosition
				local distance = (_position - _centerPosition).Magnitude
				if distance < 40 then
					table.insert(objectsInRange, obj)
				end
			end
			-- Search in models and folders
			if obj:IsA("Model") or obj:IsA("Folder") then
				searchForParts(obj)
			end
		end
	end
	searchForParts(Workspace)
	-- Apply gravity effects to found objects
	for _, part in objectsInRange do
		-- Skip if part is too large or important
		local _condition = part.Size.Magnitude > 20
		if not _condition then
			_condition = (string.find(string.lower(part.Name), "spawn"))
			if not (_condition ~= 0 and _condition == _condition and _condition) then
				_condition = (string.find(string.lower(part.Name), "base"))
			end
		end
		if _condition ~= 0 and _condition == _condition and _condition then
			continue
		end
		-- Temporarily unanchor if anchored
		local wasAnchored = part.Anchored
		part.Anchored = false
		-- Create BodyVelocity for flying effect
		local bodyVelocity = Instance.new("BodyVelocity")
		bodyVelocity.MaxForce = Vector3.new(12000, 12000, 12000)
		-- Calculate direction away from center with strong upward force
		local _position = part.Position
		local _centerPosition = centerPosition
		local direction = (_position - _centerPosition).Unit
		local upwardForce = Vector3.new(0, math.random(35, 70), 0)
		local _arg0 = math.random(30, 60)
		local outwardForce = direction * _arg0
		bodyVelocity.Velocity = outwardForce + upwardForce
		bodyVelocity.Parent = part
		-- Add dramatic rotation
		local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
		bodyAngularVelocity.MaxTorque = Vector3.new(4000, 4000, 4000)
		bodyAngularVelocity.AngularVelocity = Vector3.new(math.random(-8, 8), math.random(-8, 8), math.random(-8, 8))
		bodyAngularVelocity.Parent = part
		-- Remove the forces after some time
		task.delay(2.5, function()
			if bodyVelocity.Parent then
				bodyVelocity:Destroy()
			end
			if bodyAngularVelocity.Parent then
				bodyAngularVelocity:Destroy()
			end
			-- Re-anchor if it was originally anchored
			if wasAnchored then
				task.delay(4, function()
					if part.Parent then
						part.Anchored = true
					end
				end)
			end
		end)
	end
	print(`🌪️ Applied gravity effects to {#objectsInRange} existing objects + created debris`)
end
function createDebrisObjects(centerPosition)
	-- Create dramatic debris objects for guaranteed flying effect
	local numDebris = 15
	for i = 0, numDebris - 1 do
		local debris = Instance.new("Part")
		debris.Name = "QuakeDebris"
		-- Varied shapes for more interesting debris
		local shapeRandom = math.random()
		if shapeRandom < 0.4 then
			debris.Shape = Enum.PartType.Block
		elseif shapeRandom < 0.7 then
			debris.Shape = Enum.PartType.Ball
		else
			debris.Shape = Enum.PartType.Cylinder
		end
		debris.Size = Vector3.new(math.random(0.8, 3), math.random(0.8, 3), math.random(0.8, 3))
		-- Realistic debris colors (concrete, rock, metal)
		local colorType = math.random()
		if colorType < 0.5 then
			-- Concrete/stone
			debris.Color = Color3.fromRGB(math.random(80, 120), math.random(75, 115), math.random(70, 110))
			debris.Material = Enum.Material.Concrete
		elseif colorType < 0.8 then
			-- Metal
			debris.Color = Color3.fromRGB(math.random(60, 100), math.random(60, 100), math.random(60, 100))
			debris.Material = Enum.Material.Metal
		else
			-- Rock
			debris.Color = Color3.fromRGB(math.random(90, 130), math.random(85, 125), math.random(80, 120))
			debris.Material = Enum.Material.Rock
		end
		debris.CanCollide = true
		debris.Anchored = false
		debris.Parent = Workspace
		-- Position around the impact point with more spread
		local angle = (i / numDebris) * math.pi * 2 + math.random(-0.3, 0.3)
		local radius = math.random(2, 12)
		local _centerPosition = centerPosition
		local _vector3 = Vector3.new(math.cos(angle) * radius, math.random(1, 4), math.sin(angle) * radius)
		debris.Position = _centerPosition + _vector3
		-- Apply powerful initial velocity
		local bodyVelocity = Instance.new("BodyVelocity")
		bodyVelocity.MaxForce = Vector3.new(10000, 10000, 10000)
		local _position = debris.Position
		local _centerPosition_1 = centerPosition
		local direction = (_position - _centerPosition_1).Unit
		local upwardForce = Vector3.new(0, math.random(40, 80), 0)
		local _arg0 = math.random(35, 70)
		local outwardForce = direction * _arg0
		bodyVelocity.Velocity = outwardForce + upwardForce
		bodyVelocity.Parent = debris
		-- Add dramatic rotation
		local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
		bodyAngularVelocity.MaxTorque = Vector3.new(5000, 5000, 5000)
		bodyAngularVelocity.AngularVelocity = Vector3.new(math.random(-12, 12), math.random(-12, 12), math.random(-12, 12))
		bodyAngularVelocity.Parent = debris
		-- No sound effects for debris objects
		-- Remove forces after some time
		task.delay(3, function()
			if bodyVelocity.Parent then
				bodyVelocity:Destroy()
			end
			if bodyAngularVelocity.Parent then
				bodyAngularVelocity:Destroy()
			end
		end)
		-- Clean up debris after longer time
		task.delay(12 + math.random(3, 8), function()
			if debris.Parent then
				local fadeOut = TweenService:Create(debris, TweenInfo.new(3, Enum.EasingStyle.Quad), {
					Transparency = 1,
				})
				fadeOut:Play()
				fadeOut.Completed:Connect(function()
					debris:Destroy()
				end)
			end
		end)
	end
	print(`🌪️ Created {numDebris} dramatic debris objects for flying effect`)
end
local function createFlyingParticles(centerPosition)
	-- Create glass/ice particles that fly around
	local numParticles = 25
	for i = 0, numParticles - 1 do
		local particle = Instance.new("Part")
		particle.Name = `GlassParticle_{i}`
		particle.Shape = Enum.PartType.Block
		particle.Size = Vector3.new(math.random(0.1, 0.3), math.random(0.1, 0.3), math.random(0.1, 0.3))
		particle.Color = Color3.fromRGB(240, 250, 255)
		particle.Material = Enum.Material.Glass
		particle.Transparency = 0.2
		particle.CanCollide = false
		particle.Anchored = false
		particle.Parent = Workspace
		-- Position around the impact point
		local angle = (i / numParticles) * math.pi * 2
		local radius = math.random(2, 5)
		local _centerPosition = centerPosition
		local _vector3 = Vector3.new(math.cos(angle) * radius, math.random(-1, 3), math.sin(angle) * radius)
		local startPos = _centerPosition + _vector3
		particle.Position = startPos
		-- Add subtle glow
		local light = Instance.new("PointLight")
		light.Color = Color3.fromRGB(200, 220, 255)
		light.Range = 1
		light.Brightness = 0.5
		light.Parent = particle
		-- Apply initial velocity
		local bodyVelocity = Instance.new("BodyVelocity")
		bodyVelocity.MaxForce = Vector3.new(2000, 2000, 2000)
		-- Random direction with upward bias
		local direction = Vector3.new(math.random(-1, 1), math.random(0.5, 1.5), math.random(-1, 1)).Unit
		local _arg0 = math.random(10, 25)
		bodyVelocity.Velocity = direction * _arg0
		bodyVelocity.Parent = particle
		-- Add rotation
		local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
		bodyAngularVelocity.MaxTorque = Vector3.new(500, 500, 500)
		bodyAngularVelocity.AngularVelocity = Vector3.new(math.random(-10, 10), math.random(-10, 10), math.random(-10, 10))
		bodyAngularVelocity.Parent = particle
		-- Delayed appearance
		particle.Transparency = 1
		task.delay(math.random(0.1, 0.3), function()
			local appearTween = TweenService:Create(particle, TweenInfo.new(0.2, Enum.EasingStyle.Quad), {
				Transparency = 0.2,
			})
			appearTween:Play()
		end)
		-- Remove forces after some time and let gravity take over
		task.delay(1, function()
			if bodyVelocity.Parent then
				bodyVelocity:Destroy()
			end
			if bodyAngularVelocity.Parent then
				bodyAngularVelocity:Destroy()
			end
		end)
		-- Fade out and destroy
		task.delay(3 + math.random(1, 3), function()
			local fadeOut = TweenService:Create(particle, TweenInfo.new(2, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				particle:Destroy()
			end)
		end)
	end
	print(`✨ Created {numParticles} flying glass particles`)
end
local createCrackSparkles, createEnergyMotes, createShimmeringDust, createCrackGlitter, createFloatingSparkles
function createCrackSparklingEffects(position, direction)
	print(`✨ Creating spectacular sparkling effects for cracks at {position}`)
	-- Create multiple types of sparkling effects
	createCrackSparkles(position)
	createEnergyMotes(position, direction)
	createShimmeringDust(position)
	createCrackGlitter(position, direction)
	createFloatingSparkles(position)
	print("✅ Crack sparkling effects created")
end
function createCrackSparkles(position)
	-- Create tiny sparkling particles that emanate from crack lines
	local numSparkles = 50
	for i = 0, numSparkles - 1 do
		local sparkle = Instance.new("Part")
		sparkle.Name = `CrackSparkle_{i}`
		sparkle.Shape = Enum.PartType.Ball
		sparkle.Size = Vector3.new(0.05, 0.05, 0.05)
		sparkle.Color = Color3.fromRGB(255, 255, 255)
		sparkle.Material = Enum.Material.Neon
		sparkle.Transparency = 0
		sparkle.CanCollide = false
		sparkle.Anchored = true
		sparkle.Parent = Workspace
		-- Position sparkles along crack lines with random distribution
		local angle = math.random(0, math.pi * 2)
		local radius = math.random(1, 12)
		local height = math.random(-2, 4)
		local _position = position
		local _vector3 = Vector3.new(math.cos(angle) * radius, height, math.sin(angle) * radius)
		sparkle.Position = _position + _vector3
		-- Bright sparkle light
		local light = Instance.new("PointLight")
		light.Color = Color3.fromRGB(255, 255, 255)
		light.Range = 0.5
		light.Brightness = 5
		light.Parent = sparkle
		-- Twinkling animation - sparkles appear and disappear randomly
		local twinkleDelay = math.random(0, 2)
		local twinkleDuration = math.random(0.1, 0.3)
		task.delay(twinkleDelay, function()
			-- Flash bright
			local flashTween = TweenService:Create(sparkle, TweenInfo.new(twinkleDuration * 0.3, Enum.EasingStyle.Quad), {
				Transparency = 0,
				Size = Vector3.new(0.1, 0.1, 0.1),
			})
			flashTween:Play()
			-- Then fade out
			flashTween.Completed:Connect(function()
				local fadeOut = TweenService:Create(sparkle, TweenInfo.new(twinkleDuration * 0.7, Enum.EasingStyle.Quad), {
					Transparency = 1,
					Size = Vector3.new(0.02, 0.02, 0.02),
				})
				fadeOut:Play()
				fadeOut.Completed:Connect(function()
					return sparkle:Destroy()
				end)
			end)
		end)
	end
end
function createEnergyMotes(position, direction)
	-- Create floating energy motes that drift around the crack area
	local numMotes = 25
	for i = 0, numMotes - 1 do
		local mote = Instance.new("Part")
		mote.Name = `EnergyMote_{i}`
		mote.Shape = Enum.PartType.Ball
		mote.Size = Vector3.new(0.15, 0.15, 0.15)
		mote.Color = Color3.fromRGB(200, 230, 255)
		mote.Material = Enum.Material.Neon
		mote.Transparency = 0.3
		mote.CanCollide = false
		mote.Anchored = true
		mote.Parent = Workspace
		-- Position around the crack area
		local angle = math.random(0, math.pi * 2)
		local radius = math.random(3, 10)
		local _position = position
		local _vector3 = Vector3.new(math.cos(angle) * radius, math.random(-1, 3), math.sin(angle) * radius)
		local startPos = _position + _vector3
		mote.Position = startPos
		-- Soft glow
		local light = Instance.new("PointLight")
		light.Color = Color3.fromRGB(150, 200, 255)
		light.Range = 2
		light.Brightness = 1
		light.Parent = mote
		-- Gentle floating motion
		local floatDirection = Vector3.new(math.random(-1, 1), math.random(0.5, 1.5), math.random(-1, 1)).Unit
		local _arg0 = math.random(5, 15)
		local _arg0_1 = floatDirection * _arg0
		local endPos = startPos + _arg0_1
		local floatTween = TweenService:Create(mote, TweenInfo.new(math.random(3, 6), Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
			Position = endPos,
		})
		floatTween:Play()
		-- Gentle pulsing
		local pulseTween = TweenService:Create(mote, TweenInfo.new(math.random(1, 2), Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
			Transparency = 0.6,
			Size = Vector3.new(0.2, 0.2, 0.2),
		})
		pulseTween:Play()
		-- Fade out after time
		task.delay(4 + math.random(2, 4), function()
			pulseTween:Cancel()
			local fadeOut = TweenService:Create(mote, TweenInfo.new(2, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				return mote:Destroy()
			end)
		end)
	end
end
function createShimmeringDust(position)
	-- Create shimmering dust particles that float around the cracks
	local numDustParticles = 30
	for i = 0, numDustParticles - 1 do
		local dust = Instance.new("Part")
		dust.Name = `ShimmeringDust_{i}`
		dust.Shape = Enum.PartType.Ball
		dust.Size = Vector3.new(0.08, 0.08, 0.08)
		dust.Color = Color3.fromRGB(255, 245, 200)
		dust.Material = Enum.Material.Neon
		dust.Transparency = 0.4
		dust.CanCollide = false
		dust.Anchored = true
		dust.Parent = Workspace
		-- Position dust particles around crack area
		local angle = math.random(0, math.pi * 2)
		local radius = math.random(2, 8)
		local height = math.random(-1, 5)
		local _position = position
		local _vector3 = Vector3.new(math.cos(angle) * radius, height, math.sin(angle) * radius)
		dust.Position = _position + _vector3
		-- Gentle shimmer light
		local light = Instance.new("PointLight")
		light.Color = Color3.fromRGB(255, 220, 150)
		light.Range = 1
		light.Brightness = 0.8
		light.Parent = dust
		-- Slow floating motion with shimmer
		local _exp = TweenInfo.new(math.random(4, 8), Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
		local _object = {}
		local _left = "Position"
		local _position_1 = dust.Position
		local _vector3_1 = Vector3.new(math.random(-3, 3), math.random(2, 6), math.random(-3, 3))
		_object[_left] = _position_1 + _vector3_1
		local floatTween = TweenService:Create(dust, _exp, _object)
		floatTween:Play()
		-- Shimmer effect - transparency changes
		local shimmerTween = TweenService:Create(dust, TweenInfo.new(math.random(0.5, 1.5), Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
			Transparency = 0.8,
		})
		shimmerTween:Play()
		-- Fade out
		task.delay(5 + math.random(2, 4), function()
			shimmerTween:Cancel()
			local fadeOut = TweenService:Create(dust, TweenInfo.new(2, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				return dust:Destroy()
			end)
		end)
	end
end
function createCrackGlitter(position, direction)
	-- Create glittering particles that follow the crack lines
	local numGlitter = 40
	for i = 0, numGlitter - 1 do
		local glitter = Instance.new("Part")
		glitter.Name = `CrackGlitter_{i}`
		glitter.Shape = Enum.PartType.Block
		glitter.Size = Vector3.new(0.03, 0.03, 0.03)
		glitter.Color = Color3.fromRGB(255, 255, 255)
		glitter.Material = Enum.Material.Neon
		glitter.Transparency = 0
		glitter.CanCollide = false
		glitter.Anchored = true
		glitter.Parent = Workspace
		-- Position along crack lines
		local angle = (i / numGlitter) * math.pi * 2
		local radius = math.random(1, 15)
		local deviation = math.random(-0.5, 0.5)
		local _position = position
		local _vector3 = Vector3.new(math.cos(angle + deviation) * radius, math.random(-2, 4), math.sin(angle + deviation) * radius)
		glitter.Position = _position + _vector3
		-- Random rotation for glitter effect
		local _cFrame = glitter.CFrame
		local _arg0 = CFrame.Angles(math.random(0, math.pi * 2), math.random(0, math.pi * 2), math.random(0, math.pi * 2))
		glitter.CFrame = _cFrame * _arg0
		-- Intense sparkle light
		local light = Instance.new("PointLight")
		light.Color = Color3.fromRGB(255, 255, 255)
		light.Range = 0.3
		light.Brightness = 8
		light.Parent = glitter
		-- Quick sparkle animation
		local sparkleDelay = math.random(0, 1.5)
		task.delay(sparkleDelay, function()
			-- Quick flash
			local flashTween = TweenService:Create(glitter, TweenInfo.new(0.05, Enum.EasingStyle.Quad), {
				Size = Vector3.new(0.08, 0.08, 0.08),
				Transparency = 0,
			})
			flashTween:Play()
			-- Quick fade
			flashTween.Completed:Connect(function()
				local fadeOut = TweenService:Create(glitter, TweenInfo.new(0.2, Enum.EasingStyle.Quad), {
					Transparency = 1,
					Size = Vector3.new(0.01, 0.01, 0.01),
				})
				fadeOut:Play()
				fadeOut.Completed:Connect(function()
					return glitter:Destroy()
				end)
			end)
		end)
	end
end
function createFloatingSparkles(position)
	-- Create floating sparkles that rise up from the cracks
	local numSparkles = 20
	for i = 0, numSparkles - 1 do
		local sparkle = Instance.new("Part")
		sparkle.Name = `FloatingSparkle_{i}`
		sparkle.Shape = Enum.PartType.Ball
		sparkle.Size = Vector3.new(0.06, 0.06, 0.06)
		sparkle.Color = Color3.fromRGB(255, 255, 255)
		sparkle.Material = Enum.Material.Neon
		sparkle.Transparency = 0.2
		sparkle.CanCollide = false
		sparkle.Anchored = true
		sparkle.Parent = Workspace
		-- Start position near cracks
		local angle = math.random(0, math.pi * 2)
		local radius = math.random(1, 8)
		local _position = position
		local _vector3 = Vector3.new(math.cos(angle) * radius, math.random(-1, 1), math.sin(angle) * radius)
		local startPos = _position + _vector3
		sparkle.Position = startPos
		-- Bright sparkle light
		local light = Instance.new("PointLight")
		light.Color = Color3.fromRGB(255, 255, 255)
		light.Range = 1
		light.Brightness = 3
		light.Parent = sparkle
		-- Float upward with gentle swaying
		local _vector3_1 = Vector3.new(math.random(-2, 2), math.random(8, 15), math.random(-2, 2))
		local endPos = startPos + _vector3_1
		local floatTween = TweenService:Create(sparkle, TweenInfo.new(math.random(3, 5), Enum.EasingStyle.Sine, Enum.EasingDirection.Out), {
			Position = endPos,
		})
		floatTween:Play()
		-- Gentle twinkling
		local twinkleTween = TweenService:Create(sparkle, TweenInfo.new(math.random(0.3, 0.8), Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
			Transparency = 0.6,
			Size = Vector3.new(0.1, 0.1, 0.1),
		})
		twinkleTween:Play()
		-- Fade out as it rises
		task.delay(2, function()
			local fadeOut = TweenService:Create(sparkle, TweenInfo.new(3, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				return sparkle:Destroy()
			end)
		end)
	end
end
local function createGlassShards(centerPosition)
	-- Create beautiful glass shards that fall from the cracks
	local numShards = 15
	for i = 0, numShards - 1 do
		local shard = Instance.new("Part")
		shard.Name = `GlassShard_{i}`
		shard.Shape = Enum.PartType.Block
		shard.Size = Vector3.new(math.random(0.2, 0.6), math.random(0.2, 0.6), math.random(0.05, 0.15))
		shard.Color = Color3.fromRGB(245, 250, 255)
		shard.Material = Enum.Material.Glass
		shard.Transparency = 0.1
		shard.CanCollide = false
		shard.Anchored = false
		shard.Parent = Workspace
		-- Position around the impact point
		local angle = (i / numShards) * math.pi * 2
		local radius = math.random(3, 8)
		local _centerPosition = centerPosition
		local _vector3 = Vector3.new(math.cos(angle) * radius, math.random(2, 6), math.sin(angle) * radius)
		shard.Position = _centerPosition + _vector3
		-- Add subtle glow
		local light = Instance.new("PointLight")
		light.Color = Color3.fromRGB(200, 220, 255)
		light.Range = 1.5
		light.Brightness = 0.8
		light.Parent = shard
		-- Apply gentle falling motion
		local bodyVelocity = Instance.new("BodyVelocity")
		bodyVelocity.MaxForce = Vector3.new(1000, 1000, 1000)
		bodyVelocity.Velocity = Vector3.new(math.random(-5, 5), math.random(-10, -5), math.random(-5, 5))
		bodyVelocity.Parent = shard
		-- Add gentle rotation
		local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
		bodyAngularVelocity.MaxTorque = Vector3.new(200, 200, 200)
		bodyAngularVelocity.AngularVelocity = Vector3.new(math.random(-2, 2), math.random(-2, 2), math.random(-2, 2))
		bodyAngularVelocity.Parent = shard
		-- Remove forces after some time
		task.delay(1.5, function()
			if bodyVelocity.Parent then
				bodyVelocity:Destroy()
			end
			if bodyAngularVelocity.Parent then
				bodyAngularVelocity:Destroy()
			end
		end)
		-- Fade out and destroy
		task.delay(4 + math.random(2, 4), function()
			local fadeOut = TweenService:Create(shard, TweenInfo.new(2, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				shard:Destroy()
			end)
		end)
	end
	print(`💎 Created {numShards} falling glass shards`)
end
local function createEnhancedShockwave(position)
	-- Create multiple shockwave rings for layered effect
	for ring = 0, 2 do
		local shockwave = Instance.new("Part")
		shockwave.Name = `EnhancedShockwave_{ring}`
		shockwave.Shape = Enum.PartType.Cylinder
		shockwave.Size = Vector3.new(0.2, 1, 1)
		shockwave.Color = if ring == 0 then Color3.fromRGB(255, 255, 255) else Color3.fromRGB(240, 248, 255)
		shockwave.Material = Enum.Material.ForceField
		shockwave.Transparency = 0.1 + (ring * 0.2)
		shockwave.CanCollide = false
		shockwave.Anchored = true
		local _cFrame = CFrame.new(position)
		local _arg0 = CFrame.Angles(0, 0, math.rad(90))
		shockwave.CFrame = _cFrame * _arg0
		shockwave.Parent = Workspace
		-- Add intense light only to first ring
		if ring == 0 then
			local light = Instance.new("PointLight")
			light.Color = Color3.fromRGB(255, 255, 255)
			light.Range = 40
			light.Brightness = 10
			light.Parent = shockwave
			-- Flash effect
			local flashTween = TweenService:Create(light, TweenInfo.new(0.3, Enum.EasingStyle.Quad), {
				Brightness = 3,
			})
			flashTween:Play()
		end
		-- Staggered expansion for wave effect
		local delay = ring * 0.1
		local maxSize = 80 - (ring * 10)
		task.delay(delay, function()
			local expandTween = TweenService:Create(shockwave, TweenInfo.new(2.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				Size = Vector3.new(0.2, maxSize, maxSize),
				Transparency = 1,
			})
			expandTween:Play()
			expandTween.Completed:Connect(function()
				return shockwave:Destroy()
			end)
		end)
	end
	-- No ground impact - only air cracks
end
local createImpactCenter, createRadiatingCracks, createCrackWeb
local function createRealisticAirFractures(centerPos, direction)
	-- Create impact point in front of punch
	local _centerPos = centerPos
	local _arg0 = direction * 8
	local impactPoint = _centerPos + _arg0
	-- Create central impact shape (like broken glass center)
	createImpactCenter(impactPoint)
	-- Create radiating cracks from impact point (like glass fracture pattern)
	createRadiatingCracks(impactPoint, direction)
	-- Create secondary web pattern
	createCrackWeb(impactPoint, direction)
end
function createImpactCenter(impactPoint)
	-- Create the main "hole" in the air - like glass was punched through
	local airHole = Instance.new("Part")
	airHole.Name = "AirHole"
	airHole.Shape = Enum.PartType.Ball
	airHole.Size = Vector3.new(2, 2, 2)
	airHole.Color = Color3.fromRGB(0, 0, 0)
	airHole.Material = Enum.Material.ForceField
	airHole.Transparency = 0.8
	airHole.CanCollide = false
	airHole.Anchored = true
	airHole.Position = impactPoint
	airHole.Parent = Workspace
	-- Create bright white rim around the hole
	local rim = Instance.new("Part")
	rim.Name = "ImpactRim"
	rim.Shape = Enum.PartType.Ball
	rim.Size = Vector3.new(2.2, 2.2, 2.2)
	rim.Color = Color3.fromRGB(255, 255, 255)
	rim.Material = Enum.Material.Neon
	rim.Transparency = 0.1
	rim.CanCollide = false
	rim.Anchored = true
	rim.Position = impactPoint
	rim.Parent = Workspace
	-- Add intense light at impact center
	local light = Instance.new("PointLight")
	light.Color = Color3.fromRGB(255, 255, 255)
	light.Range = 25
	light.Brightness = 10
	light.Parent = rim
	-- Create glass fragments floating around the break
	for i = 0, 7 do
		local fragment = Instance.new("Part")
		fragment.Name = `GlassFragment_{i}`
		fragment.Shape = Enum.PartType.Block
		fragment.Size = Vector3.new(math.random(0.3, 0.8), math.random(0.3, 0.8), math.random(0.05, 0.1))
		fragment.Color = Color3.fromRGB(240, 248, 255)
		fragment.Material = Enum.Material.Glass
		fragment.Transparency = 0.2
		fragment.CanCollide = false
		fragment.Anchored = true
		fragment.Parent = Workspace
		-- Position fragments around the hole
		local angle = (i / 8) * math.pi * 2 + math.random(-0.3, 0.3)
		local distance = math.random(1.5, 3)
		local _impactPoint = impactPoint
		local _vector3 = Vector3.new(math.cos(angle) * distance, math.random(-1, 1), math.sin(angle) * distance)
		local fragmentPos = _impactPoint + _vector3
		-- Orient fragments randomly like broken glass pieces
		local _cFrame = CFrame.new(fragmentPos)
		local _arg0 = CFrame.Angles(math.random(-math.pi, math.pi), math.random(-math.pi, math.pi), math.random(-math.pi, math.pi))
		fragment.CFrame = _cFrame * _arg0
	end
	-- Flash appearance effect
	airHole.Transparency = 1
	rim.Transparency = 1
	local holeFlash = TweenService:Create(airHole, TweenInfo.new(0.15, Enum.EasingStyle.Quad), {
		Transparency = 0.8,
	})
	local rimFlash = TweenService:Create(rim, TweenInfo.new(0.1, Enum.EasingStyle.Quad), {
		Transparency = 0.1,
	})
	holeFlash:Play()
	rimFlash:Play()
	-- Disappear after time
	task.delay(6, function()
		local holeFade = TweenService:Create(airHole, TweenInfo.new(2, Enum.EasingStyle.Quad), {
			Transparency = 1,
		})
		local rimFade = TweenService:Create(rim, TweenInfo.new(2, Enum.EasingStyle.Quad), {
			Transparency = 1,
		})
		holeFade:Play()
		rimFade:Play()
		holeFade.Completed:Connect(function()
			airHole:Destroy()
			rim:Destroy()
		end)
	end)
end
function createRadiatingCracks(impactPoint, direction)
	-- Create main glass fracture lines radiating from the break point
	for i = 0, 15 do
		local angle = (i / 16) * math.pi * 2
		local rightVector = direction:Cross(Vector3.new(0, 1, 0)).Unit
		local upVector = Vector3.new(0, 1, 0)
		-- Calculate crack direction with more variation
		local _arg0 = math.cos(angle)
		local _exp = rightVector * _arg0
		local _arg0_1 = math.sin(angle)
		local _arg0_2 = upVector * _arg0_1
		local _direction = direction
		local _arg0_3 = math.random(0.1, 0.5)
		local crackDirection = _exp + _arg0_2 + (_direction * _arg0_3)
	end
end
local createGlassDust
function createCrackWeb(impactPoint, direction)
	-- Create secondary glass fractures and connecting patterns
	for i = 0, 15 do
		local webCrack = Instance.new("Part")
		webCrack.Name = `GlassWeb_{i}`
		webCrack.Shape = Enum.PartType.Block
		local length = math.random(6, 15)
		webCrack.Size = Vector3.new(0.02, length, 0.02)
		webCrack.Color = Color3.fromRGB(245, 250, 255)
		webCrack.Material = Enum.Material.Glass
		webCrack.Transparency = 0.4
		webCrack.CanCollide = false
		webCrack.Anchored = true
		webCrack.Parent = Workspace
		-- Position web cracks in realistic glass pattern
		local angle = math.random(0, math.pi * 2)
		local distance = math.random(4, 18)
		local rightVector = direction:Cross(Vector3.new(0, 1, 0)).Unit
		local upVector = Vector3.new(0, 1, 0)
		local _impactPoint = impactPoint
		local _arg0 = math.cos(angle) * distance
		local _arg0_1 = rightVector * _arg0
		local _arg0_2 = math.sin(angle) * distance * 0.8
		local _arg0_3 = upVector * _arg0_2
		local _direction = direction
		local _arg0_4 = math.random(-2, 4)
		local webPos = _impactPoint + _arg0_1 + _arg0_3 + (_direction * _arg0_4)
		-- Orient towards impact point for realistic glass web
		local towardCenter = (impactPoint - webPos).Unit
		local randomVariation = Vector3.new(math.random(-0.6, 0.6), math.random(-0.4, 0.4), math.random(-0.6, 0.6))
		local webDirection = (towardCenter + randomVariation).Unit
		webCrack.CFrame = CFrame.lookAt(webPos, webPos + webDirection)
		-- Delayed appearance after main cracks
		webCrack.Transparency = 1
		task.delay(math.random(0.3, 0.8), function()
			local appearTween = TweenService:Create(webCrack, TweenInfo.new(0.12, Enum.EasingStyle.Quad), {
				Transparency = 0.4,
			})
			appearTween:Play()
		end)
		-- Create small glass dust particles around some web cracks
		if i % 3 == 0 then
			createGlassDust(webPos)
		end
		-- Disappear
		task.delay(6, function()
			local fadeOut = TweenService:Create(webCrack, TweenInfo.new(2, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				return webCrack:Destroy()
			end)
		end)
	end
end
function createGlassDust(position)
	-- Create tiny glass dust particles for extra realism
	for i = 0, 3 do
		local dust = Instance.new("Part")
		dust.Name = `GlassDust_{i}`
		dust.Shape = Enum.PartType.Ball
		dust.Size = Vector3.new(0.1, 0.1, 0.1)
		dust.Color = Color3.fromRGB(255, 255, 255)
		dust.Material = Enum.Material.Glass
		dust.Transparency = 0.6
		dust.CanCollide = false
		dust.Anchored = true
		dust.Parent = Workspace
		local _position = position
		local _vector3 = Vector3.new(math.random(-0.8, 0.8), math.random(-0.8, 0.8), math.random(-0.8, 0.8))
		dust.Position = _position + _vector3
		-- Delayed appearance
		dust.Transparency = 1
		task.delay(math.random(0.4, 0.9), function()
			local appearTween = TweenService:Create(dust, TweenInfo.new(0.2, Enum.EasingStyle.Quad), {
				Transparency = 0.6,
			})
			appearTween:Play()
		end)
		-- Quick disappear
		task.delay(3, function()
			local fadeOut = TweenService:Create(dust, TweenInfo.new(1, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				return dust:Destroy()
			end)
		end)
	end
end
local function createMassiveImpactFlash(position)
	-- Create an absolutely massive impact flash that lights up everything
	local flash = Instance.new("Part")
	flash.Name = "MassiveImpactFlash"
	flash.Shape = Enum.PartType.Ball
	flash.Size = Vector3.new(3, 3, 3)
	flash.Color = Color3.fromRGB(255, 255, 255)
	flash.Material = Enum.Material.Neon
	flash.Transparency = 0
	flash.CanCollide = false
	flash.Anchored = true
	flash.Position = position
	flash.Parent = Workspace
	-- MASSIVE light that illuminates everything
	local light = Instance.new("PointLight")
	light.Color = Color3.fromRGB(255, 255, 255)
	light.Range = 100
	light.Brightness = 50
	light.Parent = flash
	-- Multiple expanding rings for layered effect
	for ring = 0, 4 do
		local ringFlash = Instance.new("Part")
		ringFlash.Name = `FlashRing_{ring}`
		ringFlash.Shape = Enum.PartType.Ball
		ringFlash.Size = Vector3.new(2 + ring, 2 + ring, 2 + ring)
		ringFlash.Color = Color3.fromRGB(255, 255, 255)
		ringFlash.Material = Enum.Material.ForceField
		ringFlash.Transparency = 0.3 + (ring * 0.1)
		ringFlash.CanCollide = false
		ringFlash.Anchored = true
		ringFlash.Position = position
		ringFlash.Parent = Workspace
		-- Staggered expansion
		local delay = ring * 0.05
		task.delay(delay, function()
			local expandTween = TweenService:Create(ringFlash, TweenInfo.new(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				Size = Vector3.new(25 + ring * 5, 25 + ring * 5, 25 + ring * 5),
				Transparency = 1,
			})
			expandTween:Play()
			expandTween.Completed:Connect(function()
				return ringFlash:Destroy()
			end)
		end)
	end
	-- Main flash expansion and fade
	local expandTween = TweenService:Create(flash, TweenInfo.new(0.6, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
		Size = Vector3.new(20, 20, 20),
		Transparency = 1,
	})
	local lightFade = TweenService:Create(light, TweenInfo.new(0.6, Enum.EasingStyle.Quad), {
		Brightness = 0,
	})
	expandTween:Play()
	lightFade:Play()
	expandTween.Completed:Connect(function()
		flash:Destroy()
	end)
	print("💥 MASSIVE impact flash created!")
end
local function createFloatingDebris(position, direction)
	for i = 0, 11 do
		local debris = Instance.new("Part")
		debris.Name = "Debris"
		debris.Shape = Enum.PartType.Block
		debris.Size = Vector3.new(math.random(0.5, 2), math.random(0.5, 2), math.random(0.5, 2))
		debris.Color = Color3.fromRGB(150, 150, 150)
		debris.Material = Enum.Material.Concrete
		debris.CanCollide = false
		debris.Parent = Workspace
		-- Position debris around impact
		local _position = position
		local _vector3 = Vector3.new(math.random(-8, 8), math.random(0, 5), math.random(-8, 8))
		local debrisPos = _position + _vector3
		debris.Position = debrisPos
		-- Add physics for realistic movement
		local bodyVelocity = Instance.new("BodyVelocity")
		bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
		local _direction = direction
		local _arg0 = math.random(15, 40)
		local _vector3_1 = Vector3.new(math.random(-15, 15), math.random(10, 30), math.random(-15, 15))
		bodyVelocity.Velocity = _direction * _arg0 + _vector3_1
		bodyVelocity.Parent = debris
		-- Add rotation
		local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
		bodyAngularVelocity.MaxTorque = Vector3.new(4000, 4000, 4000)
		bodyAngularVelocity.AngularVelocity = Vector3.new(math.random(-10, 10), math.random(-10, 10), math.random(-10, 10))
		bodyAngularVelocity.Parent = debris
		-- Remove debris after some time
		task.delay(4, function()
			if bodyVelocity.Parent then
				bodyVelocity:Destroy()
			end
			if bodyAngularVelocity.Parent then
				bodyAngularVelocity:Destroy()
			end
			local fadeTween = TweenService:Create(debris, TweenInfo.new(2, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeTween:Play()
			fadeTween.Completed:Connect(function()
				return debris:Destroy()
			end)
		end)
	end
end
local function createDimensionalCracks(position)
	-- Create large dimensional crack effects
	for i = 0, 5 do
		local crack = Instance.new("Part")
		crack.Name = "DimensionalCrack"
		crack.Shape = Enum.PartType.Block
		crack.Size = Vector3.new(0.1, math.random(15, 25), 0.1)
		crack.Color = Color3.fromRGB(255, 0, 255)
		crack.Material = Enum.Material.Neon
		crack.Transparency = 0.2
		crack.CanCollide = false
		crack.Anchored = true
		crack.Parent = Workspace
		local angle = (i / 6) * math.pi * 2
		local distance = math.random(3, 8)
		local _position = position
		local _vector3 = Vector3.new(math.cos(angle) * distance, math.random(-5, 5), math.sin(angle) * distance)
		local crackPos = _position + _vector3
		local _cFrame = CFrame.new(crackPos)
		local _arg0 = CFrame.Angles(math.random(-0.5, 0.5), angle, math.random(-0.3, 0.3))
		crack.CFrame = _cFrame * _arg0
		-- Fade out after delay
		task.delay(5, function()
			local fadeOut = TweenService:Create(crack, TweenInfo.new(2), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				return crack:Destroy()
			end)
		end)
	end
end
local function createAtmosphericDistortion(position, direction)
	-- Create atmospheric distortion waves
	for i = 0, 3 do
		local wave = Instance.new("Part")
		wave.Name = "AtmosphericWave"
		wave.Shape = Enum.PartType.Ball
		wave.Size = Vector3.new(1, 1, 1)
		wave.Color = Color3.fromRGB(200, 200, 255)
		wave.Material = Enum.Material.ForceField
		wave.Transparency = 0.8
		wave.CanCollide = false
		wave.Anchored = true
		wave.Parent = Workspace
		wave.Position = position
		local expandTween = TweenService:Create(wave, TweenInfo.new(1.5 + i * 0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
			Size = Vector3.new(20, 20, 20),
			Transparency = 1,
		})
		expandTween:Play()
		expandTween.Completed:Connect(function()
			return wave:Destroy()
		end)
	end
end
function createEnergyLightning(position)
	-- Create reduced energy lightning effects (less overwhelming)
	for i = 0, 2 do
		local lightning = Instance.new("Part")
		lightning.Name = "EnergyLightning"
		lightning.Shape = Enum.PartType.Block
		lightning.Size = Vector3.new(0.15, math.random(6, 10), 0.15)
		lightning.Color = Color3.fromRGB(255, 255, 150)
		lightning.Material = Enum.Material.Neon
		lightning.Transparency = 0.3
		lightning.CanCollide = false
		lightning.Anchored = true
		lightning.Parent = Workspace
		local _position = position
		local _vector3 = Vector3.new(math.random(-6, 6), math.random(3, 8), math.random(-6, 6))
		local lightningPos = _position + _vector3
		lightning.Position = lightningPos
		-- Reduced flicker effect
		for flicker = 0, 2 do
			task.delay(flicker * 0.15, function()
				lightning.Transparency = math.random(0.3, 0.7)
			end)
		end
		task.delay(1.5, function()
			return lightning:Destroy()
		end)
	end
end
local function createEnergyParticleStorm(position)
	-- Create energy particle storm
	for i = 0, 19 do
		local particle = Instance.new("Part")
		particle.Name = "EnergyParticle"
		particle.Shape = Enum.PartType.Ball
		particle.Size = Vector3.new(0.5, 0.5, 0.5)
		particle.Color = Color3.fromRGB(255, 150, 0)
		particle.Material = Enum.Material.Neon
		particle.Transparency = 0.3
		particle.CanCollide = false
		particle.Anchored = true
		particle.Parent = Workspace
		local _position = position
		local _vector3 = Vector3.new(math.random(-15, 15), math.random(-5, 10), math.random(-15, 15))
		local particlePos = _position + _vector3
		particle.Position = particlePos
		-- Floating animation
		local _exp = TweenInfo.new(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
		local _object = {}
		local _left = "Position"
		local _vector3_1 = Vector3.new(0, math.random(5, 10), 0)
		_object[_left] = particlePos + _vector3_1
		local floatTween = TweenService:Create(particle, _exp, _object)
		floatTween:Play()
		task.delay(4, function()
			local fadeOut = TweenService:Create(particle, TweenInfo.new(1), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				return particle:Destroy()
			end)
		end)
	end
end
local function createRealityShatter(position, direction)
	-- Create reality shatter effect
	local shatter = Instance.new("Part")
	shatter.Name = "RealityShatter"
	shatter.Shape = Enum.PartType.Block
	shatter.Size = Vector3.new(15, 15, 0.1)
	shatter.Color = Color3.fromRGB(0, 0, 0)
	shatter.Material = Enum.Material.Neon
	shatter.Transparency = 0.5
	shatter.CanCollide = false
	shatter.Anchored = true
	shatter.Parent = Workspace
	local _position = position
	local _arg0 = direction * 5
	shatter.Position = _position + _arg0
	-- Create shatter pattern
	for i = 0, 11 do
		local fragment = Instance.new("Part")
		fragment.Name = "ShatterFragment"
		fragment.Shape = Enum.PartType.Block
		fragment.Size = Vector3.new(math.random(1, 3), math.random(1, 3), 0.1)
		fragment.Color = Color3.fromRGB(100, 0, 100)
		fragment.Material = Enum.Material.Neon
		fragment.Transparency = 0.3
		fragment.CanCollide = false
		fragment.Anchored = true
		fragment.Parent = Workspace
		local _position_1 = position
		local _vector3 = Vector3.new(math.random(-8, 8), math.random(-8, 8), math.random(-2, 2))
		local fragmentPos = _position_1 + _vector3
		fragment.Position = fragmentPos
		task.delay(3, function()
			local fadeOut = TweenService:Create(fragment, TweenInfo.new(2), {
				Transparency = 1,
			})
			fadeOut:Play()
			fadeOut.Completed:Connect(function()
				return fragment:Destroy()
			end)
		end)
	end
	task.delay(4, function()
		local fadeOut = TweenService:Create(shatter, TweenInfo.new(2), {
			Transparency = 1,
		})
		fadeOut:Play()
		fadeOut.Completed:Connect(function()
			return shatter:Destroy()
		end)
	end)
end
return {
	createAirCracks = createAirCracks,
	createEnhancedAirCracks = createEnhancedAirCracks,
	createEnhancedGlassCracks = createEnhancedGlassCracks,
	createGlassImpactCenter = createGlassImpactCenter,
	createMainGlassCracks = createMainGlassCracks,
	createEnhancedGlassCrackLine = createEnhancedGlassCrackLine,
	createEnhancedGlassWebPattern = createEnhancedGlassWebPattern,
	createGlassFragments = createGlassFragments,
	createRealisticGlassBreaking = createRealisticGlassBreaking,
	createIceImpactCrater = createIceImpactCrater,
	createExpandingIceCracks = createExpandingIceCracks,
	createVerticalIceCrack = createVerticalIceCrack,
	createIceCrackLine = createIceCrackLine,
	createIceCrystals = createIceCrystals,
	createIceShards = createIceShards,
	createGlassCrackLine = createGlassCrackLine,
	createGlassWebConnections = createGlassWebConnections,
	createGlassWebSegment = createGlassWebSegment,
	createGlassImpactCrater = createGlassImpactCrater,
	animateCrackLine = animateCrackLine,
	createCrackSegment = createCrackSegment,
	createGlassWebPattern = createGlassWebPattern,
	createImpactFlash = createImpactFlash,
	createFlyingObjects = createFlyingObjects,
	createDebrisObjects = createDebrisObjects,
	createFlyingParticles = createFlyingParticles,
	createCrackSparklingEffects = createCrackSparklingEffects,
	createCrackSparkles = createCrackSparkles,
	createEnergyMotes = createEnergyMotes,
	createShimmeringDust = createShimmeringDust,
	createCrackGlitter = createCrackGlitter,
	createFloatingSparkles = createFloatingSparkles,
	createGlassShards = createGlassShards,
	createEnhancedShockwave = createEnhancedShockwave,
	createRealisticAirFractures = createRealisticAirFractures,
	createImpactCenter = createImpactCenter,
	createRadiatingCracks = createRadiatingCracks,
	createCrackWeb = createCrackWeb,
	createGlassDust = createGlassDust,
	createMassiveImpactFlash = createMassiveImpactFlash,
	createFloatingDebris = createFloatingDebris,
	createDimensionalCracks = createDimensionalCracks,
	createAtmosphericDistortion = createAtmosphericDistortion,
	createEnergyLightning = createEnergyLightning,
	createEnergyParticleStorm = createEnergyParticleStorm,
	createRealityShatter = createRealityShatter,
}
