import * as React from "@rbxts/react";
import { ResponsiveManager } from "../layout/ResponsiveManager";
import { ContainerFrame, VerticalFrame, Button, Label } from "../../../core";

interface ResponsiveTestProps {
  isOpen?: boolean;
  onClose?: () => void;
}

export function ResponsiveTest(props: ResponsiveTestProps) {
  const [screenInfo, setScreenInfo] = React.useState("");
  const responsiveManager = ResponsiveManager.getInstance();

  // Update screen info when component mounts or screen changes
  React.useEffect(() => {
    const updateScreenInfo = () => {
      const screenSize = responsiveManager.getScreenSize();
      const deviceType = responsiveManager.getDeviceType();
      const safeAreaInsets = responsiveManager.getSafeAreaInsets();
      
      const info = [
        `Screen: ${math.floor(screenSize.width)}x${math.floor(screenSize.height)}`,
        `Device: ${deviceType}`,
        `Aspect: ${math.floor(screenSize.aspectRatio * 100) / 100}`,
        `Safe Areas: T:${safeAreaInsets.top} B:${safeAreaInsets.bottom} L:${safeAreaInsets.left} R:${safeAreaInsets.right}`,
        `Mobile: ${responsiveManager.isMobile() ? "Yes" : "No"}`
      ].join("\n");
      
      setScreenInfo(info);
    };

    updateScreenInfo();
    
    // Listen for screen size changes
    const unsubscribe = responsiveManager.onScreenSizeChange(updateScreenInfo);
    
    return unsubscribe;
  }, [responsiveManager]);

  if (!props.isOpen) {
    return <></>;
  }

  return (
    <ContainerFrame
      size={new UDim2(0, 400, 0, 300)}
      position={new UDim2(0.5, 0, 0.5, 0)}
      anchorPoint={new Vector2(0.5, 0.5)}
      responsive={true}
      responsiveMargin={true}
      minSize={new UDim2(0, 300, 0, 250)}
      maxSize={new UDim2(0, 500, 0, 400)}
    >
      <VerticalFrame spacing={12} padding={16} responsive={true} responsiveMargin={true}>
        
        {/* Title */}
        <Label
          text="📱 Responsive Test Panel"
          fontSize={16}
          bold={true}
          autoSize={true}
        />
        
        {/* Screen Info Display */}
        <ContainerFrame
          backgroundColor="#2A2A2A"
          backgroundTransparency={0}
          cornerRadius={8}
          padding={12}
          fitContent={true}
        >
          <Label
            text={screenInfo}
            fontSize={12}
            textWrapped={true}
            autoSize={true}
            size={new UDim2(1, 0, 0, 0)}
          />
        </ContainerFrame>
        
        {/* Test Buttons */}
        <VerticalFrame spacing={8} padding={0} responsive={true}>
          <Button
            text="📏 Responsive Button"
            onClick={() => print("Responsive button clicked")}
            responsive={true}
          />
          
          <Button
            text="📱 Mobile Optimized"
            onClick={() => print("Mobile button clicked")}
            responsive={true}
          />
          
          <Button
            text="🖥️ Desktop Sized"
            onClick={() => print("Desktop button clicked")}
            responsive={false}
          />
        </VerticalFrame>
        
        {/* Close Button */}
        <Button
          text="❌ Close"
          onClick={() => props.onClose?.()}
          responsive={true}
        />
        
      </VerticalFrame>
    </ContainerFrame>
  );
}

// Test function to simulate different screen sizes
export function testResponsiveBehavior() {
  const responsiveManager = ResponsiveManager.getInstance();
  
  print("=== RESPONSIVE BEHAVIOR TEST ===");
  
  // Test screen size calculations
  const screenSize = responsiveManager.getScreenSize();
  print(`Current Screen: ${screenSize.width}x${screenSize.height}`);
  print(`Device Type: ${responsiveManager.getDeviceType()}`);
  print(`Is Mobile: ${responsiveManager.isMobile()}`);
  
  // Test responsive positioning
  const testPosition = responsiveManager.getRelativePosition({ x: 100, y: 50 });
  print(`Relative Position for (100,50): ${testPosition.X.Scale}, ${testPosition.Y.Scale}`);
  
  // Test responsive sizing
  const testSize = responsiveManager.getResponsiveSize(
    { width: 200, height: 100 },
    { width: 150, height: 80 },
    { width: 300, height: 150 }
  );
  print(`Responsive Size for 200x100: ${testSize.X.Scale}, ${testSize.Y.Scale}`);
  
  // Test responsive margins
  const margin = responsiveManager.getResponsiveMargin(16);
  print(`Responsive Margin for 16px: ${margin}px`);
  
  // Test safe area insets
  const safeArea = responsiveManager.getSafeAreaInsets();
  print(`Safe Area Insets: Top:${safeArea.top} Bottom:${safeArea.bottom} Left:${safeArea.left} Right:${safeArea.right}`);
  
  print("=== TEST COMPLETE ===");
}
