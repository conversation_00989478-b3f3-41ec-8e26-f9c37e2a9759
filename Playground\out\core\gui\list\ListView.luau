-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local Label = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "label", "Label").Label
local ListItemButton = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button").ListItemButton
local Image = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "image", "Image").Image
local ScrollingFrame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame").ScrollingFrame
local function ListView(props)
	local _condition = props.width
	if _condition == nil then
		_condition = 200
	end
	local width = _condition
	local _condition_1 = props.height
	if _condition_1 == nil then
		_condition_1 = 300
	end
	local height = _condition_1
	local itemHeight = 40
	local _exp = React.createElement("uilistlayout", {
		Padding = UDim.new(0, 2),
		SortOrder = Enum.SortOrder.LayoutOrder,
	})
	local _exp_1 = props.items
	-- ▼ ReadonlyArray.map ▼
	local _newValue = table.create(#_exp_1)
	local _callback = function(item, index)
		local _value = item.icon
		local _exp_2 = if _value ~= "" and _value then (React.createElement(Image, {
			image = item.icon,
			size = UDim2.new(0, 24, 0, 24),
			position = UDim2.new(0, 8, 0.5, 0),
			anchorPoint = Vector2.new(0, 0.5),
		})) else nil
		local _attributes = {
			text = item.text,
		}
		local _value_1 = item.icon
		_attributes.size = UDim2.new(1, if _value_1 ~= "" and _value_1 then -40 else -16, 1, 0)
		local _value_2 = item.icon
		_attributes.position = UDim2.new(0, if _value_2 ~= "" and _value_2 then 40 else 8, 0, 0)
		return React.createElement(ListItemButton, {
			key = `list-item-{index}`,
			selected = item.selected,
			size = UDim2.new(1, -10, 0, itemHeight),
			layoutOrder = index,
			onClick = function()
				if item.onClick then
					item.onClick()
				end
				if props.onItemSelect then
					props.onItemSelect(index)
				end
			end,
		}, _exp_2, React.createElement(Label, _attributes))
	end
	for _k, _v in _exp_1 do
		_newValue[_k] = _callback(_v, _k - 1, _exp_1)
	end
	-- ▲ ReadonlyArray.map ▲
	return React.createElement(ScrollingFrame, {
		size = UDim2.new(0, width, 0, height),
		canvasSize = UDim2.new(0, 0, 0, 0),
		scrollBarThickness = 6,
	}, _exp, _newValue)
end
return {
	ListView = ListView,
}
