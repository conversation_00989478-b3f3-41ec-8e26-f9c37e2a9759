import * as React from "@rbxts/react";
import { COLORS, TYPOGRAPHY, SIZES } from "../../design";

interface LabelProps {
  text: string;
  textColor?: string;
  fontSize?: number;
  alignment?: Enum.TextXAlignment;
  size?: UDim2;
  position?: UDim2;
  anchorPoint?: Vector2;
  layoutOrder?: number;
  bold?: boolean;
  autoSize?: boolean; // When true, label sizes to fit text content
  textWrapped?: boolean; // Allow text wrapping
}

export function Label(props: LabelProps): React.ReactElement {
  const textColor = props.textColor ?? COLORS.text.main;
  const fontSize = props.fontSize ?? SIZES.fontSize;
  const alignment = props.alignment ?? Enum.TextXAlignment.Left;
  const textWrapped = props.textWrapped ?? false;

  // Smart sizing: use autoSize if specified or no size provided
  const useAutoSize = props.autoSize ?? (props.size === undefined);
  const size = props.size ?? (useAutoSize
    ? (textWrapped ? new UDim2(1, 0, 0, 0) : new UDim2(0, 0, 0, 0)) // For wrapped text, use full width
    : new UDim2(1, 0, 0, fontSize + 4));

  return (
    <textlabel
      Text={props.text}
      TextColor3={Color3.fromHex(textColor)}
      Font={TYPOGRAPHY.font}
      TextSize={fontSize}
      Size={size}
      Position={props.position}
      AnchorPoint={props.anchorPoint}
      LayoutOrder={props.layoutOrder}
      BackgroundTransparency={1}
      TextXAlignment={alignment}
      TextWrapped={textWrapped}
      AutomaticSize={useAutoSize ? Enum.AutomaticSize.XY : Enum.AutomaticSize.None}
      FontFace={props.bold ? new Font(TYPOGRAPHY.font.Name, Enum.FontWeight.Bold) : undefined}
    />
  );
}