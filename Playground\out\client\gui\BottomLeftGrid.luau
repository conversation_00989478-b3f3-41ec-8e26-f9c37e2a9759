-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local Button = _core.Button
local VerticalFrame = _core.VerticalFrame
local WorldTestingPanel = TS.import(script, script.Parent, "WorldTestingPanel").WorldTestingPanel
local DebugPanel = TS.import(script, script.Parent, "DebugPanel").DebugPanel
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local function BottomLeftGrid(_props)
	local worldLabOpen, setWorldLabOpen = React.useState(false)
	local debugPanelOpen, setDebugPanelOpen = React.useState(false)
	-- Get responsive manager for dynamic positioning
	local responsiveManager = ResponsiveManager:getInstance()
	local safeAreaInsets = responsiveManager:getSafeAreaInsets()
	-- Calculate responsive position and size
	local containerWidth = if responsiveManager:isMobile() then 100 else 120
	local containerHeight = if responsiveManager:isMobile() then 160 else 200
	local marginLeft = responsiveManager:getResponsiveMargin(16)
	local marginBottom = responsiveManager:getResponsiveMargin(20) + safeAreaInsets.bottom
	return React.createElement(React.Fragment, nil, React.createElement(VerticalFrame, {
		backgroundTransparency = 1,
		size = UDim2.new(0, containerWidth, 0, containerHeight),
		position = UDim2.new(0, marginLeft, 1, -marginBottom),
		anchorPoint = Vector2.new(0, 1),
		spacing = 8,
		padding = 0,
		responsive = true,
		responsiveMargin = true,
	}, React.createElement(Button, {
		text = "🌍 World",
		onClick = function()
			return setWorldLabOpen(true)
		end,
		LayoutOrder = 5,
	}), React.createElement(Button, {
		text = "🔧 Debug",
		onClick = function()
			return setDebugPanelOpen(true)
		end,
		LayoutOrder = 6,
	})), React.createElement(WorldTestingPanel, {
		isOpen = worldLabOpen,
		onClose = function()
			return setWorldLabOpen(false)
		end,
	}), React.createElement(DebugPanel, {
		isOpen = debugPanelOpen,
		onClose = function()
			return setDebugPanelOpen(false)
		end,
	}))
end
return {
	BottomLeftGrid = BottomLeftGrid,
}
