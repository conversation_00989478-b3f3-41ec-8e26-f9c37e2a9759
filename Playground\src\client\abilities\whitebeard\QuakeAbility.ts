import { AbilityBase } from "../AbilityBase";
import { Players, ReplicatedStorage } from "@rbxts/services";

import { createCameraShake } from "./effects/CameraShake";
import { createMapShockwave } from "./effects/ShockwaveEffects";
import { createQuakeSphere, createSequentialQuakeSpheres } from "./effects/SphereVisuals";
import { executeQuakePunch } from "./animations/PunchExecution";

interface WhitebeardEffectData {
    type: string;
    casterUserId: number;
    punchType: "single" | "double";
    position: Vector3;
    timestamp: number;
}

export class QuakeAbility extends AbilityBase {
    public quakeEffect?: Part;
    public leftQuakeEffect?: Part;
    public quakeConnection?: RBXScriptConnection;
    private cooldownEndTime = 0;
    private effectReplicationEvent?: RemoteEvent;

    constructor() {
        super("QUAKE_PUNCH", 12);
        this.initializeNetworking();
    }

    private initializeNetworking(): void {
        // Wait for the effect replication event created by Core server helpers
        const remoteEventsFolder = ReplicatedStorage.WaitForChild("RemoteEvents") as Folder;
        this.effectReplicationEvent = remoteEventsFolder.WaitForChild("EffectReplication") as RemoteEvent;

        // Listen for effect replications from server
        this.effectReplicationEvent.OnClientEvent.Connect((effectData: unknown) => {
            const data = effectData as WhitebeardEffectData;
            if (data.type === "WHITEBEARD_QUAKE_PUNCH") {
                this.createVisualEffectsFromServer(data);
            }
        });
    }

    public isOnCooldown(): boolean {
        return tick() < this.cooldownEndTime;
    }

    private startCooldown(): void {
        this.cooldownEndTime = tick() + this.getCooldownTime();
    }

    private createVisualEffectsFromServer(effectData: WhitebeardEffectData): void {
        // Get the player who cast the ability
        const caster = Players.GetPlayerByUserId(effectData.casterUserId);
        if (!caster?.Character) return;

        print(`🥊 Creating ${effectData.punchType} quake punch effects for ${caster.Name}`);

        const character = caster.Character;
        const rightHand = character.FindFirstChild("RightHand") as Part;
        if (!rightHand) return;

        // Phase 1: Create sphere(s) based on punch type (same as original)
        if (effectData.punchType === "single") {
            createQuakeSphere(this, character);
        } else {
            createSequentialQuakeSpheres(this, character);
        }

        // Phase 2: Execute punch animation and effects (after 2 seconds like original)
        task.delay(2, () => {
            if (character.Parent) {
                executeQuakePunch(this, character, rightHand, effectData.punchType);

                // Create environmental effects
                const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
                if (humanoidRootPart) {
                    createCameraShake(5, 0.5);
                    createMapShockwave(humanoidRootPart.Position, 200, 2);
                }
            }
        });
    }

    public activate(punchType: "single" | "double" = "single"): void {
        if (this.isOnCooldown()) return;

        const player = Players.LocalPlayer;
        const character = player.Character;
        if (!character) return;

        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (!humanoidRootPart) return;

        print(`🥊 Requesting ${punchType} punch from server for visual sync`);

        // Send request to server to replicate visual effects to all clients
        // The server will handle replication back to ALL clients including this one
        const remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
        const visualSyncEvent = remoteEventsFolder?.FindFirstChild("WHITEBEARD_VISUAL_SYNC") as RemoteEvent;

        if (visualSyncEvent) {
            visualSyncEvent.FireServer({
                request: {
                    punchType: punchType,
                    casterUserId: player.UserId,
                    position: humanoidRootPart.Position,
                    timestamp: tick()
                },
                clientTimestamp: tick(),
                sequenceNumber: 1
            });
        }

        // Start cooldown immediately to prevent spam while waiting for server response
        this.startCooldown();
    }
}