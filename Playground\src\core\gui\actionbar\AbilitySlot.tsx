import * as React from "@rbxts/react";
import { RunService } from "@rbxts/services";
import { COLORS, SIZES, BORDER_RADIUS } from "../../design";
import { Label } from "../label/Label";
import { AbilitySlotData } from "./ActionBar";

interface AbilitySlotProps {
  slotNumber: number;
  abilityData: AbilitySlotData;
  onClick: () => void;
  layoutOrder?: number;
  size?: UDim2;
}

export function AbilitySlot(props: AbilitySlotProps): React.ReactElement {
  const [currentTime, setCurrentTime] = React.useState(tick());
  const [hovered, setHovered] = React.useState(false);

  // Update current time when on cooldown for smooth countdown
  React.useEffect(() => {
    if (!props.abilityData.isOnCooldown || !props.abilityData.cooldownEndTime) {
      return;
    }

    const connection = RunService.Heartbeat.Connect(() => {
      const now = tick();
      if (now >= (props.abilityData.cooldownEndTime ?? 0)) {
        // Cooldown finished, stop updating
        connection.Disconnect();
        return;
      }
      setCurrentTime(now);
    });

    return () => {
      connection.Disconnect();
    };
  }, [props.abilityData.isOnCooldown, props.abilityData.cooldownEndTime]);

  const size = props.size ?? new UDim2(0, 60, 0, 60);
  const isOnCooldown = props.abilityData.isOnCooldown &&
    props.abilityData.cooldownEndTime &&
    currentTime < props.abilityData.cooldownEndTime;

  const remainingTime = isOnCooldown && props.abilityData.cooldownEndTime
    ? math.max(0, props.abilityData.cooldownEndTime - currentTime)
    : 0;

  // Calculate cooldown progress (0 to 1, where 1 is fully on cooldown)
  const cooldownProgress = isOnCooldown && props.abilityData.cooldownEndTime && remainingTime > 0
    ? (() => {
        const totalDuration = props.abilityData.totalCooldownDuration ?? 30; // Default to 30 seconds
        return math.max(0, math.min(1, remainingTime / totalDuration));
      })()
    : 0;

  // Get background color based on state
  const backgroundColor = isOnCooldown 
    ? COLORS.bg.secondary 
    : hovered 
      ? COLORS.bg["surface-hover"] 
      : COLORS.bg.surface;

  return (
    <textbutton
      Text=""
      BackgroundColor3={Color3.fromHex(backgroundColor)}
      Size={size}
      LayoutOrder={props.layoutOrder}
      AutoButtonColor={false}
      BorderSizePixel={0}
      Event={{
        Activated: () => {
          if (!isOnCooldown) {
            props.onClick();
          }
        },
        MouseEnter: () => !isOnCooldown && setHovered(true),
        MouseLeave: () => setHovered(false),
      }}
    >
      <uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />
      
      {/* Border */}
      <uistroke
        Color={Color3.fromHex(isOnCooldown ? COLORS.border.l1 : COLORS.border.l2)}
        Thickness={2}
        Transparency={isOnCooldown ? 0.3 : 0}
      />

      {/* Slot number in top-left corner */}
      <Label
        text={tostring(props.slotNumber)}
        fontSize={10}
        textColor={COLORS.text.secondary}
        position={new UDim2(0, 4, 0, 2)}
        size={new UDim2(0, 15, 0, 12)}
        alignment={Enum.TextXAlignment.Left}
      />

      {/* Ability name/icon in center */}
      <Label
        text={props.abilityData.icon ?? props.abilityData.name.sub(1, 1)}
        fontSize={isOnCooldown ? 12 : 16}
        textColor={isOnCooldown ? COLORS.text.secondary : COLORS.text.main}
        position={new UDim2(0.5, 0, 0.5, 0)}
        anchorPoint={new Vector2(0.5, 0.5)}
        size={new UDim2(0.8, 0, 0.6, 0)}
        alignment={Enum.TextXAlignment.Center}
      />

      {/* Cooldown timer text */}
      {isOnCooldown && remainingTime > 0 && (
        <Label
          text={tostring(math.ceil(remainingTime))}
          fontSize={14}
          textColor={COLORS.warning}
          position={new UDim2(0.5, 0, 0.8, 0)}
          anchorPoint={new Vector2(0.5, 0.5)}
          size={new UDim2(1, 0, 0, 16)}
          alignment={Enum.TextXAlignment.Center}
          bold={true}
        />
      )}

      {/* Cooldown progress overlay */}
      {isOnCooldown && (
        <frame
          BackgroundColor3={Color3.fromHex(COLORS.bg.base)}
          BackgroundTransparency={0.7}
          Size={new UDim2(1, 0, cooldownProgress, 0)}
          Position={new UDim2(0, 0, 0, 0)}
          BorderSizePixel={0}
          ZIndex={2}
        >
          <uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />
        </frame>
      )}
    </textbutton>
  );
}
