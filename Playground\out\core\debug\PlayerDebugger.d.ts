import { DebugRenderer } from "./DebugRenderer";
import { DebugConfig } from "./DebugManager";
export declare class PlayerDebugger {
    private renderer;
    private playerInfoLabel?;
    constructor(renderer: DebugRenderer);
    update(config: DebugConfig): void;
    private setupGUI;
    private updatePlayerInfo;
    private debugPlayer;
    private debugPlayerBodyParts;
    private debugPlayerRelationships;
    private debugPlayerEquipment;
    private debugPlayerAnimations;
    cleanup(): void;
}
