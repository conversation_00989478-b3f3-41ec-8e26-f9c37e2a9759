    client
      main.client.tsx
    core
      index.ts
    server
      main.server.ts
    shared
      module.ts
      abilities
        AbilityBase.ts
        ClientAbilityManager.ts
        FireFistAbility.ts
        HakiDominanceAbility.ts
        IceAgeAbility.ts
        RoomAbility.ts
        ThreeSwordStyleAbility.ts
      gui
        ActionBarDemo.tsx
        BottomLeftGrid.tsx
        ComponentTestingLab.tsx
      movement
        MovementExample.ts
        PlayerMovement.ts
        whitebeard
          QuakeAbility.ts
          animations
            QuakeAnimations.ts
          effects
            QuakeEffects.ts
      animations
        AnimationBuilder.ts
        CharacterJointManager.ts
        LimbAnimator.ts
      design
        index.ts
      effects
        EffectPartBuilder.ts
        EffectTweenBuilder.ts
        ParticleHelper.ts
        SoundHelper.ts
        VisualEffectUtils.ts
      gui
      helper
        utils.ts
        actionbar
          AbilitySlot.tsx
          ActionBar.tsx
          index.ts
        button
          Button.tsx
          IconButton.tsx
          index.ts
          ListItemButton.tsx
        dropdown
          DropdownButton.tsx
          index.ts
        frame
          ContainerFrame.tsx
          HorizontalFrame.tsx
          index.ts
          ScrollingFrame.tsx
          types.ts
          VerticalFrame.tsx
        grid
          Grid.tsx
        image
          Image.tsx
        input
          Input.tsx
        label
          Label.tsx
        list
          ListView.tsx
        modal
          Modal.tsx
        overlay
          Overlay.tsx
      abilities
        ServerAbilityManager.ts
      orbs
        ServerOrbManager.ts
      abilities
        AbilityEvents.ts
        AbilityTypes.ts
