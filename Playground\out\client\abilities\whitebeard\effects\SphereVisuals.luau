-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local EffectPartBuilder = _core.EffectPartBuilder
local FrameAnimationHelper = _core.FrameAnimationHelper
local _WhitebeardPoses = TS.import(script, script.Parent.Parent, "animations", "WhitebeardPoses")
local createKneeBendStance = _WhitebeardPoses.createKneeBendStance
local createArmCrossingPose = _WhitebeardPoses.createArmCrossingPose
local function createQuakeSphere(ability, character)
	local rightHand = character:FindFirstChild("RightHand")
	if not rightHand then
		return nil
	end
	-- Create white quake sphere using Core framework - exact same properties
	ability.quakeEffect = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(0.3, 0.3, 0.3)):color(Color3.fromRGB(255, 255, 255)):material(Enum.Material.ForceField):transparency(0.3):withLight(8, 2, Color3.fromRGB(255, 255, 255)):spawn()
	ability.quakeEffect.Name = "QuakeSphere"
	-- Sphere expansion animation using Core framework - exact same behavior
	ability.quakeConnection = FrameAnimationHelper:createConnection(function(elapsed)
		if ability.quakeEffect and rightHand:IsDescendantOf(game.Workspace) then
			local handPosition = rightHand.Position
			-- Expand from 0.3 to 2.2 over 2 seconds with easing - exact same formula
			local progress = math.min(elapsed / 2, 1)
			local easeProgress = 1 - math.pow(1 - progress, 3)
			local size = 0.3 + (2.2 - 0.3) * easeProgress
			-- No pulsing - steady growth - exact same as original
			ability.quakeEffect.Size = Vector3.new(size, size, size)
			ability.quakeEffect.CFrame = CFrame.new(handPosition)
			-- Increase intensity as it grows - exact same formula as original
			local light = ability.quakeEffect:FindFirstChild("PointLight")
			if light then
				light.Brightness = 2 + (progress * 4)
			end
		end
	end)
end
local function createSequentialQuakeSpheres(ability, character)
	local rightHand = character:FindFirstChild("RightHand")
	local leftHand = character:FindFirstChild("LeftHand")
	if not rightHand or not leftHand then
		return nil
	end
	-- ENHANCED CROSS PUNCH ANIMATION SEQUENCE
	print("🥊 Starting enhanced cross punch animation with knee bend and arm crossing")
	-- Phase 1: Knee bend and stance preparation (0.3s)
	createKneeBendStance(character)
	-- Phase 2: Cross arms and create spheres (0.5s delay)
	task.delay(0.3, function()
		createArmCrossingPose(character)
		-- Create LEFT hand sphere using Core framework - exact same properties
		ability.leftQuakeEffect = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(0.2, 0.2, 0.2)):color(Color3.fromRGB(255, 255, 255)):material(Enum.Material.ForceField):transparency(0.4):withLight(6, 1, Color3.fromRGB(255, 255, 255)):spawn()
		ability.leftQuakeEffect.Name = "LeftQuakeSphere"
	end)
	-- Phase 3: Create RIGHT hand sphere during arm crossing (0.8s delay for right punch timing)
	task.delay(0.8, function()
		if rightHand:IsDescendantOf(game.Workspace) then
			print("🥊 Creating right hand Whitebeard sphere")
			-- Create RIGHT hand sphere using Core framework - exact same properties
			ability.quakeEffect = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(0.2, 0.2, 0.2)):color(Color3.fromRGB(255, 255, 255)):material(Enum.Material.ForceField):transparency(0.4):withLight(6, 1, Color3.fromRGB(255, 255, 255)):spawn()
			ability.quakeEffect.Name = "RightQuakeSphere"
		end
	end)
	-- Sequential sphere expansion animation using Core framework - exact same behavior
	ability.quakeConnection = FrameAnimationHelper:createConnection(function(elapsed)
		if leftHand:IsDescendantOf(game.Workspace) then
			local leftHandPosition = leftHand.Position
			-- Expand left sphere first - exact same formula as original
			if ability.leftQuakeEffect then
				local progress = math.min(elapsed / 2, 1)
				local easeProgress = 1 - math.pow(1 - progress, 3)
				local size = 0.2 + (3.5 - 0.2) * easeProgress
				ability.leftQuakeEffect.Size = Vector3.new(size, size, size)
				ability.leftQuakeEffect.CFrame = CFrame.new(leftHandPosition)
				-- Increase left sphere intensity - exact same formula as original
				local leftLight = ability.leftQuakeEffect:FindFirstChild("PointLight")
				if leftLight then
					leftLight.Brightness = 2 + (progress * 4)
				end
			end
			-- Expand right sphere after 0.5s delay - exact same logic as original
			if ability.quakeEffect and elapsed > 0.5 and rightHand:IsDescendantOf(game.Workspace) then
				local rightHandPosition = rightHand.Position
				local rightProgress = math.min((elapsed - 0.5) / 2, 1)
				local rightEaseProgress = 1 - math.pow(1 - rightProgress, 3)
				local rightSize = 0.2 + (3.5 - 0.2) * rightEaseProgress
				ability.quakeEffect.Size = Vector3.new(rightSize, rightSize, rightSize)
				ability.quakeEffect.CFrame = CFrame.new(rightHandPosition)
				-- Increase right sphere intensity - exact same formula as original
				local rightLight = ability.quakeEffect:FindFirstChild("PointLight")
				if rightLight then
					rightLight.Brightness = 2 + (rightProgress * 4)
				end
			end
		end
	end)
end
return {
	createQuakeSphere = createQuakeSphere,
	createSequentialQuakeSpheres = createSequentialQuakeSpheres,
}
